# API调用方式文档

本项目有两种主要的API调用方式，根据后端实现情况选择使用：

## 1. doCreateNew 方式（有Java后端Controller）

### 适用场景
- 已经编写了Java后端Controller
- 需要复杂的业务逻辑处理
- 需要数据验证和转换
- 需要事务管理

### 调用方式

#### 前端调用（正确方式）
```typescript
// 使用 useInfo hook 创建API服务
import { useInfo } from '@/hooks/web/useRestAPI';

// 创建API服务实例
const apiService = useInfo({ rootPath: '/graph-rest-api' });

// 设置请求数据到 info.value 中
apiService.info.value = {
  startDate: '2025-01-01 00:00:00',
  endDate: '2025-12-31 23:59:59',
  areaCode: 'nj'
};

// 调用 doCreateNew 方法
const result = await apiService.doCreateNew('/api/dual-route-statistics/monthly');
```

#### 前端调用（错误方式 - 不要使用）
```typescript
// ❌ 错误：直接使用 defHttp.post
import { defHttp } from '@/utils/http/axios';

const result = await defHttp.post({
  url: '/graph-rest-api/api/dual-route-statistics/monthly',
  data: requestData
}, { isTransformResponse: false });
```

#### 后端实现
```java
// Controller层
@RestController
@RequestMapping("/api/dual-route-statistics")
public class DualRouteStatisticsController {

    @PostMapping("/monthly")
    @LogAnnotation(interfaceName = "双路由月度运营统计查询")
    public ResponseEntity<JSONObject> getMonthlyStatistics(@RequestBody JSONObject request) {
        try {
            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);

            // 调用服务层
            JSONObject result = dualRouteStatisticsService.getMonthlyStatistics(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }
}

// Service层
@Service
public class DualRouteStatisticsServiceImpl {
    
    @Autowired
    private DualRouteStatisticsDao dao;
    
    public JSONObject getMonthlyStatistics(JSONObject param) {
        // 调用DAO层
        return dao.getDualRouteMonthlyStatistics(param, shardingCode);
    }
}

// DAO层
@DaClientProxy
public interface DualRouteStatisticsDao {
    
    @DaAPI(apiCode = "dual_route_monthly_statistics", version = "V20250617090933641", returnTypes = {JSONObject.class})
    JSONObject getDualRouteMonthlyStatistics(@DaParam JSONObject param, @DaShardingCode String shardingCode);
}
```

### 调用链路
```
前端 defHttp.post()
  ↓ HTTP POST
后端 Controller (@PostMapping + @RequestBody)
  ↓
Service层
  ↓
DAO层 (@DaAPI)
  ↓
DataQL脚本
```

## 2. callUnifiedApi 方式（无Java后端Controller）

### 适用场景
- 没有编写Java后端Controller
- 简单的数据查询
- 直接调用DataQL脚本
- 快速原型开发

### 调用方式

#### 前端调用
```typescript
import { callUnifiedApi, buildShardingCode } from '../unified-api';

// 直接调用DataQL接口
export const queryProtectionGroupDevices = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_PROTECTION_GROUP_DEVICES_QUERY',
    version: 'V20250419121012578',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};
```

#### 后端实现
```java
// 只需要统一的ApiConvergeController（已存在）
@RestController
@RequestMapping("/api/apiconverge")
public class ApiConvergeController {
    
    @PostMapping("query")
    public JSONObject queryController(@RequestBody JSONObject request) {
        JSONObject result = apiService.executeApi(request, JSONObject.class);
        return result;
    }
}

// UnifiedApiService会直接调用DataQL
@Service
public class UnifiedApiServiceImpl {
    public <T> T executeApi(JSONObject request, Class<T> responseType) {
        // 直接构建DaRequest调用DataQL
        DaRequest<String> daRequest = daClient.buildRequest()
            .apiCode(apiCode)
            .version(version)
            .shardingCode(shardingCode)
            .param(params)
            .build();
        return daRequest.doApi();
    }
}
```

### 调用链路
```
前端 callUnifiedApi() 
  ↓ HTTP POST
ApiConvergeController 
  ↓
UnifiedApiService 
  ↓
DaClient 直接调用 
  ↓
DataQL脚本
```

## 3. 参数格式对比

### doCreateNew方式（正确）
```typescript
// 使用 useInfo hook 创建API服务
import { useInfo } from '@/hooks/web/useRestAPI';

const apiService = useInfo({ rootPath: '/graph-rest-api' });

// 设置业务参数到 info.value 中
apiService.info.value = {
  startDate: '2025-01-01 00:00:00',
  endDate: '2025-12-31 23:59:59',
  areaCode: 'nj'
};

// 调用 doCreateNew 方法
const result = await apiService.doCreateNew('/api/dual-route-statistics/monthly');

// 后端Controller接收并处理
@PostMapping("/monthly")
public ResponseEntity<JSONObject> getMonthlyStatistics(@RequestBody JSONObject request) {
    // request 直接包含业务参数
    String shardingCode = extractShardingCode(request);
    request.put("shardingCode", shardingCode);
}
```

### callUnifiedApi方式
```typescript
// 前端需要包装成统一格式
const request = {
  apiCode: 'dual_route_monthly_statistics',
  version: 'V20250617090933641',
  shardingCode: 'ds_bc_o3_nj',
  param: {
    startDate: '2025-01-01',
    endDate: '2025-12-31'
  }
};
```

## 4. 选择建议

### 使用 doCreateNew 的情况：
- ✅ 需要复杂业务逻辑
- ✅ 需要参数验证和转换
- ✅ 需要聚合多个DataQL接口
- ✅ 需要事务管理
- ✅ 需要统一的错误处理

### 使用 callUnifiedApi 的情况：
- ✅ 简单的数据查询
- ✅ 快速开发原型
- ✅ 一对一调用DataQL接口
- ✅ 不需要复杂业务逻辑

## 5. 最佳实践

1. **统计分析类接口**：建议使用 doCreateNew，因为通常需要数据聚合和计算
2. **简单查询接口**：可以使用 callUnifiedApi
3. **分库查询**：建议使用 doCreateNew，便于处理分库逻辑
4. **实时查询**：可以使用 callUnifiedApi，减少中间层开销

## 6. 注意事项

- doCreateNew 方式需要编写完整的 Controller → Service → DAO 链路
- callUnifiedApi 方式需要确保 DataQL 脚本已正确注册
- 两种方式的错误处理机制不同
- 分库代码的处理方式不同
