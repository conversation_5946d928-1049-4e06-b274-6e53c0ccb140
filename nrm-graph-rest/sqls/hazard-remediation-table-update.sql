-- 隐患整改主表字段更新脚本
-- 添加完成相关字段和检测关联字段

-- 1. 添加完成相关字段（用户填写）
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS completion_description TEXT;
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS user_remaining_issues TEXT;
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS exemption_reason TEXT;
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS completion_operator VARCHAR(100);
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS completion_time TIMESTAMP;

-- 2. 添加检测关联字段
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS latest_detection_id NUMERIC(24);
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS initial_detection_id NUMERIC(24);
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS completion_detection_id NUMERIC(24);
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS detection_count INTEGER DEFAULT 0;
ALTER TABLE hazard_remediation ADD COLUMN IF NOT EXISTS has_remaining_issues BOOLEAN DEFAULT FALSE;

-- 3. 添加字段注释
COMMENT ON COLUMN hazard_remediation.completion_description IS '完成说明（用户填写）';
COMMENT ON COLUMN hazard_remediation.user_remaining_issues IS '用户填写的遗留问题描述';
COMMENT ON COLUMN hazard_remediation.exemption_reason IS '豁免理由（用户填写）';
COMMENT ON COLUMN hazard_remediation.completion_operator IS '完成操作人';
COMMENT ON COLUMN hazard_remediation.completion_time IS '完成操作时间';
COMMENT ON COLUMN hazard_remediation.latest_detection_id IS '最后一次检测记录ID';
COMMENT ON COLUMN hazard_remediation.initial_detection_id IS '初始检测记录ID';
COMMENT ON COLUMN hazard_remediation.completion_detection_id IS '完成检测记录ID';
COMMENT ON COLUMN hazard_remediation.detection_count IS '检测次数';
COMMENT ON COLUMN hazard_remediation.has_remaining_issues IS '是否有遗留问题（基于检测结果）';

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_hazard_remediation_latest_detection_id ON hazard_remediation(latest_detection_id);
CREATE INDEX IF NOT EXISTS idx_hazard_remediation_completion_time ON hazard_remediation(completion_time);
CREATE INDEX IF NOT EXISTS idx_hazard_remediation_has_remaining_issues ON hazard_remediation(has_remaining_issues);

-- 5. 更新现有数据的默认值
UPDATE hazard_remediation 
SET detection_count = 0, has_remaining_issues = FALSE 
WHERE detection_count IS NULL OR has_remaining_issues IS NULL;
