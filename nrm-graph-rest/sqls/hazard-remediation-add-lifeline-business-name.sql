-- 添加生命线业务名称字段到隐患整改表
-- 执行时间：2024-01-XX
-- 说明：修复生命线业务名称字段缺失问题

-- 检查字段是否已存在
DO $$
BEGIN
    -- 检查 lifeline_business_name 字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'hazard_remediation' 
        AND column_name = 'lifeline_business_name'
    ) THEN
        -- 添加生命线业务名称字段
        ALTER TABLE hazard_remediation 
        ADD COLUMN lifeline_business_name VARCHAR(500);
        
        -- 添加字段注释
        COMMENT ON COLUMN hazard_remediation.lifeline_business_name IS '生命线业务名称';
        
        RAISE NOTICE '已添加 lifeline_business_name 字段到 hazard_remediation 表';
    ELSE
        RAISE NOTICE 'lifeline_business_name 字段已存在，跳过添加';
    END IF;
END $$;

-- 验证字段是否添加成功
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'hazard_remediation' 
AND column_name = 'lifeline_business_name';
