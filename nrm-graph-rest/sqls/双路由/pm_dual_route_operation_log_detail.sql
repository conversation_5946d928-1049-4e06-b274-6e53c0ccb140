// pm_dual_route_operation_log_detail.sql
// 双路由管理操作日志详情查询API - 简化版本
// limit 1

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};


// 直接查询，所有业务逻辑在Java层处理
var queryFun = @@sql(p)<%
    SELECT
        id,
        operation_time,
        operator_id,
        operator_name,
        operator_ip,
        session_id,
        object_type,
        object_id,
        object_code,
        object_name,
        operation_type,
        operation_module,
        operation_description,
        area_code,
        area_name,
        speciality,
        before_data,
        after_data,
        changed_fields,
        operation_result,
        error_message,
        request_params,
        response_data,
        execution_time_ms,
        create_time,
        remark
    FROM pm_dual_route_operation_log
    WHERE id = #{p.id}::numeric
%>;

var result = queryFun(p);

return
{
    "success": true,
    "message": "双路由管理操作日志详情查询成功",
    "data": result
};


//limit 1