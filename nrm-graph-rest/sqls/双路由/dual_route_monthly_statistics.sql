// dual_route_monthly_statistics.sql
// 双路由光路检测月度运营统计查询

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 月度统计主查询
var monthlyStatsFun = @@sql(p)<%
    WITH monthly_status_changes AS (
        SELECT 
            pc.object_id,
            pc.check_result as current_status,
            pc.previous_check_result as previous_status,
            pc.create_time,
            pc.check_time,
            grp.code,
            grp.name,
            grp.area_name,
            -- 状态分类
            CASE 
                WHEN pc.check_result IN ('缺管道', '无光路', '异常') THEN 'severe_problem'
                WHEN pc.check_result IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'general_problem'
                WHEN pc.check_result = '正常' THEN 'normal'
                ELSE 'unknown'
            END as current_status_type,
            CASE 
                WHEN pc.previous_check_result IN ('缺管道', '无光路', '异常') THEN 'severe_problem'
                WHEN pc.previous_check_result IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'general_problem'
                WHEN pc.previous_check_result = '正常' THEN 'normal'
                WHEN pc.previous_check_result IS NULL THEN 'first_check'
                ELSE 'unknown'
            END as previous_status_type
        FROM pm_risk_check pc
        INNER JOIN pm_opt_road_group grp ON pc.object_id = grp.id
        WHERE grp.source_type_id = '0'
            AND pc.create_time >= #{p.startDate}::timestamp
            AND pc.create_time <= #{p.endDate}::timestamp
            AND pc.check_result IS NOT NULL
            -- 通过shardingCode已经定位到具体分库，查询该库下所有数据
    ),
    
    status_change_classification AS (
        SELECT 
            *,
            -- 分类业务指标
            CASE 
                -- 整改数量：问题状态 -> 正常
                WHEN previous_status_type IN ('severe_problem', 'general_problem') 
                     AND current_status_type = 'normal' THEN 'remediation'
                
                -- 变好数量：严重问题 -> 一般问题 或 一般问题 -> 正常
                WHEN previous_status_type = 'severe_problem' 
                     AND current_status_type = 'general_problem' THEN 'improvement'
                WHEN previous_status_type = 'general_problem' 
                     AND current_status_type = 'normal' THEN 'improvement'
                
                -- 变差数量：正常 -> 问题状态 或 一般问题 -> 严重问题
                WHEN previous_status_type = 'normal' 
                     AND current_status_type IN ('severe_problem', 'general_problem') THEN 'deterioration'
                WHEN previous_status_type = 'general_problem' 
                     AND current_status_type = 'severe_problem' THEN 'deterioration'
                
                -- 确认数量：状态无变化或首次检测
                WHEN previous_status_type = current_status_type THEN 'confirmation'
                WHEN previous_status_type = 'first_check' THEN 'confirmation'
                
                ELSE 'other'
            END as metric_type
        FROM monthly_status_changes
    )
    
    SELECT 
        DATE_TRUNC('month', create_time) as stat_month,
        area_name,
        COUNT(CASE WHEN metric_type = 'remediation' THEN 1 END) as remediation_count,
        COUNT(CASE WHEN metric_type = 'confirmation' THEN 1 END) as confirmation_count,
        COUNT(CASE WHEN metric_type = 'improvement' THEN 1 END) as improvement_count,
        COUNT(CASE WHEN metric_type = 'deterioration' THEN 1 END) as deterioration_count,
        COUNT(*) as total_checks,
        
        -- 计算比例
        ROUND(COUNT(CASE WHEN metric_type = 'remediation' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as remediation_rate,
        ROUND(COUNT(CASE WHEN metric_type = 'improvement' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as improvement_rate,
        ROUND(COUNT(CASE WHEN metric_type = 'deterioration' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as deterioration_rate
        
    FROM status_change_classification
    GROUP BY DATE_TRUNC('month', create_time), area_name
    ORDER BY stat_month DESC, area_name
    LIMIT 100
%>;

// 状态分布统计
var statusDistributionFun = @@sql(p)<%
    SELECT 
        pc.check_result as status,
        COUNT(*) as count,
        COUNT(DISTINCT pc.object_id) as unique_objects,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM pm_risk_check pc2
                                  INNER JOIN pm_opt_road_group grp2 ON pc2.object_id = grp2.id
                                  WHERE grp2.source_type_id = '0'
                                  AND pc2.create_time >= #{p.startDate}::timestamp
                                  AND pc2.create_time <= #{p.endDate}::timestamp
                                  -- 查询当前分库下的所有数据
                                 ), 2) as percentage
    FROM pm_risk_check pc
    INNER JOIN pm_opt_road_group grp ON pc.object_id = grp.id
    WHERE grp.source_type_id = '0'
        AND pc.create_time >= #{p.startDate}::timestamp
        AND pc.create_time <= #{p.endDate}::timestamp
        AND pc.check_result IS NOT NULL
        -- 查询当前分库下的所有数据
    GROUP BY pc.check_result
    ORDER BY count DESC
    LIMIT 20
%>;

// 地区统计
var areaStatsFun = @@sql(p)<%
    SELECT 
        grp.area_name,
        COUNT(*) as total_checks,
        COUNT(CASE WHEN pc.check_result = '正常' THEN 1 END) as normal_count,
        COUNT(CASE WHEN pc.check_result != '正常' THEN 1 END) as problem_count,
        ROUND(COUNT(CASE WHEN pc.check_result = '正常' THEN 1 END) * 100.0 / COUNT(*), 2) as normal_rate
    FROM pm_risk_check pc
    INNER JOIN pm_opt_road_group grp ON pc.object_id = grp.id
    WHERE grp.source_type_id = '0'
        AND pc.create_time >= #{p.startDate}::timestamp
        AND pc.create_time <= #{p.endDate}::timestamp
        AND pc.check_result IS NOT NULL
    GROUP BY grp.area_name
    ORDER BY total_checks DESC
    LIMIT 50
%>;

// 执行查询
var monthlyStats = monthlyStatsFun(${param});
var statusDistribution = statusDistributionFun(${param});
var areaStats = areaStatsFun(${param});


return {
    "success": true,
    "message": "双路由月度统计查询成功",
    "data": {
        "queryParams": {
            "startDate": startDate,
            "endDate": endDate
        },
        "monthlyStats": monthlyStats,
        "statusDistribution": statusDistribution,
        "areaStats": areaStats
    }
};

//limit 1000