-- 双路由光路检测运营数据分析模块
-- 用于生成月度运营统计数据：整改数量、确认数量、变好数量、变差数量

-- =====================================================
-- 1. 状态变化分类逻辑定义
-- =====================================================

-- 问题状态定义
-- 严重问题：缺管道、无光路、异常
-- 一般问题：同管道、同光缆、局内光路、同局前井
-- 正常状态：正常

-- 业务指标分类逻辑：
-- 1. 整改数量：问题状态 -> 正常状态
-- 2. 确认数量：状态无变化（包括首次检测）
-- 3. 变好数量：严重问题 -> 一般问题，或 一般问题 -> 正常
-- 4. 变差数量：正常 -> 问题状态，或 一般问题 -> 严重问题

-- =====================================================
-- 2. 月度统计基础查询
-- =====================================================

-- 获取指定月份的状态变化数据
WITH monthly_status_changes AS (
    SELECT 
        pc.object_id,
        pc.check_result as current_status,
        pc.previous_check_result as previous_status,
        pc.create_time,
        pc.check_time,
        grp.group_code,
        grp.group_name,
        grp.area_code,
        -- 状态分类
        CASE 
            WHEN pc.check_result IN ('缺管道', '无光路', '异常') THEN 'severe_problem'
            WHEN pc.check_result IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'general_problem'
            WHEN pc.check_result = '正常' THEN 'normal'
            ELSE 'unknown'
        END as current_status_type,
        CASE 
            WHEN pc.previous_check_result IN ('缺管道', '无光路', '异常') THEN 'severe_problem'
            WHEN pc.previous_check_result IN ('同管道', '同光缆', '局内光路', '同局前井') THEN 'general_problem'
            WHEN pc.previous_check_result = '正常' THEN 'normal'
            WHEN pc.previous_check_result IS NULL THEN 'first_check'
            ELSE 'unknown'
        END as previous_status_type
    FROM pm_risk_check pc
    INNER JOIN pm_opt_road_group grp ON pc.object_id = grp.id
    WHERE grp.source_type_id = '0'
        AND pc.create_time >= :start_date
        AND pc.create_time < :end_date
        AND pc.check_result IS NOT NULL
),

-- =====================================================
-- 3. 业务指标计算
-- =====================================================

status_change_classification AS (
    SELECT 
        *,
        -- 分类业务指标
        CASE 
            -- 整改数量：问题状态 -> 正常
            WHEN previous_status_type IN ('severe_problem', 'general_problem') 
                 AND current_status_type = 'normal' THEN 'remediation'
            
            -- 变好数量：严重问题 -> 一般问题 或 一般问题 -> 正常
            WHEN previous_status_type = 'severe_problem' 
                 AND current_status_type = 'general_problem' THEN 'improvement'
            WHEN previous_status_type = 'general_problem' 
                 AND current_status_type = 'normal' THEN 'improvement'
            
            -- 变差数量：正常 -> 问题状态 或 一般问题 -> 严重问题
            WHEN previous_status_type = 'normal' 
                 AND current_status_type IN ('severe_problem', 'general_problem') THEN 'deterioration'
            WHEN previous_status_type = 'general_problem' 
                 AND current_status_type = 'severe_problem' THEN 'deterioration'
            
            -- 确认数量：状态无变化或首次检测
            WHEN previous_status_type = current_status_type THEN 'confirmation'
            WHEN previous_status_type = 'first_check' THEN 'confirmation'
            
            ELSE 'other'
        END as metric_type
    FROM monthly_status_changes
)

-- =====================================================
-- 4. 月度统计汇总
-- =====================================================

SELECT 
    DATE_TRUNC('month', create_time) as stat_month,
    area_code,
    COUNT(CASE WHEN metric_type = 'remediation' THEN 1 END) as remediation_count,
    COUNT(CASE WHEN metric_type = 'confirmation' THEN 1 END) as confirmation_count,
    COUNT(CASE WHEN metric_type = 'improvement' THEN 1 END) as improvement_count,
    COUNT(CASE WHEN metric_type = 'deterioration' THEN 1 END) as deterioration_count,
    COUNT(*) as total_checks,
    
    -- 计算比例
    ROUND(COUNT(CASE WHEN metric_type = 'remediation' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as remediation_rate,
    ROUND(COUNT(CASE WHEN metric_type = 'improvement' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as improvement_rate,
    ROUND(COUNT(CASE WHEN metric_type = 'deterioration' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as deterioration_rate
    
FROM status_change_classification
GROUP BY DATE_TRUNC('month', create_time), area_code
ORDER BY stat_month DESC, area_code;

-- =====================================================
-- 5. 详细状态变化明细查询
-- =====================================================

-- 用于查看具体的状态变化详情
/*
SELECT 
    object_id,
    group_code,
    group_name,
    area_code,
    previous_status,
    current_status,
    metric_type,
    create_time,
    check_time
FROM status_change_classification
WHERE metric_type = :metric_type  -- 'remediation', 'confirmation', 'improvement', 'deterioration'
    AND DATE_TRUNC('month', create_time) = :target_month
ORDER BY create_time DESC;
*/
