// pm_dual_route_operation_log_insert.sql
// 双路由管理操作日志插入API - 简化版本
// limit 1

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.operator_id) || collect.isEmpty(p.operation_type)) {
    return 0;
}

// 直接插入，所有业务逻辑在Java层处理
var insertFun = @@sql(p)<%
    INSERT INTO pm_dual_route_operation_log (
        operator_id,
        operator_name,
        operator_ip,
        session_id,
        object_type,
        object_id,
        object_code,
        object_name,
        operation_type,
        operation_module,
        operation_description,
        area_code,
        area_name,
        speciality,
        before_data,
        after_data,
        changed_fields,
        operation_result,
        error_message,
        request_params,
        response_data,
        execution_time_ms,
        remark
    ) VALUES (
        #{p.operator_id},
        #{p.operator_name},
        #{p.operator_ip},
        #{p.session_id},
        #{p.object_type},
        #{p.object_id}::numeric,
        #{p.object_code},
        #{p.object_name},
        #{p.operation_type},
        #{p.operation_module},
        #{p.operation_description},
        #{p.area_code},
        #{p.area_name},
        #{p.speciality},
        #{p.before_data},
        #{p.after_data},
        #{p.changed_fields},
        #{p.operation_result},
        #{p.error_message},
        #{p.request_params},
        #{p.response_data},
        #{p.execution_time_ms}::integer,
        #{p.remark}
    )
%>;

return insertFun(p);

//limit 1
