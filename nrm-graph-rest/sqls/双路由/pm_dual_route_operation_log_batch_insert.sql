// pm_dual_route_operation_log_batch_insert.sql
// 双路由管理操作日志批量插入API - 简化版本
// limit 1

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var p = ${param};

// 基本参数验证
if (collect.isEmpty(p) || collect.isEmpty(p.list)) {
    return 0;
}

var logList = p.list;
var successCount = 0;

// 简单的批量插入，所有业务逻辑在Java层处理
for (var i = 0; i < logList.size(); i++) {
    var log = logList[i];

    // 基本验证
    if (collect.isEmpty(log.operator_id) || collect.isEmpty(log.operation_type)) {
        continue;
    }

    try {
        var insertFun = @@sql(log)<%
            INSERT INTO pm_dual_route_operation_log (
                operator_id,
                operator_name,
                operator_ip,
                session_id,
                object_type,
                object_id,
                object_code,
                object_name,
                operation_type,
                operation_module,
                operation_description,
                area_code,
                area_name,
                speciality,
                before_data,
                after_data,
                changed_fields,
                operation_result,
                error_message,
                request_params,
                response_data,
                execution_time_ms,
                remark
            ) VALUES (
                #{log.operator_id},
                #{log.operator_name},
                #{log.operator_ip},
                #{log.session_id},
                #{log.object_type},
                #{log.object_id}::numeric,
                #{log.object_code},
                #{log.object_name},
                #{log.operation_type},
                #{log.operation_module},
                #{log.operation_description},
                #{log.area_code},
                #{log.area_name},
                #{log.speciality},
                #{log.before_data},
                #{log.after_data},
                #{log.changed_fields},
                #{log.operation_result},
                #{log.error_message},
                #{log.request_params},
                #{log.response_data},
                #{log.execution_time_ms}::integer,
                #{log.remark}
            )
        %>;

        var result = insertFun(log);
        if (result > 0) {
            run successCount++;
        }

    } catch (e) {
        // 忽略单条插入失败，继续处理下一条
    }
}

return successCount;

//limit 1
