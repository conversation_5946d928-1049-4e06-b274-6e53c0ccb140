// pm_busi_user_scene_member_graph.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select a_node_name,z_node_name,count(circuit_code) circuit_count,sum(case when service_type_name='虚拟电路' then 1 else 0 end) virtual_num from pm_busi_user_scene_member where scene_id=#{p.scene_id}::numeric group by a_node_name,z_node_name
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return {
"data": data,
"pageInfo": pageInfo
};

// ===================================================================================