hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var saveFun = @@sql(p)<%
INSERT INTO pm_busi_user_scene_member
(id,scene_id,service_id,area_id,area_name,service_type_id,service_type_name,a_customer_name,z_customer_name,customer_name,access_code,a_access_code,z_access_code,circuit_code,address_id,address_name,a_device_type_name,a_device_id,a_device_name,a_device_code,z_device_type_name,z_device_id,z_device_name,z_device_code,a_address_name,a_address_id,z_address_name,z_address_id,a_node_name,z_node_name) 
VALUES 
(
#{p.id}::numeric, 
#{p.scene_id}::numeric,
#{p.service_id}::numeric,
#{p.area_id}::numeric,
#{p.area_name},
#{p.service_type_id}::numeric,
#{p.service_type_name},
#{p.a_customer_name},
#{p.z_customer_name},
#{p.customer_name},
#{p.access_code},
#{p.a_access_code},
#{p.z_access_code},
#{p.circuit_code},
#{p.address_id}::numeric,
#{p.address_name},
#{p.a_device_type_name},
#{p.a_device_id}::numeric,
#{p.a_device_name},
#{p.a_device_code},
#{p.z_device_type_name},
#{p.z_device_id}::numeric,
#{p.z_device_name},
#{p.z_device_code},
#{p.a_address_name},
#{p.a_address_id}::numeric,
#{p.z_address_name},
#{p.z_address_id}::numeric,
#{p.a_node_name},
#{p.z_node_name}
) ON CONFLICT (area_id,circuit_code,scene_id) do update set area_name=#{p.area_name}, area_id=#{p.area_id}::numeric,service_type_id=#{p.service_type_id}::numeric,service_type_name=#{p.service_type_name},customer_name=#{p.customer_name},a_customer_name=#{p.a_customer_name},service_id=#{p.service_id}::numeric
%>;

var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_busi_user_scene_member',24,'0' )
		run newMap.put('id',newId.shortId);
	}
    return newMap.data()
};

var p = generateId(${param});
var result =  saveFun(p);
return {
	"result": result
};