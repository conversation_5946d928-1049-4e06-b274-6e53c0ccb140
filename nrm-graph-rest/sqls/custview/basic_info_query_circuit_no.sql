hint FRAGMENT_SQL_COLUMN_CASE = "lower";

var queryFun = @@mybatis(p)<%
    <select>
select 
rs.id id,
rarea.name area_name, 
pprt.res_type_id service_type_id,
pprt.res_type service_type_name, 
rc.name customer_name, 
rs.access_code access_code, 
cl.code circuit_code, 
ra.name address_name, 
prt1.res_type a_device_type, 
a_ra.name a_address_name, 
prt1.res_type_id a_device_type_id,
prt2.res_type_id a_device_type_id,
prt2.res_type z_device_type, 
z_ra.name z_addr_name, 
a_ra.id a_address_id, 
z_ra.id z_address_id, 
ra.id address_id
from cm_link cl
inner join rr_service_entity rse on rse.entity_id = cl.id and rse.entity_spec_id=cl.spec_id
inner join  rm_service rs on  rs.id = rse.service_id 
inner join rm_area rarea on rs.sharding_id = rarea.id
inner join pm_pub_res_type pprt on rs.spec_id = pprt.res_type_id and rs.spec_id in (2840,2217,3933,2580,2089,2782,3861,2105,2464,4390,2733,3153,2082,2851,2839,2083,2124,2176,2842,2915,2706,2834,2716,2118,2783,700001221,
2462,2693,2473,2805,9310192086,2784)
inner join rm_customer rc on rs.a_customer_id = rc.id 
inner join rm_address ra on rs.a_address_id = ra.id
inner join cm_device cd1 on cl.a_physic_device_id = cd1.id
inner join cm_device cd2 on cl.z_physic_device_id = cd2.id
left join rm_address a_ra on cd1.address_id = a_ra.id
left join rm_address z_ra on cd2.address_id = z_ra.id
inner join pm_pub_res_type prt1 on cd1.spec_id = prt1.res_type_id
inner join pm_pub_res_type prt2 on cd2.spec_id = prt2.res_type_id
where 1=1 
        <if test="p.circuit_code!= null and p.circuit_code!= ''">
        	and cl.code like '%'||#{p.circuit_code}||'%'
        </if>
limit 100
</select>
%>;

var data = queryFun(${param});
return data