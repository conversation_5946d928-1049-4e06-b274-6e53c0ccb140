DROP TABLE IF EXISTS gl_group_data;--SELECT * FROM gl_group_data
CREATE TABLE gl_group_data AS
SELECT  DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code, device_id lgc_eqp_id,device_code lgc_eqp_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'单设备'eqp_spec from sly_device_gl_info  
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,1 lgc_eqp_id,b_pair lgc_eqp_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'设备对'eqp_spec  from sly_pair_gl_info 
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,1,v_no lgc_eqp_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'dcsw'eqp_spec  from TMP_DCSW_PHY_GL1 
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,1,b1_ip||'-'||b2_ip  lgc_eqp_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'A环'eqp_spec from TMP_NOC_gl_A1 where gl_code !='同机房检测' 
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,device_id,device_code  lgc_eqp_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'cn2'eqp_spec from SLY_CN_BASE_INFO
where spec_no='CN2'
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,T.TRANS_SYSTEM_ID,T1.TRANS_NO  lgc_eqp_no,T.GL_NO gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'SDH'eqp_spec from TMP_SLY_SDHTRANS_2_GL T JOIN TMP_SLY_SDHTRANS_INFO T1 ON T.TRANS_SYSTEM_ID=T1.TRANS_SYSTEM_ID
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,T.TRANS_SYSTEM_ID,T.TRANS_NO  lgc_eqp_no,T.GL_NO gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'DWDM'eqp_spec from MID_XHJ_TRANS_DWDM_GL T ;

--select * from cbl_gl_group_data t ;
DROP TABLE IF EXISTS  cbl_gl_group_data;
CREATE TABLE cbl_gl_group_data AS
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code ,cs_code section_no,gl_code gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_date,'单设备'eqp_spec from sly_dwdm_gl_cable 
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code ,cs_code section_no,gl_code gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'设备对'eqp_spec from sly_three_gl_group_cable where group_type='设备对'
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code ,cs_code section_no,gl_code gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'dcsw'eqp_spec from sly_two_gl_group_cable where group_type='DCSW' 

union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code ,cs_code section_no,gl_code gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'A环'eqp_spec from TMP_NOC_gl_cable_A 
union

select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code ,cs_code section_no,gl_code gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'cn2'eqp_spec from sly_two_gl_group_cable 
where group_type='CN2'
UNION 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code , section_no, gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'SDH'eqp_spec from TMP_SLY_SDHTRANS_2_SECTION 
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when   '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code , cs_code section_no, gl_no ,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'DWDM'eqp_spec from TMP_XHJ_TRANS_DWDM_CABLE ;


--select * from bse_gl_group_data t ;
DROP TABLE IF EXISTS bse_gl_group_data;
CREATE TABLE bse_gl_group_data AS
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_date,'单设备'eqp_spec  from sly_dwdm_gl_zc 
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'设备对'eqp_spec  from sly_three_cable_zc_section where    group_type='设备对'
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'dcsw'eqp_spec  from sly_two_cable_zc_section where    group_type='DCSW'
union 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'A环'eqp_spec  from sly_A_cable_zc_section 
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no,gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'cn2'eqp_spec  from sly_two_cable_zc_section
where group_type='cn2'
union
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,t1.code bs_no, gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'SDH'eqp_spec  from TMP_SLY_SDHTRANS_2_ZC join ${o3_res_schema}.CM_PIPELINE t1 on t1.id=BSE_SECT_ID  
UNION 
select DISTINCT case '${areaName}' when  '南京' then 'nj.js.cn' when  '无锡' then 'wx.js.cn' when  '镇江' then 'zj.js.cn'when  '苏州' then 'sz.js.cn' when  '南通' then 'nt.js.cn' when  '扬州' then 'yz.js.cn' when  '盐城' then 'yc.js.cn'when  '徐州' then 'xz.js.cn' when  '淮安' then 'ha.js.cn' when  '连云港' then 'lyg.js.cn' when  '常州' then 'cz.js.cn' when  '泰州' then 'tz.js.cn' when  '宿迁' then 'sq.js.cn'ELSE '' END as area_code,zc_section_code bs_no, gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') create_time,'DWDM'eqp_spec  from TMP_XHJ_TRANS_DWDM_ZC_KPI ;

--预迁改所有的单光路
drop table RLT_NOC_SLY_ALL_GLERR_YQG ;--select * from SLY_CN_NETRESOURCE  limit 100
create table RLT_NOC_SLY_ALL_GLERR_YQG as
select t1.city_name,t1.area_name,t1.device_id lgc_eqp_id,t1.device_code lgc_eqp_no,t1.gl_code phy_gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd')  create_date from sly_device_netsource  t1 where t1.error = '缺光路' and t1.gl_code is not null
union
select t2.city_name,t2.area_name,1 lgc_eqp_id,t2.lgc_eqp_no,t2.gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') as create_time  from SLY_DCSW_NETRESOURCE t2 where t2.error = '缺光路'and t2.gl_code is not null
union
select t3.city_name,t3.area_name,t3.device_id lgc_eqp_id,t3.device_code lgc_eqp_no,t3.gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd') as create_time from sly_pair_device_netsource t3 where t3.error = '缺光路'and t3.gl_code is not null
union
select t4.city_name,t4.area_name,t4.device_id lgc_eqp_id,t4.device_code lgc_eqp_no,t4.gl_code gl_no,to_char(CURRENT_DATE,'yyyy-mm-dd')as create_time from SLY_CN_NETRESOURCE  t4 where t4.error = '单光路'and t4.gl_code is not null
;

----预迁改数据专业所有的同光缆
drop table sly_cable_error_view_yqg ;--select * from sly_cable_error_view_yqg   limit 100
create table sly_cable_error_view_yqg as
select t1.city_name,t1.area_name,t1.cable_code cable_no,t1.cs_code section_no,t1.gl_code gl_no from sly_dwdm_gl_route_err  t1
where /*t1.lgc_eqp_spec_no in( 'OLT','BAS','DSW') and*/ t1.error = '同光缆'
group by t1.city_name,t1.area_name,t1.cable_code,t1.cs_code,t1.gl_code
union
select t2.city_name,t2.area_name,t2.cable_code cable_no,t2.cs_code section_no,t2.gl_code gl_no from SLY_DCSW_ROUTE t2 
where  t2.error = '同光缆'
group by t2.city_name,t2.area_name,t2.cable_code,t2.cs_code,t2.gl_code
union
select t3.city_name,t3.area_name,t3.cable_code cable_no,t3.cs_code section_no,t3.gl_code gl_no from SLY_PAIR_ROUTE t3
where t3.error = '同光缆'
group by t3.city_name,t3.area_name,t3.cable_code,t3.cs_code,t3.gl_code
union
select t4.city_name,t4.area_name,t4.cable_code,t4.cs_code,t4.gl_code from SLY_CN_ROUTE  t4
where t4.error = '同光缆'
group by t4.city_name,t4.area_name,t4.cable_code,t4.cs_code,t4.gl_code
union
select t5.city_name,t5.area_name,t5.cable_code,t5.cs_code,t5.gl_no from TMP_XHJ_TRANS_DWDM_CABLEerr  t5
group by t5.city_name,t5.area_name,t5.cable_code,t5.cs_code,t5.gl_no
union
select t6.city_name,t6.area_name,t6.cable_no,t6.section_no,t6.gl_no from RLT_SLY_SDHTRANS_2_SECTION t6
where t6.err = '光缆段重复'
group by t6.city_name,t6.area_name,t6.cable_no,t6.section_no,t6.gl_no;
--select * from RLT_SLY_SDHTRANS_2_SECTION   limit 100;

----预迁改数据专业所有的同管道
--select * from sly_zc_error_view_yqg  limit 100;
drop table sly_zc_error_view_yqg ;
create table sly_zc_error_view_yqg as
select t1.city_name,t1.area_name,t1.zc_section_code zc_eqp_no,t1.gl_code gl_no2 from sly_dwdm_gl_route_err  t1
where /*t1.lgc_eqp_spec_no in( 'OLT','BAS','DSW') and*/ t1.error = '同管道'
group by t1.city_name,t1.area_name,t1.zc_section_code,t1.gl_code
union
select t2.city_name,t2.area_name,t2.zc_section_code zc_eqp_no,t2.gl_code gl_no2 from SLY_DCSW_ROUTE  t2
where /*t2.lgc_eqp_spec_no  in('B设备'） and*/ t2.error = '同管道'
group by t2.city_name,t2.area_name,t2.zc_section_code,t2.gl_code
union
select t3.city_name,t3.area_name,t3.zc_section_code zc_eqp_no,t3.gl_code gl_no from SLY_PAIR_ROUTE  t3
where t3.error = '同管道'
group by t3.city_name,t3.area_name,t3.zc_section_code,t3.gl_code
union
select t4.city_name,t4.area_name,t4.zc_section_code,t4.gl_code from SLY_CN_ROUTE   t4
where t4.error = '同管道'
group by t4.city_name,t4.area_name,t4.zc_section_code,t4.gl_code
union
select t5.city_name,t5.area_name,t5.zc_section_code,t5.gl_no from TMP_XHJ_TRANS_DWDM_ZC_KPIERR   t5 where err='同管道'
group by t5.city_name,t5.area_name,t5.zc_section_code,t5.gl_no
union
select t6.city_name,t6.area_name,t6.zc_eqp_no,t6.gl_no from RLT_SLY_SDHTRANS_2_ZC  t6
where t6.err = '光缆段管道重复'
group by  t6.city_name,t6.area_name,t6.zc_eqp_no,t6.gl_no;

--------追加脚本---select * from gl_group_data_num limit 100  
--统计设备光路数
drop table gl_group_data_num ;
create table gl_group_data_num as
select g.lgc_eqp_no, g.eqp_spec, count(distinct g.gl_no) as all_gl_num, to_char(CURRENT_DATE,'yyyy-mm-dd') create_date
  from gl_group_data g
 where g.lgc_eqp_no is not null
 group by g.lgc_eqp_no, g.eqp_spec;
 
create index idx_lgc_eqp_no_num on gl_group_data_num(lgc_eqp_no);
create index idx_eqp_spec_num on gl_group_data_num(eqp_spec);

--统计设备主备用光路
drop table standby_application_gl ;
create table standby_application_gl as
select g.lgc_eqp_id,g.gl_no,g2.lgc_eqp_no,g2.eqp_spec,g2.gl_list,to_char(CURRENT_DATE,'yyyy-mm-dd')  update_time
  from gl_group_data g
  left join (select g1.lgc_eqp_no,
                    g1.eqp_spec,
                    string_agg(DISTINCT gl_no, ',') gl_list
               from gl_group_data g1
              where g1.lgc_eqp_no is not null
              group by g1.lgc_eqp_no, g1.eqp_spec) g2
  on g.lgc_eqp_no = g2.lgc_eqp_no
  and g.eqp_spec = g2.eqp_spec;
  
create index idx_lgc_eqp_no_gl_list on standby_application_gl(lgc_eqp_no);
create index idx_eqp_spec_gl_list on standby_application_gl(eqp_spec);
create index idx_gl_no_gl_list on standby_application_gl(gl_no);