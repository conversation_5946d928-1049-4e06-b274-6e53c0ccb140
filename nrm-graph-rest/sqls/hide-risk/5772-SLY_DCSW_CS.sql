drop table IF EXISTS TMP_noc_sly_dcsw_list ;
CREATE TABLE TMP_noc_sly_dcsw_list AS 
SELECT '${areaName}' AS CITY_NAME,t1.AREA_NAME,T1.IP,T1.V_NAME,
T1.V_NO,T1.LGC_EQP_NAME,T1.LGC_EQP_NO,T1.BK,'' port_code,'' port_code2,'' port_code3,'' port_code4,'' port_code5,'' port_code6,'' port_code7,''PORT_CODE8  FROM mid_noc_sly_dcsw_list t1 
WHERE  t1.city_name='${area_pinyin}';
--这里是因为匹配规则以及o3数据不明确，导致常州多了光路，现在剔除
DELETE FROM TMP_noc_sly_dcsw_list  where lgc_eqp_no='CZ-QYJ-DCSW-1.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1' ;
DELETE FROM TMP_noc_sly_dcsw_list  WHERE lgc_eqp_no='CZ-WXJ-DSW-1.MAN.S12508' AND BK='M-GigabitEthernet1/0/0/3';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-11.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-13.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-12.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-15.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-14.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/1';

DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-11.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/2';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-13.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/2';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-12.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/2';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-15.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/2';
DELETE FROM TMP_noc_sly_dcsw_list  WHERE v_no='CZ-HuL-VD-14.MAN.S12504' AND BK='M-GigabitEthernet1/0/0/2';
------------------------------------------------------------由于板卡数据规格不同，针对不同匹配形式做出了不同更改-------------------------------------------------------

UPDATE TMP_noc_sly_dcsw_list
SET port_code = 
    CONCAT_WS('/',
        RIGHT('00'||regexp_replace(bk, '^.*?(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/\d+/(\d+)$', '\1'), 2)
    );---10GE2/7/0/13  变为 02/07/00/13


UPDATE TMP_noc_sly_dcsw_list
SET port_code2 = 
    CONCAT_WS('/',
        --RIGHT('00'||regexp_replace(bk, '^.*?(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/\d+/(\d+)$', '\1'), 2)
    );-----10GE2/7/0/13  变为 07/00/13

UPDATE TMP_noc_sly_dcsw_list
SET port_code8 = 
    CONCAT_WS('/',
        --RIGHT('00'||regexp_replace(bk, '^.*?(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/(\d+)/.*$', '\1'), 1),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/(\d+)/.*$', '\1'), 1),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/\d+/(\d+)$', '\1'), 2)
    );-----10GE2/7/0/13  变为 7/0/13
UPDATE TMP_noc_sly_dcsw_list b
SET port_code3 = result1 from (
    SELECT *,
    regexp_replace(bk, '.*?([0-9]/[0-9]/[0-9]+)', '\1') AS result1 from TMP_noc_sly_dcsw_list)a
where a.LGC_EQP_NO = b.LGC_EQP_NO and a.bk=b.bk;
 <#if areaCode = 'sz'>
DROP TABLE IF EXISTS cm_port1;
create table cm_port1 as   
SELECT t.*,
    regexp_replace(t.nm_code, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from ${o3_res_schema}.cm_port_01 t 
where t.physic_device_id in(select c.id from ${o3_res_schema}.cm_device c where  c.code in (select distinct lgc_eqp_no from TMP_noc_sly_dcsw_list) );

insert into cm_port1
SELECT t.*,
    regexp_replace(t.nm_code, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from ${o3_res_schema}.cm_port_02 t 
where t.physic_device_id in(select c.id from ${o3_res_schema}.cm_device c where  c.code in (select distinct lgc_eqp_no from TMP_noc_sly_dcsw_list) );

insert into cm_port1
SELECT t.*,
    regexp_replace(t.nm_code, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from ${o3_res_schema}.cm_port_03 t 
where t.physic_device_id in(select c.id from ${o3_res_schema}.cm_device c where  c.code in (select distinct lgc_eqp_no from TMP_noc_sly_dcsw_list) );

insert into cm_port1
SELECT t.*,
    regexp_replace(t.nm_code, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from ${o3_res_schema}.cm_port_04 t 
where t.physic_device_id in(select c.id from ${o3_res_schema}.cm_device c where  c.code in (select distinct lgc_eqp_no from TMP_noc_sly_dcsw_list) );
</#if>
 <#if areaCode != 'sz'>
DROP TABLE IF EXISTS cm_port1;
create table cm_port1 as   
SELECT t.*,
    regexp_replace(t.nm_code, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from ${o3_res_schema}.cm_port t 
where t.physic_device_id in(select c.id from ${o3_res_schema}.cm_device c where  c.code in (select distinct lgc_eqp_no from TMP_noc_sly_dcsw_list));
</#if>
				
				UPDATE TMP_noc_sly_dcsw_list
SET port_code4 = 
    CONCAT_WS('/',
        RIGHT('00'||regexp_replace(bk, '^.*?(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/(\d+)/.*$', '\1'), 2),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/(\d+)/.*$', '\1'), 2)
        --RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/\d+/(\d+)$', '\1'), 2)
    ); 

UPDATE TMP_noc_sly_dcsw_list
SET port_code5 = 
    REPLACE(port_code4, '//', '/0')
    ; 
		--SELECT * FROM cm_port1
/*alter table TMP_noc_sly_dcsw_list add port_code4 varchar(100);
UPDATE TMP_noc_sly_dcsw_list b
SET port_code4 = result1 from (
    SELECT *,
    regexp_replace(bk, '.*?([0-9]/[0-9]/[0-9]/[0-9]+)', '\1') AS result1 from TMP_noc_sly_dcsw_list)a
where a.LGC_EQP_NO = b.LGC_EQP_NO and a.bk=b.bk;*/
-----10GE2/7/0/13  变为 7/0/13
-----10GE2/7/0/13  变为 7/0/13

				UPDATE TMP_noc_sly_dcsw_list
SET port_code6 = 
    SUBSTRING(port_code3 FROM 3)
    ; 
UPDATE TMP_noc_sly_dcsw_list
SET port_code7 = 
    CONCAT_WS('/',
        RIGHT('0'||regexp_replace(bk, '^.*?(\d+)/.*$', '\1'), 1),
        RIGHT('0'||regexp_replace(bk, '^.*?\d+/(\d+)/.*$', '\1'), 1),
        RIGHT('0'||regexp_replace(bk, '^.*?\d+/\d+/(\d+)/.*$', '\1'), 1),
        RIGHT('00'||regexp_replace(bk, '^.*?\d+/\d+/\d+/(\d+)$', '\1'), 2)
    );
  -------下面还有一种是BK直接等于NM_CODE总共总结四种大规律

CREATE INDEX TMP_noc_sly_dcsw_listsy1 ON TMP_noc_sly_dcsw_list (port_code);
CREATE INDEX TMP_noc_sly_dcsw_listsy2 ON TMP_noc_sly_dcsw_list (port_code2);
CREATE INDEX TMP_noc_sly_dcsw_listsy3 ON TMP_noc_sly_dcsw_list (port_code3);
CREATE INDEX TMP_noc_sly_dcsw_listsy4 ON TMP_noc_sly_dcsw_list (port_code4);
CREATE INDEX TMP_noc_sly_dcsw_listsy5 ON TMP_noc_sly_dcsw_list (port_code5);
CREATE INDEX TMP_noc_sly_dcsw_listsy6 ON TMP_noc_sly_dcsw_list (port_code6);
CREATE INDEX TMP_noc_sly_dcsw_listsy7 ON TMP_noc_sly_dcsw_list (port_code7);
-------------------------------------


--and p.nm_code = t.bk
drop table IF EXISTS TMP_DCSW_PHY_GL;
create table TMP_DCSW_PHY_GL as
select DISTINCT t.city_name,t.area_name,t.v_name,t.v_no,t.bk,t.lgc_eqp_no,t.lgc_eqp_name,c."id" device_id,c.code device_code,c."name" device_name,p."id" port_id,p.code port_code,p."name" port_name,t2.id gl_id,t2.code gl_code 
from TMP_noc_sly_dcsw_list t
left join ${o3_res_schema}.cm_device c on t.lgc_eqp_no = c.code 
left join cm_port1 p on c.id = p.physic_device_id and p.up_down_id = 100362  and (t.port_code=p.nm_code or t.bk=p.nm_code or t.port_code2=p.nm_code or t.port_code3 =p.nm_code OR t.port_code3 =p.RESULT1 or t.port_code4=p.nm_code or t.port_code5=p.nm_code or  t.port_code6=p.nm_code  or t.port_code3=p.code or '1/'||t.port_code3 = p.code OR  T.PORT_CODE4=P.CODE or T.PORT_CODE5=P.CODE OR T.PORT_CODE7=P.CODE OR T.PORT_CODE8=P.CODE)---N种条件满足其一即可
left join ${o3_res_schema}.cm_link t2 on t2.a_physic_device_id = c.id and t2.a_port_id = p.id and t2.spec_id = 1132400006
union
select DISTINCT t.city_name,t.area_name,t.v_name,t.v_no,t.bk,t.lgc_eqp_no,t.lgc_eqp_name,c."id" device_id,c.code device_code,c."name" device_name,p."id" port_id,p.code port_code,p."name" port_name,t2.id gl_id,t2.code gl_code 
from TMP_noc_sly_dcsw_list t
left join ${o3_res_schema}.cm_device c on t.lgc_eqp_no = c.code 
left join cm_port1 p on c.id = p.physic_device_id and p.up_down_id = 100362  and (t.port_code=p.nm_code or t.bk=p.nm_code or t.port_code2=p.nm_code or  t.port_code3 =p.nm_code OR t.port_code3 =p.RESULT1 or  t.port_code4=p.nm_code or t.port_code5=p.nm_code or t.port_code6=p.nm_code  or t.port_code3=p.code or '1/'||t.port_code3 = p.code OR  T.PORT_CODE4=P.CODE or T.PORT_CODE5=P.CODE OR T.PORT_CODE7=P.CODE OR T.PORT_CODE8=P.CODE)---N种条件满足其一即可
left join ${o3_res_schema}.cm_link t2 on t2.z_physic_device_id = c.id and t2.z_port_id = p.id and t2.spec_id = 1132400006;


DELETE FROM TMP_DCSW_PHY_GL T
 WHERE T.gl_id IS NULL
   AND EXISTS (SELECT 1  FROM TMP_DCSW_PHY_GL t1
         WHERE T.device_id = t1.device_id AND t1.gl_id IS NOT NULL);
---------------网络资源检测----------------
drop table IF EXISTS SLY_DCSW_NETRESOURCE;
create table SLY_DCSW_NETRESOURCE as
select distinct T.city_name,T.area_name,T.v_name,T.v_no,T.lgc_eqp_name,T.lgc_eqp_no,T.port_code,T.gl_code ,'缺端口' ERROR from TMP_DCSW_PHY_GL T
where t.v_name in 
(select t1.v_name from TMP_DCSW_PHY_GL t1 
group by t1.v_name having count(distinct t1.lgc_eqp_name || t1.port_code)<2);


insert into SLY_DCSW_NETRESOURCE
select distinct T.city_name,T.area_name,T.v_name,T.v_no,T.lgc_eqp_name,T.lgc_eqp_no,T.port_code,T.gl_code ,'缺光路' ERROR from TMP_DCSW_PHY_GL T
where t.device_id in(select t1.device_id from TMP_DCSW_PHY_GL t1 
group by t1.device_id having count (distinct t1.gl_code)<2 ) and t.device_id in(select t1.device_id from TMP_DCSW_PHY_GL t1 
group by t1.device_id having count (distinct t1.port_code)>=2 );
commit;
 delete from TMP_DCSW_PHY_GL t where exists(select 1 from SLY_DCSW_NETRESOURCE t1 where t.v_no=t1.v_no );

------检测同板卡
drop table IF EXISTS sly_dcsw_device_gl_bk ;
create table sly_dcsw_device_gl_bk as
select cd.*,
(case when cdw2.code='' then '' else cdw2.code || '/' end)||
(case when cdw1.code='' then '' else cdw1.code || '/' end)||
(case when pcdw.code='' then '' else pcdw.code end) AS bk_code
from TMP_DCSW_PHY_GL cd
left join ${o3_res_schema}.cm_port cm on cm."id" = cd.port_id
left join (SELECT   cdw.child_id,cdw.parent_id, cw.code FROM    ${o3_res_schema}.CR_DEVICE_WARE cdw JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) pcdw on pcdw.child_id = cm.ware_id 
left join (SELECT   cdw.child_id,cdw.parent_id, cw.code FROM    ${o3_res_schema}.CR_DEVICE_WARE cdw JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw1 on cdw1.child_id = pcdw.parent_id and cdw1.child_id IS NOT NULL
left join (SELECT   cdw.child_id,cdw.parent_id, cw.code FROM    ${o3_res_schema}.CR_DEVICE_WARE cdw JOIN ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw2 on cdw2.child_id = cdw1.parent_id and cdw2.child_id IS NOT NULL;

drop table IF EXISTS sly_dcsw_device_gl_bk_err ;
create table sly_dcsw_device_gl_bk_err as
select t.* from sly_dcsw_device_gl_bk t 
where t.device_code in(select distinct t1.v_name from sly_dcsw_device_gl_bk t1
where t1.gl_code is not null
and t1.bk_code is not null
group by t1.v_name having count(distinct (t1.bk_code)||(t1.device_code)) = 1
);

delete from sly_gl_group_info sg where sg.group_type = 'DCSW';
insert into sly_gl_group_info (group_code,group_name,group_type,gl_id,gl_code) 
select distinct t.v_no,t.v_name,'DCSW',t.gl_id,t.gl_code from TMP_DCSW_PHY_GL t where t.gl_code is not null;																																			drop table IF EXISTS sly_dwdm_gl_no_1_dcsw ;
create table sly_dwdm_gl_no_1_dcsw as
select distinct t1.* ,  t3.id bf_id , t3.code bf_no   --, t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec
from TMP_DCSW_PHY_GL t1 
inner join ${o3_res_schema}.cm_link gl on gl.code = t1.gl_code and gl.spec_id = 1131200002
inner join ${o3_res_schema}.cr_link_link t2 on t2.lower_link_id = gl.id and t2.upper_link_spec_id = 1132100021 -- 以太网链路

inner join ${o3_res_schema}.cr_link_link t4 on t4.upper_link_id = t2.upper_link_id and t4.lower_link_spec_id = 1132300020 -- client链路
inner join ${o3_res_schema}.cm_link t3 on t3.id = t4.lower_link_id
join ${o3_res_schema}.cv_link tt on tt.link_id=t3.id and pkey='P_OPT_ROUTE_MODEL'and value='100172'
; 
--select * from sly_dwdm_gl_no_1_dcsw limit 100;
create index sly_dwdm_gl_no_1dcswsy on sly_dwdm_gl_no_1_dcsw(bf_id);

drop table  if exists  sly_dwdm_gl_no_dcsw;
create table sly_dwdm_gl_no_dcsw as select  DiSTINCT  t.*,t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec from sly_dwdm_gl_no_1_dcsw t 
join ${o3_res_schema}.cr_link_link t5 on t.bf_id=t5.upper_link_id and t5.lower_link_spec_id = 1132300005 
inner join ${o3_res_schema}.cm_link t3 on t3.id = t5.lower_link_id

join ${o3_res_schema}.cr_link_link t6 on t5.lower_link_id=t6.upper_link_id and t6.lower_link_spec_id = 1132200002
join ${o3_res_schema}.cr_link_link t7 on t6.lower_link_id=t7.upper_link_id and t7.lower_link_spec_id = 1132300002
join ${o3_res_schema}.cr_link_link t8 on t7.lower_link_id=t8.upper_link_id and t8.lower_link_spec_id = 1132100010
join ${o3_res_schema}.cr_link_link t9 on t8.lower_link_id=t9.upper_link_id  and t9.lower_link_spec_id = 1131200002
join ${o3_res_schema}.cr_link_link t10 on t9.lower_link_id=t10.lower_link_id  and t10.upper_link_spec_id = 1132400006
join ${o3_res_schema}.cm_link t11 on t10.upper_link_id=t11.id ;

--select * from sly_dwdm_gl_no_dcsw limit 100;







--select DISTINCT * from sly_dwdm_gl_no_1_dcsw 
drop table if EXISTS cm_link_gl;
drop table if EXISTS cr_link_link_t4 ;
drop table if EXISTS cm_link_t13;
drop table if EXISTS cr_link_link_t2;
----整合同属一个波分的
insert into sly_dwdm_gl_no_dcsw 
select t.*,null,t.gl_code||'波分',t.gl_id,t.gl_code from tmp_dcsw_phy_gl1 t ;



-----------------------------------------------路由资源分析---------------------------------------------------------
drop table IF EXISTS sly_dwdm_gl_cable_dcsw ;
create table sly_dwdm_gl_cable_dcsw as
select distinct  t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level
from sly_dwdm_gl_no_dcsw t
left join    ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id_bf and cll1.spec_id=1133111310000
left join    ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join   ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join   ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
left join   ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  --光缆段
left join   ${o3_res_schema}.cr_net_entity cne on cs.id=cne.entity_ID
left join   ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join   ${o3_res_schema}.pm_pub_restriction ppr on ppr.serial_no = cn.net_type_id;

delete from sly_dwdm_gl_cable_dcsw t where t.cs_code is null and exists(select 1 from sly_dwdm_gl_cable_dcsw t1 where t1.device_code = t.device_code and t1.gl_code = t.gl_code and  t.cs_code is not null);

drop table IF EXISTS temp_sly_dwdm_num_dcsw ;
create table temp_sly_dwdm_num_dcsw as
select  v_no,count(distinct t.gl_code) num from sly_dwdm_gl_cable_dcsw t 
group by  v_no;
 
drop table IF EXISTS temp_sly_dwdm_num2_dcsw ;
create table temp_sly_dwdm_num2_dcsw as
select  v_no,cs_id,count(distinct t.gl_code) num from sly_dwdm_gl_cable_dcsw t where t.cable_level <> '局内'and t.cable_level <> '联络'
group by  v_no,cs_id ;

drop table IF EXISTS temp_sly_cable_err_dcsw ;
create table temp_sly_cable_err_dcsw as
select t.* from temp_sly_dwdm_num2_dcsw t 
inner join temp_sly_dwdm_num_dcsw t1 on t1.v_no = t.v_no and t1.num = t.num ;
--select * from temp_sly_cable_err_dcsw limit 100
--这里的是通过光缆段--管孔--管道段，但是目前有两种特殊情况1.光缆段--子管孔--管孔--管道段 2.光缆段--管道段
drop table IF EXISTS sly_dwdm_gl_zc_dcsw ;--select DISTINCT * from sly_dwdm_gl_zc_dcsw where gl_code='F2109020394'limit 100
create table  sly_dwdm_gl_zc_dcsw as
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_dcsw t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001 --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union 
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_dcsw t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id  --子管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id 
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
UNION
select distinct t.*,t2.id zc_section_id, null as code,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable_dcsw t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id !=1111100001  --去掉管孔
--left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id;




create index sly_dwdm_gl_zc_dcswys1 on sly_dwdm_gl_zc_dcsw(device_id);
create index sly_dwdm_gl_zc_dcswsy2 on sly_dwdm_gl_zc_dcsw(cs_id);
create index sly_dwdm_gl_zc_dcswsy3 on sly_dwdm_gl_zc_dcsw(zc_section_id);

delete from sly_dwdm_gl_zc_dcsw t where cs_id is null;
delete from sly_dwdm_gl_zc_dcsw t where z_zc_eqp_code is null AND a_zc_eqp_code is null AND  zc_section_id  IS NOT NULL ;

delete from sly_dwdm_gl_zc_dcsw t where T.zc_section_id is null and exists
(select 1 from sly_dwdm_gl_zc_dcsw T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);


drop table IF EXISTS sly_dwdm_gl_route_err1_dcsw ; --SELECT DEVICE_CODE FROM sly_dwdm_gl_route_err1 GROUP BY DEVICE_CODE
create table  sly_dwdm_gl_route_err1_dcsw as
select T.* , '无穿管' ERROR from sly_dwdm_gl_zc_dcsw T 
where T.zc_section_id is null and T.Cable_Level<> '局内' and T.Cable_Level<> '联络' and not exists
(select 1 from sly_dwdm_gl_zc_dcsw T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);

--发现存在光缆属性不是局内，但光缆段属性是局内。所以在此做一个剔除；
delete from sly_dwdm_gl_route_err1_dcsw t where error='无穿管' and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );
--删除光缆级别为楼间的无穿管
delete from sly_dwdm_gl_route_err1_dcsw      where cable_level='楼间'; 



DELETE FROM sly_dwdm_gl_zc_dcsw WHERE zc_section_id is null;

delete from sly_dwdm_gl_zc_dcsw t where z_zc_eqp_code is null or a_zc_eqp_code is null;

drop table IF EXISTS temp_sly_dwdm_num3_dcsw ;
create table temp_sly_dwdm_num3_dcsw as
select  t.v_no, t.zc_section_code,count(distinct t.gl_code) num from sly_dwdm_gl_zc_dcsw t
where t.cable_level <> '局内'
group by  t.v_no, t.zc_section_code;

drop table IF EXISTS temp_sly_zc_err_dcsw ;
create table temp_sly_zc_err_dcsw as
select distinct t.* from temp_sly_dwdm_num3_dcsw t
inner join temp_sly_dwdm_num_dcsw t1 on t1.v_no= t.v_no and t1.num = t.num  ;

insert into sly_dwdm_gl_route_err1_dcsw 
select t.*,'同管道'  from sly_dwdm_gl_zc_dcsw t
where EXISTS (select 1 from temp_sly_zc_err_dcsw t1 where t1.v_no= t.v_no and t1.zc_section_code = t.zc_section_code) 
and not EXISTS (select 1 from temp_sly_cable_err_dcsw t1 where t1.v_no = t.v_NO and t1.cs_id = t.cs_id) ;

insert into sly_dwdm_gl_route_err1_dcsw (city_name,area_name,v_name,v_no,bk,lgc_eqp_no,lgc_eqp_name,device_id,device_code,device_name,port_id,port_code,port_name,gl_id,gl_code,bf_id,bf_no,gl_id_bf,gl_code_bf,gl_spec,cs_id,cs_code,cs_name,cable_id,cable_code,cable_name,spec_id,cable_level,error)
select t.*,'同光缆'  from sly_dwdm_gl_cable_dcsw t
where EXISTS (select 1 from temp_sly_cable_err_dcsw t1 where t1.V_NO= t.V_NO and t1.cs_id = t.cs_id) ;






DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR_DCSW1 ;--select * from TMP_PHYPAIR_ZC_EQP_CXR_DCSW1 limit 100
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR_DCSW1  
AS
 SELECT  t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.a_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.v_no,zc_section_code BSE_SECT_ID
   FROM  sly_dwdm_gl_zc_dcsw  T 
	 join  TMP_DCSW_PHY_GL   t3 on t.v_no=t3.v_no and t.gl_code=t3.gl_code 
	 join ${o3_res_schema}.cm_facility t2 on t.a_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id
 union
  SELECT  t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.z_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.v_no,zc_section_code BSE_SECT_ID
   FROM  sly_dwdm_gl_zc_dcsw  T 
	 join  TMP_DCSW_PHY_GL   t3 on t.v_no=t3.v_no and t.gl_code=t3.gl_code
	 join ${o3_res_schema}.cm_facility t2 on t.z_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id;
COMMIT;


DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR_DCSW ;
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR_DCSW
AS
SELECT DISTINCT LGC_EQP_ID,GL_NO2,BSE_EQP_ID, BEQ_NO, BSE_SPEC,v_no
   FROM TMP_PHYPAIR_ZC_EQP_CXR_DCSW1 T
  GROUP BY LGC_EQP_ID, GL_NO2, BSE_EQP_ID, BSE_SPEC,T.v_no,T.BEQ_NO
 HAVING COUNT(DISTINCT BSE_SECT_ID) = 1;
COMMIT;


drop table IF EXISTS dcsw_white;
create table dcsw_white as
select distinct t.group_name v_no,t.bse_eqp_no eqp1 from sly_sys_white_config t where t.function_type = 'DCSW'
union
select  distinct  v_no,BEQ_NO from TMP_PHYPAIR_ZC_EQP_CXR_DCSW ;

drop table IF EXISTS dcsw_white1;
create table dcsw_white1 as
select distinct t.v_no,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from dcsw_white t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp1 = t1.a_zc_eqp_code and t.v_no = t1.v_no
union
select distinct t.v_no,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from dcsw_white t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp1 = t1.z_zc_eqp_code  and t.v_no = t1.v_no;

drop table IF EXISTS dcsw_white2;
create table dcsw_white2 as
select distinct t.v_no,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from dcsw_white1 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from dcsw_white1 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white3;
create table dcsw_white3 as
select distinct t.v_no,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from dcsw_white2 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from dcsw_white2 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white4;
create table dcsw_white4 as
select distinct t.v_no,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from dcsw_white3 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from dcsw_white3 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white5;
create table dcsw_white5 as
select distinct t.v_no,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from dcsw_white4 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from dcsw_white4 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white6;
create table dcsw_white6 as
select distinct t.v_no,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from dcsw_white5 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from dcsw_white5 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white7;
create table dcsw_white7 as
select distinct t.v_no,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from dcsw_white6 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from dcsw_white6 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white8;
create table dcsw_white8 as
select distinct t.v_no,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from dcsw_white7 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from dcsw_white7 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white9;
create table dcsw_white9 as
select distinct t.v_no,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from dcsw_white8 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.v_no = t1.v_no 
where t1.z_zc_eqp_code is not null
union
select distinct t.v_no,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from dcsw_white8 t 
left join sly_dwdm_gl_zc_dcsw   t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.v_no = t1.v_no where t1.z_zc_eqp_code is not null;

drop table IF EXISTS dcsw_white_list;
create table dcsw_white_list as
select t.v_no,t.eqp1 zc_eqp_no from dcsw_white t
union 
select t1.v_no,t1.eqp2 from dcsw_white1 t1
union 
select t2.v_no,t2.eqp3 from dcsw_white2 t2
union 
select t3.v_no,t3.eqp4 from dcsw_white3 t3
union 
select t4.v_no,t4.eqp5 from dcsw_white4 t4
union 
select t5.v_no,t5.eqp6 from dcsw_white5 t5
union 
select t6.v_no,t6.eqp7 from dcsw_white6 t6
union 
select t7.v_no,t7.eqp8 from dcsw_white7 t7
union 
select t8.v_no,t8.eqp9 from dcsw_white8 t8
union 
select t9.v_no,t9.eqp10 from dcsw_white9 t9;

select * from dcsw_white9  limit 100;
delete from sly_dwdm_gl_route_err1_dcsw t where  t.error = '同管道' and exists(select 1 from dcsw_white_list t1 where t.v_no = t1.v_no and (t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code)) and exists (select 1 from ${o3_res_schema}.cm_facility t1  where notes like '%单路由局前井%' and (t1.code = t.a_zc_eqp_code or t1.code = t.z_zc_eqp_code) );

----------------------------------------统计脚本

           
--------------------------------统计脚本----------------
delete from sly_dcsw_check_info_gzdl t where t.create_time = CURRENT_DATE;

insert into sly_dcsw_check_info_gzdl (city_name,eqp_num)
select t.city_name,count(distinct t.v_name) from TMP_DCSW_PHY_GL  t group by t.city_name;

update sly_dcsw_check_info_gzdl set port_err = (select count(distinct t.v_name) count1 from SLY_DCSW_NETRESOURCE t where t.error = '缺端口' group by t.city_name) where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set port_err = 0 where port_err is null and create_time = CURRENT_DATE;
 
update sly_dcsw_check_info_gzdl set gl_err = (select count(distinct t.v_name) count1 from SLY_DCSW_NETRESOURCE t where t.error = '缺光路' group by t.city_name) where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set gl_err = 0 where gl_err is null and create_time = CURRENT_DATE;

update sly_dcsw_check_info_gzdl set bk_err = (select count(distinct t.v_name) count1 from sly_dcsw_device_gl_bk_err t  group by t.city_name) where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set bk_err = 0 where bk_err is null and create_time = CURRENT_DATE;

update sly_dcsw_check_info_gzdl set cable_err = (select count(distinct t.v_name) count1 from sly_dwdm_gl_route_err1_dcsw t where t.error = '同光缆') where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set cable_err = 0 where cable_err is null and create_time = CURRENT_DATE;

update sly_dcsw_check_info_gzdl set zc_err = (select count(distinct t.v_name) count1 from sly_dwdm_gl_route_err1_dcsw t where t.error = '同管道') where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set zc_err = 0 where zc_err is null and create_time = CURRENT_DATE;

update sly_dcsw_check_info_gzdl set none_zc = (select count(distinct t.v_name) count1 from sly_dwdm_gl_route_err1_dcsw t where t.error = '无穿管') where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl set none_zc = 0 where none_zc is null and create_time = CURRENT_DATE;

drop table TMP_DCSW_PHY_GL1 ;
ALTER TABLE TMP_DCSW_PHY_GL  RENAME TO TMP_DCSW_PHY_GL1;

 drop table if EXISTS SLY_dcsw_ERR_4_DATE_gzdl;
create table SLY_dcsw_ERR_4_DATE_gzdl AS
SELECT *,CURRENT_DATE as sj,NULL::date AS SJ_his FROM sly_dwdm_gl_route_err1_dcsw  ;
insert into SLY_dcsw_ERR_4_DATE_gzdl (city_name,area_name,v_name,v_no,device_name,device_code,port_code,gl_code,error,sj)
select city_name,area_name,v_name,v_no,lgc_eqp_name,lgc_eqp_no,port_code,gl_code,error,CURRENT_DATE as sj from SLY_DCSW_NETRESOURCE ;



update SLY_DCSW_ERR_4_DATE_gzdl t set SJ_his=t1.SJ_his from SLY_DCSW_ERR_4_DATE_his_gzdl t1 where t1.v_no=t.v_no and t1.error=t.error;
update SLY_DCSW_ERR_4_DATE_gzdl t set sj_his=sj where sj_his is null;
drop table if EXISTS  SLY_DCSW_ERR_4_DATE_his_gzdl;
create table SLY_DCSW_ERR_4_DATE_his_gzdl as 
select * ,SJ-SJ_his as err_sj from SLY_DCSW_ERR_4_DATE_gzdl;

update sly_dcsw_check_info_gzdl  set day_err= (select count(distinct t.v_name) count1 from  SLY_DCSW_ERR_4_DATE_his_gzdl   t where t.err_sj >= 4) where create_time = CURRENT_DATE;
update sly_dcsw_check_info_gzdl  set day_err = 0 where day_err  is null and create_time = CURRENT_DATE;
