-------------------------------------------------设备清单----------------------------------------------
--1024600002 交换机：汇聚交换机DSW IDC交换机
--1024600001 路由器中有：SR ER
-- 宽带接入服务器(BRAS)
--1028100001 DSLAM设备
--1028200001 OLT设备
--1028400001 AG设备
drop table IF EXISTS sly_device_base_info ;
create table sly_device_base_info as


select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
CASE
    WHEN t.network_role_id=100663 THEN 'DSW设备'
    WHEN t.network_role_id=81702876 THEN 'IDC交换机'
   
END device_spec,t.create_date 
from   ${o3_res_schema}.cm_device t
inner join   ${o3_res_schema}.pm_pub_res_type t1 on t1.res_type_id = t.spec_id and t1.res_type_id = 1024600002 --交换机
left join   ${o3_res_schema}.RM_AREA t2 on t2.id = t.region_id 
left join   ${o3_res_schema}.RM_AREA t3 on t3.id = t.leaf_region_id
left join   ${o3_res_schema}.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   ${o3_res_schema}.RM_NUMBER t5 on t5.id = t4.number_id
where t.network_role_id in(100663,81702876) --dsw idc交换机 	
and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')   <#if areaCode= 'sq'> 
and  virtual_real_id in (100706,
100707)
  </#if>
union
select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
CASE
    WHEN t.network_role_id=81704825 THEN 'ER'
    WHEN t.network_role_id=103606 THEN 'SR'
   
END  device_spec,t.create_date 
from   ${o3_res_schema}.cm_device t
inner join   ${o3_res_schema}.pm_pub_res_type t1 on t1.res_type_id = t.spec_id and t1.res_type_id = 1024600001 --路由器
left join   ${o3_res_schema}.RM_AREA t2 on t2.id = t.region_id 
left join   ${o3_res_schema}.RM_AREA t3 on t3.id = t.leaf_region_id
left join   ${o3_res_schema}.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   ${o3_res_schema}.RM_NUMBER t5 on t5.id = t4.number_id
where t.network_role_id in(81704825,103606)--ER,SR
 and t.code not like '%NMAN%' and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')
 
 union
 select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
'新城'  device_spec,t.create_date 
from   ${o3_res_schema}.cm_device t
inner join   ${o3_res_schema}.pm_pub_res_type t1 on t1.res_type_id = t.spec_id and t1.res_type_id = 1024600001 --路由器
left join   ${o3_res_schema}.RM_AREA t2 on t2.id = t.region_id 
left join   ${o3_res_schema}.RM_AREA t3 on t3.id = t.leaf_region_id
left join   ${o3_res_schema}.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   ${o3_res_schema}.RM_NUMBER t5 on t5.id = t4.number_id
where t.code like '%NMAN%'
 and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')
union
select  -- t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
t1.res_type device_spec,t.create_date 
from   ${o3_res_schema}.cm_device t
inner join   ${o3_res_schema}.pm_pub_res_type t1 on t1.res_type_id = t.spec_id 
left join   ${o3_res_schema}.RM_AREA t2 on t2.id = t.region_id 
left join   ${o3_res_schema}.RM_AREA t3 on t3.id = t.leaf_region_id
left join   ${o3_res_schema}.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   ${o3_res_schema}.RM_NUMBER t5 on t5.id = t4.number_id 
where  t.life_state_id = 100374 and t.other_state_id = 100381 
and ( t1.res_type_id in(1024600003,1028100001,1028200001,1028400001) or t.notes in ('C网-IGWMGW', 'C网-MGW', 'IMS-MGW', '软交换TG') )
and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功') ;
<#if areaCode= 'cz'> 
insert into sly_device_base_info 
 select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
'宽带接入设备'  device_spec,t.create_date 
from   res_cz_sch.cm_device t
inner join   res_cz_sch.pm_pub_res_type t1 on t1.res_type_id = t.spec_id and (t1.res_type_id = 1024600001 or t1.res_type_id = 1024600003)--路由器 或宽带接入设备
left join   res_cz_sch.RM_AREA t2 on t2.id = t.region_id 
left join   res_cz_sch.RM_AREA t3 on t3.id = t.leaf_region_id
left join   res_cz_sch.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   res_cz_sch.RM_NUMBER t5 on t5.id = t4.number_id
where t.code like '%MAN%' and t.code not like'%NMAN%'and not exists (select 1 from sly_device_base_info z where z.device_code=t.code)
 and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')
 union
  select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
'新城'  device_spec,t.create_date 
from   res_cz_sch.cm_device t
inner join   res_cz_sch.pm_pub_res_type t1 on t1.res_type_id = t.spec_id and (t1.res_type_id = 1024600001 or t1.res_type_id = 1024600003)--路由器 或宽带接入设备
left join   res_cz_sch.RM_AREA t2 on t2.id = t.region_id 
left join   res_cz_sch.RM_AREA t3 on t3.id = t.leaf_region_id
left join   res_cz_sch.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   res_cz_sch.RM_NUMBER t5 on t5.id = t4.number_id
where t.code like '%NMAN%'and not exists (select 1 from sly_device_base_info z where z.device_code=t.code)
 and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')
 union
   select   --t5.code ip
'' ip, t2.name city_name,t3.name area_name,t.id device_id,t.code device_code,t.name device_name,
'DSW设备'  device_spec,t.create_date 
from   res_cz_sch.cm_device t
inner join   res_cz_sch.pm_pub_res_type t1 on t1.res_type_id = t.spec_id 
left join   res_cz_sch.RM_AREA t2 on t2.id = t.region_id 
left join   res_cz_sch.RM_AREA t3 on t3.id = t.leaf_region_id
left join   res_cz_sch.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t.id
--left join   res_cz_sch.RM_NUMBER t5 on t5.id = t4.number_id
where t.code like '%MAN%' and t.network_role_id in(100663,102037,107695) and not exists (select 1 from sly_device_base_info z where z.device_code=t.code)
 and t.life_state_id = 100374 and t.other_state_id = 100381 and t.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功');
 </#if>
--/**/

<#if areaCode= 'nj'> 
truncate table sys_sly_function_config;
insert into sys_sly_function_config (device_name,device_spec)
select name,'政企设备' from res_nj_sch.cm_device where spec_id in (1022200001,1022500001,1022200002) and network_layer_id in (100657,81730627,81730628);
insert into sys_sly_function_config (device_code,device_spec,device_ip)
select  DISTINCT  t.code,'云设备',z.name from sly_device_tmp z
 join odso_nj_sch.res_phy_dev_daily  a  on ip=a.telnet_ip join res_nj_sch.cm_device t on t.id=a.phy_eqp_id; 
 </#if>

--配置表里的设备包括：政企设备
insert into sly_device_base_info
select   --t5.code ip
device_ip ip, t2.name city_name,t3.name area_name,t6.id device_id,t6.code device_code,t6.name device_name,
t.device_spec  device_spec,t6.create_date  
from sys_sly_function_config t
inner join   ${o3_res_schema}.cm_device t6 on (t6.name = t.device_name or t6.code = t.device_code)
inner join   ${o3_res_schema}.pm_pub_res_type t1 on t1.res_type_id = t6.spec_id 
left join   ${o3_res_schema}.RM_AREA t2 on t2.id = t6.region_id 
left join   ${o3_res_schema}.RM_AREA t3 on t3.id = t6.leaf_region_id
left join   ${o3_res_schema}.RR_NUMBER_ENTITY t4 on t4.ENTITY_ID = t6.id
--left join   ${o3_res_schema}.RM_NUMBER t5 on t5.id = t4.number_id
where  t6.code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功');

commit;

--删除出现在dcsw设备检测的dsw设备
delete from sly_device_base_info t where   exists  (select 1 from mid_noc_sly_dcsw_list where t.device_code=lgc_eqp_no or t.device_name=lgc_eqp_name  ) and t.device_spec='DSW设备';

commit;

-----------------------------------------设备到光路 100362：上联 100366：下联-----------------------------------------
drop table if exists cm_link_t2;

drop table IF EXISTS sly_device_gl_info_1 ;
create table sly_device_gl_info_1 as
select    t.* ,t1.id port_id , t1.code port_code--,t2.id gl_id ,t2.code gl_code  
from sly_device_base_info t 
left join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id and t1.up_down_id = 100362; 
create index sly_device_gl_info_1sy1 on sly_device_gl_info_1(device_id);
create index sly_device_gl_info_1sy2 on sly_device_gl_info_1(port_id);
drop table if exists sly_device_gl_info;
create table sly_device_gl_info as select DISTINCT t.*,t2.id gl_id ,t2.code gl_code from sly_device_gl_info_1 t
left join ${o3_res_schema}.cm_link t2 on ((t2.a_physic_device_id = t.device_id and t2.a_port_id = t.port_id) 
or (t2.z_physic_device_id = t.device_id and t2.z_port_id = t.port_id)) and t2.spec_id = 1132400006;   --光路标准域


DELETE FROM sly_device_gl_info T
 WHERE T.gl_id IS NULL
   AND EXISTS (SELECT 1  FROM sly_device_gl_info t1
         WHERE T.device_id = t1.device_id AND T.device_spec = t1.device_spec AND t1.gl_id IS NOT NULL);

--/**/
----------------------------------------------网络资源检测---------------------------------------------------

drop table sly_device_netsource_1;
create table sly_device_netsource_1 as 
select device_id from sly_device_gl_info t1  where   t1.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')  group by t1.device_id having count(distinct t1.port_code)<2;

drop table sly_device_netsource;
create table sly_device_netsource as select T.* ,'缺端口' ERROR from sly_device_gl_info T where EXISTS (select 1 from sly_device_netsource_1 t1 where t.device_id=t1.device_id) ;
--这里云设备只需要单独的设备有一个上联端口就可以，所以端口id不为空就是有一个上联端口
delete from sly_device_netsource where device_spec='云设备' and port_id is not null;


insert into sly_device_netsource
select T.* ,'缺光路' ERROR from sly_device_gl_info T
where t.device_id in(select t1.device_id from sly_device_gl_info t1 
group by t1.device_id having count (distinct t1.gl_code)<2 ) and t.device_id in(select t1.device_id from sly_device_gl_info t1 
group by t1.device_id having count (distinct t1.port_code)>=2 ) and  t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功') ;
--这里云设备只需要单独的设备有一个上联光路就可以，所以端口id不为空就是有一个上联光路
delete from sly_device_netsource where device_spec='云设备' and gl_id is not null;

--/**/


insert into sly_device_netsource
select T.* ,'光路异常' ERROR from sly_device_gl_info T
where  EXISTS(select 1 from sly_noc_err_gl t1 where t1.gl_id = t.gl_id ) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功') ;

DELETE FROM sly_device_gl_info T
 WHERE EXISTS (SELECT 1  FROM sly_device_netsource t1
         WHERE T.device_id = t1.device_id AND T.device_spec = t1.device_spec);
--同板卡
drop table IF EXISTS sly_device_gl_bk_1 ;
create table sly_device_gl_bk_1 as
select cd.*,cm.ware_id from sly_device_gl_info cd
left join   ${o3_res_schema}.cm_port cm on cm."id" = cd.port_id;
create index sly_device_gl_bk_1_1 on sly_device_gl_bk_1(ware_id);

drop table IF EXISTS CR_DEVICE_WARE_z;
create table CR_DEVICE_WARE_z as SELECT   cdw.child_id,cdw.parent_id, cw.code FROM      ${o3_res_schema}.CR_DEVICE_WARE cdw JOIN   ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id;
create index CR_DEVICE_WARE_z_1 on CR_DEVICE_WARE_z(child_id);
create index CR_DEVICE_WARE_z_2 on CR_DEVICE_WARE_z(parent_id);

drop table IF EXISTS sly_device_gl_bk;
create table sly_device_gl_bk as select t.* ,(case when cdw2.code='' then '' else cdw2.code || '/' end)||
(case when cdw1.code='' then '' else cdw1.code || '/' end)||
(case when pcdw.code='' then '' else pcdw.code end) AS bk_code from sly_device_gl_bk_1 t
left join CR_DEVICE_WARE_z pcdw on pcdw.child_id = t.ware_id 
left join CR_DEVICE_WARE_z cdw1 on cdw1.child_id = pcdw.parent_id and cdw1.child_id IS NOT NULL
left join CR_DEVICE_WARE_z cdw2 on cdw2.child_id = cdw1.parent_id and cdw2.child_id IS NOT NULL;
alter table sly_device_gl_bk drop column ware_id;









drop table IF EXISTS sly_device_gl_bk_err ;
create table sly_device_gl_bk_err as
select t.*,'无板卡' error from sly_device_gl_bk t 
where t.device_id in(
select sdgi.device_id  from sly_device_gl_bk sdgi 
where sdgi.gl_code is not null and sdgi.bk_code is null ) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功') ;

insert into sly_device_gl_bk_err
select t.*,'同板卡'  from sly_device_gl_bk t 
where t.device_id in(
select sdgi.device_id  from sly_device_gl_bk sdgi 
where sdgi.gl_id is not null
group by sdgi.device_id having count(distinct sdgi.bk_code) = 1) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'同卡板白名单') and is_white='申请白名单成功')  ;
--这里是因为云设备的单独的设备只需要一个端口，所以只有一个板卡也不属于同板卡，所以没有关于同板卡一说；
delete from sly_device_gl_bk_err where  device_spec='云设备'and error='同板卡';
--DELETE FROM sly_device_gl_info T
 --WHERE EXISTS (SELECT 1  FROM sly_device_gl_bk_err t1
    --     WHERE T.device_id = t1.device_id AND T.device_spec = t1.device_spec);
--在这里将云设备的设备id做一个更改将成对的云设备设为一组
update sly_device_gl_info set device_id=11 where ip='仪征CT云出口路由器';
update sly_device_gl_info set device_id=12 where ip='仪征CT云专线交换机';
update sly_device_gl_info set device_id=13 where ip='吉山CT云二节点出口路由器';
update sly_device_gl_info set device_id=14 where ip='吉山CT云二节点专线交换机';
update sly_device_gl_info set device_id=15 where ip='大区CT云吉山节点出口交换机';
update sly_device_gl_info set device_id=16 where ip='吉山CT云一节点出口交换机';
update sly_device_gl_info set device_id=17 where ip='吉山CT云一节点专线交换机';

----------------------------------------------波分光路分析--------------------------------------------------------
------------------------------------------------波分稽核------------------------------------------------------------------

drop table if EXISTS cm_link_gl;

drop table if EXISTS cr_link_link_t2;

drop table if EXISTS cr_link_link_t4;

 drop table if EXISTS cm_link_t13;



drop table IF EXISTS sly_dwdm_gl_no_1 ;
create table sly_dwdm_gl_no_1 as
select  t1.* ,  t3.id bf_id , t3.code bf_no   --, t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec
from sly_device_gl_info t1 
inner join ${o3_res_schema}.cm_link gl on gl.code = t1.gl_code and gl.spec_id = 1131200002
inner join ${o3_res_schema}.cr_link_link t2 on t2.lower_link_id = gl.id and t2.upper_link_spec_id = 1132100021 -- 以太网链路

inner join ${o3_res_schema}.cr_link_link t4 on t4.upper_link_id = t2.upper_link_id and t4.lower_link_spec_id = 1132300020 -- client链路
inner join ${o3_res_schema}.cm_link t3 on t3.id = t4.lower_link_id
where gl.spec_id = 1131200002 and t2.upper_link_spec_id = 1132100021 and t4.lower_link_spec_id = 1132300020; 
create index sly_dwdm_gl_no_1sy on sly_dwdm_gl_no_1(bf_id);

/*
drop table if exists sly_dwdm_gl_no_1_tmp;
create table sly_dwdm_gl_no_1_tmp as
select  t1.* ,  t3.id bf_id , t3.code bf_no   --, t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec
from sly_device_gl_info t1 
inner join res_cz_sch.cm_link gl on gl.code = t1.gl_code and gl.spec_id = 1131200002
inner join res_cz_sch.cr_link_link t2 on t2.lower_link_id = gl.id and t2.upper_link_spec_id = 1132100021 -- 以太网链路

inner join res_cz_sch.cr_link_link t4 on t4.upper_link_id = t2.upper_link_id and t4.lower_link_spec_id = 1132300020 -- client链路
inner join res_cz_sch.cm_link t3 on t3.id = t4.lower_link_id
where gl.spec_id = 1131200002 and t2.upper_link_spec_id = 1132100021 and t4.lower_link_spec_id = 1132300020; 
create index sly_dwdm_gl_no_1sy on sly_dwdm_gl_no_1(bf_id);
*/

drop table  if exists  sly_dwdm_gl_no;
create table sly_dwdm_gl_no as select    t.*,t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec from sly_dwdm_gl_no_1 t 
join ${o3_res_schema}.cr_link_link t5 on t.bf_id=t5.upper_link_id and t5.lower_link_spec_id = 1132300005 
join ${o3_res_schema}.cr_link_link t6 on t5.lower_link_id=t6.upper_link_id and t6.lower_link_spec_id = 1132200002
join ${o3_res_schema}.cr_link_link t7 on t6.lower_link_id=t7.upper_link_id and t7.lower_link_spec_id = 1132300002
join ${o3_res_schema}.cr_link_link t8 on t7.lower_link_id=t8.upper_link_id and t8.lower_link_spec_id = 1132100010
join ${o3_res_schema}.cr_link_link t9 on t8.lower_link_id=t9.upper_link_id  and t9.lower_link_spec_id = 1131200002
join ${o3_res_schema}.cr_link_link t10 on t9.lower_link_id=t10.lower_link_id  and t10.upper_link_spec_id = 1132400006
join ${o3_res_schema}.cm_link t11 on t10.upper_link_id=t11.id 
where t5.lower_link_spec_id = 1132300005  and t6.lower_link_spec_id = 1132200002  and t7.lower_link_spec_id = 1132300002 and t8.lower_link_spec_id = 1132100010 and t9.lower_link_spec_id = 1131200002 and t10.upper_link_spec_id = 1132400006;


/*
drop table if exists sly_dwdm_gl_no_tmp;
create table sly_dwdm_gl_no_tmp as select    t.*,t11.id gl_id_bf,t11.code gl_code_bf,t11.spec_id gl_spec from sly_dwdm_gl_no_1_tmp t 
join res_cz_sch.cr_link_link t5 on t.bf_id=t5.upper_link_id and t5.lower_link_spec_id = 1132300005 
join res_cz_sch.cr_link_link t6 on t5.lower_link_id=t6.upper_link_id and t6.lower_link_spec_id = 1132200002
join res_cz_sch.cr_link_link t7 on t6.lower_link_id=t7.upper_link_id and t7.lower_link_spec_id = 1132300002
join res_cz_sch.cr_link_link t8 on t7.lower_link_id=t8.upper_link_id and t8.lower_link_spec_id = 1132100010
join res_cz_sch.cr_link_link t9 on t8.lower_link_id=t9.upper_link_id  and t9.lower_link_spec_id = 1131200002
join res_cz_sch.cr_link_link t10 on t9.lower_link_id=t10.lower_link_id  and t10.upper_link_spec_id = 1132400006
join res_cz_sch.cm_link t11 on t10.upper_link_id=t11.id 
where t5.lower_link_spec_id = 1132300005  and t6.lower_link_spec_id = 1132200002  and t7.lower_link_spec_id = 1132300002 and t8.lower_link_spec_id = 1132100010 and t9.lower_link_spec_id = 1131200002 and t10.upper_link_spec_id = 1132400006;

*/


--/**/
/*inner join   ${o3_res_schema}.cr_link_link t6 on t6.upper_link_id = t4.lower_link_id;-- ODU链路
drop table IF EXISTS sly_dwdm_gl_no_1 ;
create table sly_dwdm_gl_no_1 as
select t.ip,t.city_name,t.area_name,t.device_id,t.device_code,t.device_name,t.device_spec,t.create_date,t.port_id,t.port_code,t.gl_id,t.gl_code,t.bf_id , t.bf_no,t9.lower_link_id from sly_dwdm_gl_no_11 t

inner join   ${o3_res_schema}.cr_link_link t7 on t7.upper_link_id = t.lower_link_id  -- 波链路
inner join   ${o3_res_schema}.cr_link_link t8 on t8.upper_link_id = t7.lower_link_id  -- DWDM光通道
inner join   ${o3_res_schema}.cr_link_link t9 on t9.upper_link_id = t8.lower_link_id;  -- ODU波链路
drop table IF EXISTS sly_dwdm_gl_no ;
create table sly_dwdm_gl_no as
select distinct t.ip,t.city_name,t.area_name,t.device_id,t.device_code,t.device_name,t.device_spec,t.create_date,t.port_id,t.port_code,t.gl_id,t.gl_code,t.bf_id , t.bf_no,t13.id gl_id_bf,t13.code gl_code_bf,t13.spec_id gl_spec
from sly_dwdm_gl_no_1 t
inner join   ${o3_res_schema}.cr_link_link t10 on t10.upper_link_id = t.lower_link_id -- 复用段
inner join   ${o3_res_schema}.cr_link_link t11 on t11.upper_link_id = t10.lower_link_id -- F光路
inner join   ${o3_res_schema}.cm_link t12 on t12.id=  t11.lower_link_id 
inner join cm_link_t13 t13 on t12.code = t13.code 
order by t.device_code ,t.gl_code ,t.bf_id,t13.code;*/
--/**/










-----------判断波分光路主备用情况
drop table IF EXISTS sly_dwdm_gl_no_room ;
create table sly_dwdm_gl_no_room as
select  t1.device_code,t1.gl_code,t1.bf_no,t1.gl_code_bf,t2.name a_room ,t3.name z_room
from sly_dwdm_gl_no t1
left join   ${o3_res_schema}.cm_link cl on cl.id = t1.gl_id_bf
left join   ${o3_res_schema}.cm_device t4 on t4.id = cl.a_physic_device_id
left join   ${o3_res_schema}.cm_device t5 on t5.id = cl.z_physic_device_id
left join   ${o3_res_schema}.cm_facility t2 on t2.id=  t4.facility_id
left join   ${o3_res_schema}.cm_facility t3 on t3.id=  t5.facility_id ;

drop table IF EXISTS sly_dwdm_protect;
create table sly_dwdm_protect as
select  device_code,gl_code,bf_no,'无主备' protect_type from(
select t.device_code,t.gl_code,bf_no,t.a_room room_name from sly_dwdm_gl_no_room t
union all
select t.device_code,t.gl_code,bf_no,t.z_room room_name from sly_dwdm_gl_no_room t) a
group by device_code,gl_code,bf_no,room_name
having count(*) = 1 ;

insert into sly_dwdm_protect
select  t.device_code,t.gl_code,t.bf_no ,'有主备'  from sly_dwdm_gl_no_room t
where not EXISTS(select 1 from sly_dwdm_protect t1 where t1.device_code = t.device_code and t1.gl_code = t.gl_code 
and t1.bf_no = t.bf_no) ;

------------------------OLP保护----------------------------

--传输系统光路表
drop table IF EXISTS sly_trans_system_gl ;
create table sly_trans_system_gl as
select  t.id trans_system_id,t.code trans_system_no , t.name trans_system_name, t2.code dwdm_no,t4.id gl_id ,t4.code gl_code,t4.spec_id gl_spec,
t4.a_port_id,t4.z_port_id 
from   ${o3_res_schema}.cm_net t
inner join   ${o3_res_schema}.cr_net_entity t1 on t1.net_id = t.id
inner join   ${o3_res_schema}.cm_link t2 on t2.id = t1.entity_id
inner join   ${o3_res_schema}.cr_link_link t3 on t3.upper_link_id = t2.id
inner join   ${o3_res_schema}.cm_link t4 on t4.id = t3.lower_link_id
where t.spec_id = 1212000007 ;

----传输系统光路AZ端排序
drop table IF EXISTS sly_trans_system_gl_correct ;
create table sly_trans_system_gl_correct as
select t.trans_system_id,t.trans_system_no , t.trans_system_name, t.dwdm_no,t.gl_id , t.gl_code,t.gl_spec,t.a_port_id,t.z_port_id from sly_trans_system_gl t
where t.a_port_id <= t.z_port_id
union
select t.trans_system_id,t.trans_system_no , t.trans_system_name, t.dwdm_no,t.gl_id ,t.gl_code,t.gl_spec,t.z_port_id,t.a_port_id from sly_trans_system_gl t
where t.a_port_id > z_port_id ;


--传输系统光路板卡表
drop table IF EXISTS sly_trans_system_gl_bk_1 ;
create table sly_trans_system_gl_bk_1 as
select t.*,t1.code a_port_code,
t1.ware_id a_ware_id, 
--t3.code a_bk_code,
t2.code z_port_code,
t2.ware_id z_ware_id
--,t4.code z_bk_code 
from sly_trans_system_gl_correct t
inner join   ${o3_res_schema}.cm_port t1 on t1.id = t.a_port_id
inner join   ${o3_res_schema}.cm_port t2 on t2.id = t.z_port_id;
 create index a_ware_id on sly_trans_system_gl_bk_1(a_ware_id);
  create index z_ware_id on sly_trans_system_gl_bk_1(z_ware_id);


drop table IF EXISTS sly_trans_system_gl_bk;
create table sly_trans_system_gl_bk as
select  t.trans_system_id,t.trans_system_no,t.trans_system_name,t.dwdm_no,t.gl_id,t.gl_code,t.gl_spec,t.a_port_id,t.z_port_id ,t.a_port_code,t3.code a_bk_code,t.z_port_code,t4.code z_bk_code 
from sly_trans_system_gl_bk_1 t
inner join   ${o3_res_schema}.cm_ware t3 on t3.id = t.a_ware_id 
inner join   ${o3_res_schema}.cm_ware t4 on t4.id = t.z_ware_id ;

----有OLP保护的波分底层
drop table IF EXISTS sly_dwdm_gl_olp_protect ;
create table sly_dwdm_gl_olp_protect as
select t.*,t3.gl_id protect_gl_id,t3.gl_code protect_gl_code from sly_dwdm_gl_no t
inner join sly_trans_system_gl_bk t2 on t2.gl_code = t.gl_code_bf
inner join sly_trans_system_gl_bk t3 on t2.trans_system_no = t3.trans_system_no and 
((t2.a_bk_code = t3.a_bk_code and t2.z_bk_code = t3.z_bk_code)  or (t2.a_bk_code = t3.z_bk_code and t2.z_bk_code = t3.a_bk_code)) and t3.gl_code!=t2.gl_code
where EXISTS (select 1 from sly_trans_system_gl t1 where t1.gl_code = t.gl_code_bf) ;

----这部分先比对
drop table IF EXISTS sly_olp_protect_gl_check ;
create table sly_olp_protect_gl_check as
select t.gl_code_bf gl_group,t.gl_id_bf check_id ,t.gl_code_bf check_gl from sly_dwdm_gl_olp_protect  t 
union
select t.gl_code_bf gl_group,t.protect_gl_id check_id,t.protect_gl_code check_gl from sly_dwdm_gl_olp_protect  t ;

delete from sly_gl_group_info sg where sg.group_type = 'OLP';
insert into sly_gl_group_info 
select null,sc.gl_group ,null,'OLP',sc.check_id,sc.check_gl from sly_olp_protect_gl_check sc;
commit;

call gl_cable('OLP');
call cable_zc_section('OLP');

----真正可用来剔除的OLP保护波分底层
delete from sly_dwdm_gl_olp_protect t where exists (select 1 from SLY_TWO_ROUTE t1 where t1.gl_code = gl_code_bf and t1.group_type = 'OLP') ;

----先从要稽核的波分光路中剔除有主备用的光路
delete from sly_dwdm_gl_no t where exists (select 1 from sly_dwdm_protect t1 where t1.device_code = t.device_code and 
t1.gl_code = t.gl_code and t1.bf_no = t.bf_no and t1.protect_type = '有主备') ;
commit;

----有主备的波分光路内部稽核
delete from sly_gl_group_info sg where sg.group_type = 'BFGL';
insert into sly_gl_group_info 
select null,t.bf_no ,null,'BFGL',t.gl_id,t.gl_code from sly_dwdm_gl_no t
where exists (select 1 from sly_dwdm_protect t1 where t1.device_code = t.device_code and 
t1.gl_code = t.gl_code and t1.bf_no = t.bf_no and t1.protect_type = '有主备');
commit;

call gl_cable('BFGL');
call cable_zc_section('BFGL');

----把有问题的加入到检测
insert into  sly_dwdm_gl_no(device_code,gl_code,bf_no)
select t1.device_code,t1.gl_code,t1.bf_no from sly_dwdm_protect t1 where exists ( select 1 from SLY_TWO_ROUTE t2 where  t1.bf_no = t2.group_code  )and t1.protect_type = '有主备';
---- 剔除OLP保护的
delete from sly_dwdm_gl_no t where exists(select 1 from sly_dwdm_gl_olp_protect t1 where t1.device_id = t.device_id and t1.bf_no = t.bf_no and 
t1.gl_code_bf = t.gl_code_bf) ;


----将波分光路两端F加入到波分
DROP TABLE IF EXISTS sly_dwdm_gl_no_2;
CREATE TABLE sly_dwdm_gl_no_2 AS
select  t1.* ,  t3.id bf_id , t3.code bf_no--, t6.id gl_id_bf,t6.code gl_code_bf,t6.spec_id gl_spec
from sly_device_gl_info t1 
inner join ${o3_res_schema}.cm_link gl on gl.code = t1.gl_code and gl.spec_id = 1131200002
inner join ${o3_res_schema}.cr_link_link t2 on t2.lower_link_id = gl.id and t2.upper_link_spec_id = 1132100021 -- 以太网链路
inner join ${o3_res_schema}.cm_link t3 on t3.id = t2.upper_link_id and t3.code !=gl.code
where gl.spec_id = 1131200002 and t2.upper_link_spec_id = 1132100021 ;
DROP TABLE IF EXISTS sly_dwdm_gl_no_1;
CREATE TABLE sly_dwdm_gl_no_1 AS SELECT T.*,t6.id gl_id_bf,t6.code gl_code_bf,t6.spec_id gl_spec FROM sly_dwdm_gl_no_2 T
inner join ${o3_res_schema}.cr_link_link t4 on t4.upper_link_id = t.bf_id and t4.lower_link_spec_id = 1131200002 -- 光链路路由
inner join ${o3_res_schema}.cm_link t5 on t5.id = t4.lower_link_id
inner join ${o3_res_schema}.cm_link t6 on t6.code = t5.code and t6.spec_id = 1132400006 
inner join sly_dwdm_gl_no t7 on t7.device_id = t.device_id
where t4.lower_link_spec_id = 1131200002  and t6.spec_id = 1132400006 ; -- 这里有问题 sly_dwdm_gl_no 关联没有任何作用


INSERT INTO sly_dwdm_gl_no SELECT * FROM sly_dwdm_gl_no_1;
drop table if EXISTS cm_link_gl;
drop table if EXISTS cr_link_link_t4 ;
drop table if EXISTS cm_link_t13;
drop table if EXISTS cr_link_link_t2;
----整合同属一个波分的
insert into sly_dwdm_gl_no 
select t.*,null,t.gl_code||'波分',t.gl_id,t.gl_code from sly_device_gl_info t 
where  not EXISTS (select 1 from sly_dwdm_gl_no t1 where t.device_id = t1.device_id and t.gl_code = t1.gl_code) ;


-----------------------------------------------路由资源分析---------------------------------------------------------
 drop table IF EXISTS sly_dwdm_gl_cable_1 ;
create table sly_dwdm_gl_cable_1 as
select   t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name--,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level
from sly_dwdm_gl_no t
left join    ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id_bf and cll1.spec_id=1133111310000
left join    ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
left join   ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
left join   ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
left join   ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002;  --光缆段
create index sly_dwdm_gl_cable_1sy_1 on sly_dwdm_gl_cable_1(cs_id);

 drop table IF EXISTS sly_dwdm_gl_cable ;
create table sly_dwdm_gl_cable as
select t.*,cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id ,ppr.desc_china cable_level from sly_dwdm_gl_cable_1 t
left join   ${o3_res_schema}.cr_net_entity cne on t.cs_id=cne.entity_ID
left join   ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join   ${o3_res_schema}.pm_pub_restriction ppr on ppr.serial_no = cn.net_type_id ;

delete from sly_dwdm_gl_cable t where t.cs_code is null and exists(select 1 from sly_dwdm_gl_cable t1 where t1.device_code = t.device_code and t1.gl_code = t.gl_code and  t.cs_code is not null);

drop table IF EXISTS temp_sly_dwdm_num ;
create table temp_sly_dwdm_num as
select device_id,count(distinct t.bf_no) num from sly_dwdm_gl_cable t 
group by device_id ;
 
drop table IF EXISTS temp_sly_dwdm_num2 ;
create table temp_sly_dwdm_num2 as
select device_id,cs_id,count(distinct t.bf_no) num from sly_dwdm_gl_cable t where t.cable_level <> '局内'and t.cable_level <> '联络'
group by device_id,cs_id ;

drop table IF EXISTS temp_sly_cable_err ;
create table temp_sly_cable_err as
select t.* from temp_sly_dwdm_num2 t 
inner join temp_sly_dwdm_num t1 on t1.device_id = t.device_id and t1.num = t.num ;
--这里的是通过光缆段--管孔--管道段，但是目前有两种特殊情况1.光缆段--子管孔--管孔--管道段 2.光缆段--管道段
drop table IF EXISTS sly_dwdm_gl_zc ;
create table  sly_dwdm_gl_zc as
select  t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001 --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union 
select  t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id  --子管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id 
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
UNION
select  t.*,t2.id zc_section_id, '' code,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from sly_dwdm_gl_cable t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id !=1111100001  --去掉管孔
--left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id;




create index device_id99 on sly_dwdm_gl_zc(device_id);
create index cs_id99 on sly_dwdm_gl_zc(cs_id);
create index zc_section_id99 on sly_dwdm_gl_zc(zc_section_id);

delete from sly_dwdm_gl_zc t where cs_id is null;
delete from sly_dwdm_gl_zc t where z_zc_eqp_code is null AND a_zc_eqp_code is null AND  zc_section_id  IS NOT NULL ;

delete from sly_dwdm_gl_zc t where T.zc_section_id is null and exists
(select 1 from sly_dwdm_gl_zc T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);


drop table IF EXISTS sly_dwdm_gl_route_err ; --SELECT DEVICE_CODE FROM sly_dwdm_gl_route_err GROUP BY DEVICE_CODE
create table  sly_dwdm_gl_route_err as
select T.* , '无穿管' ERROR from sly_dwdm_gl_zc T 
where T.zc_section_id is null and T.Cable_Level<> '局内' and T.Cable_Level<> '联络' and not exists
(select 1 from sly_dwdm_gl_zc T1 where T.device_id = T1.device_id  and T1.cs_id = T.cs_id and T1.zc_section_id is not null);
delete from sly_dwdm_gl_route_err where DEVICE_CODE IN  (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功') ;
--删除光缆级别为楼间的无穿管。
delete from sly_dwdm_gl_route_err where cable_level='楼间'; 
--发现存在光缆属性不是局内，但光缆段属性是局内。所以在此做一个剔除；
delete from sly_dwdm_gl_route_err t where error='无穿管' and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );




DELETE FROM sly_dwdm_gl_zc WHERE zc_section_id is null;

delete from sly_dwdm_gl_zc t where z_zc_eqp_code is null or a_zc_eqp_code is null;

drop table IF EXISTS temp_sly_dwdm_num3 ;
create table temp_sly_dwdm_num3 as
select t.device_id , t.zc_section_code,count(distinct t.gl_code) num from sly_dwdm_gl_zc t
where t.cable_level <> '局内'
group by t.device_id , t.zc_section_code;

drop table IF EXISTS temp_sly_zc_err ;
create table temp_sly_zc_err as
select  t.* from temp_sly_dwdm_num3 t
inner join temp_sly_dwdm_num t1 on t1.device_id = t.device_id and t1.num = t.num  ;

insert into sly_dwdm_gl_route_err 
select t.*,'同管道'  from sly_dwdm_gl_zc t
where EXISTS (select 1 from temp_sly_zc_err t1 where t1.device_id = t.device_id and t1.zc_section_code = t.zc_section_code) 
and not EXISTS (select 1 from temp_sly_cable_err t1 where t1.device_id = t.device_id and t1.cs_id = t.cs_id) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单') and is_white='申请白名单成功') ;

insert into sly_dwdm_gl_route_err (ip,city_name,area_name,device_id,device_code,device_name,device_spec,create_date,port_id,port_code,gl_id,gl_code,bf_id,bf_no,gl_id_bf,gl_code_bf,gl_spec,cs_id,cs_code,cs_name,cable_id,cable_code,cable_name,spec_id,cable_level,error)
select t.*,'同光缆'  from sly_dwdm_gl_cable t
where EXISTS (select 1 from temp_sly_cable_err t1 where t1.device_id = t.device_id and t1.cs_id = t.cs_id) and t.device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单')and is_white='申请白名单成功' );

drop table IF EXISTS device_white;
create table device_white as
select  t.group_name group_code,t.bse_eqp_no eqp1 from sly_sys_white_config t where t.function_type = '单设备';

drop table IF EXISTS device_white1;
create table device_white1 as
select  t.group_code,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from device_white t 
left join sly_dwdm_gl_zc t1 on t.eqp1 = t1.a_zc_eqp_code and t.group_code = t1.device_code
union
select  t.group_code,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from device_white t 
left join sly_dwdm_gl_zc t1 on t.eqp1 = t1.z_zc_eqp_code  and t.group_code = t1.device_code;

drop table IF EXISTS device_white2;
create table device_white2 as
select  t.group_code,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from device_white1 t 
left join sly_dwdm_gl_zc t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from device_white1 t 
left join sly_dwdm_gl_zc t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white3;
create table device_white3 as
select  t.group_code,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from device_white2 t 
left join sly_dwdm_gl_zc t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from device_white2 t 
left join sly_dwdm_gl_zc t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white4;
create table device_white4 as
select  t.group_code,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from device_white3 t 
left join sly_dwdm_gl_zc t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from device_white3 t 
left join sly_dwdm_gl_zc t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white5;
create table device_white5 as
select  t.group_code,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from device_white4 t 
left join sly_dwdm_gl_zc t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from device_white4 t 
left join sly_dwdm_gl_zc t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white6;
create table device_white6 as
select  t.group_code,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from device_white5 t 
left join sly_dwdm_gl_zc t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from device_white5 t 
left join sly_dwdm_gl_zc t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white7;
create table device_white7 as
select  t.group_code,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from device_white6 t 
left join sly_dwdm_gl_zc t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from device_white6 t 
left join sly_dwdm_gl_zc t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS device_white8;
create table device_white8 as
select  t.group_code,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from device_white7 t 
left join sly_dwdm_gl_zc t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from device_white7 t 
left join sly_dwdm_gl_zc t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
--/**/
drop table IF EXISTS device_white9;
create table device_white9 as
select  t.group_code,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from device_white8 t 
left join sly_dwdm_gl_zc t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from device_white8 t 
left join sly_dwdm_gl_zc t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
drop table IF EXISTS device_white10;
create table device_white10 as
select  t.group_code,t.eqp10,t1.z_zc_eqp_code eqp11,t1.zc_section_code 
from device_white9 t 
left join sly_dwdm_gl_zc t1 on t.eqp10 = t1.a_zc_eqp_code and t.eqp9 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp10,t1.a_zc_eqp_code eqp11,t1.zc_section_code
from device_white9 t 
left join sly_dwdm_gl_zc t1 on t.eqp10 = t1.z_zc_eqp_code and t.eqp9 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;drop table IF EXISTS device_white11;
create table device_white11 as
select  t.group_code,t.eqp11,t1.z_zc_eqp_code eqp12,t1.zc_section_code 
from device_white10 t 
left join sly_dwdm_gl_zc t1 on t.eqp11 = t1.a_zc_eqp_code and t.eqp10 <> t1.z_zc_eqp_code and t.group_code = t1.device_code 
where t1.z_zc_eqp_code is not null
union
select  t.group_code,t.eqp11,t1.a_zc_eqp_code eqp12,t1.zc_section_code
from device_white10 t 
left join sly_dwdm_gl_zc t1 on t.eqp11 = t1.z_zc_eqp_code and t.eqp10 <> t1.a_zc_eqp_code and t.group_code = t1.device_code where t1.z_zc_eqp_code is not null;
--/**/
drop table IF EXISTS device_white_list;
create table device_white_list as
select t.group_code,t.eqp1 zc_eqp_no from device_white t
union 
select t1.group_code,t1.eqp2 from device_white1 t1
union 
select t2.group_code,t2.eqp3 from device_white2 t2
union 
select t3.group_code,t3.eqp4 from device_white3 t3
union 
select t4.group_code,t4.eqp5 from device_white4 t4
union 
select t5.group_code,t5.eqp6 from device_white5 t5
union 
select t6.group_code,t6.eqp7 from device_white6 t6
union 
select t7.group_code,t7.eqp8 from device_white7 t7
union 
select t8.group_code,t8.eqp9 from device_white8 t8
union 
select t9.group_code,t9.eqp10 from device_white9 t9
union 
select t10.group_code,t10.eqp11 from device_white10 t10
union 
select t11.group_code,t11.eqp12 from device_white11 t11;

delete from sly_dwdm_gl_route_err t where exists(select 1 from device_white_list t1 where t.device_code = t1.group_code and (t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code)) and exists (select 1 from ${o3_res_schema}.cm_facility t1  where notes like '%单路由%' and (t1.code = t.a_zc_eqp_code or t1.code = t.z_zc_eqp_code) );

---之前逻辑需要在稽核时删除进线室，但常州删除进线室会导致部分局前井失效，所以在检测之后将出现同管道的进线室删除掉。
delete from sly_dwdm_gl_route_err t WHERE (z_zc_eqp_code LIKE '%进线室%' OR A_zc_eqp_code LIKE '%进线室%') AND ERROR ='同管道';



-----------------------------------统计数据----------------------------------------剔除白名单
delete from  sly_deivice_tj_info t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_deivice_tj_info (city_name,area_name,device_spec,device_num,create_time)
select t.city_name ,area_name,device_spec,count(1),to_char(CURRENT_DATE,'yyyy-mm-dd')  from (select * from sly_device_base_info t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
group by t.city_name ,t.area_name,t.device_spec;

--/**/
update sly_deivice_tj_info t set port_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺端口' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set port_err = 0 where port_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t
where error = '缺光路' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err1 = 0 where gl_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set gl_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '光路异常' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set gl_err2 = 0 where gl_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set bk_err1 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单' and is_white='申请白名单成功')) t 
where error = '无板卡' group by CITY_NAME, AREA_NAME, device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err1 = 0 where bk_err1 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
--/**/
update sly_deivice_tj_info t set bk_err2 = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_device_gl_bk_err  t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'同卡板白名单') and is_white='申请白名单成功')) t 
where error = '同板卡' group by CITY_NAME, AREA_NAME, device_spec)n  
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set bk_err2 = 0 where bk_err2 is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set cs_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单')and is_white='申请白名单成功' )) t 
where error = '同光缆' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set cs_err = 0 where cs_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_deivice_tj_info t set zc_null = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '无穿管' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_null = 0 where zc_null is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

--/**/

update sly_deivice_tj_info t set zc_err = n.num 
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t 
where error = '同管道' group by CITY_NAME, AREA_NAME, device_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set zc_err = 0 where zc_err is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info t set Dual_routing = n.num
from (SELECT    CITY_NAME, AREA_NAME, device_spec, COUNT(DISTINCT device_id) AS num FROM 
(select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_device_netsource t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type='设备白名单'and is_white='申请白名单成功')) t
UNION
select CITY_NAME, AREA_NAME,device_id,device_spec,create_date from (select * from sly_dwdm_gl_route_err t where device_code not in (select phy_eqp_no from MID_NOC_WHITE_LIST_PHY where white_type in ('设备白名单' ,'双路由白名单') and is_white='申请白名单成功')) t) a
 group by a.CITY_NAME, a.AREA_NAME, a.device_spec)n
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.device_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_deivice_tj_info set Dual_routing = 0 where Dual_routing is null and create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
--这个sql里面用到两个函数，里面的表数据在运行完清空防止因为数据量过大影响其他脚本。
delete from sly_two_gl_group_cable sg where sg.group_type = 'OLP';
delete from sly_two_eqp_glnum sc where sc.group_type = 'OLP';
delete from sly_two_section_glnum sc where sc.group_type = 'OLP';
delete from SLY_TWO_SECTION_ERR_TMP sc where sc.group_type = 'OLP';
delete from SLY_TWO_SECTION_ERR sc where sc.group_type = 'OLP';
delete from sly_two_cable_zc_section sc where sc.group_type = 'OLP';
delete from SLY_TWO_ROUTE sc where sc.group_type = 'OLP';
delete from sly_two_eqp_zc_glnum sc where sc.group_type = 'OLP';
delete from sly_two_zc_section_glnum sc where sc.group_type = 'OLP';
delete from SLY_TWO_ZC_ERROR sc where sc.group_type = 'OLP';


delete from sly_two_gl_group_cable sg where sg.group_type = 'BFGL';
delete from sly_two_eqp_glnum sc where sc.group_type = 'BFGL';
delete from sly_two_section_glnum sc where sc.group_type = 'BFGL';
delete from SLY_TWO_SECTION_ERR_TMP sc where sc.group_type = 'BFGL';
delete from SLY_TWO_SECTION_ERR sc where sc.group_type = 'BFGL';
delete from sly_two_cable_zc_section sc where sc.group_type = 'BFGL';
delete from SLY_TWO_ROUTE sc where sc.group_type = 'BFGL';
delete from sly_two_eqp_zc_glnum sc where sc.group_type = 'BFGL';
delete from sly_two_zc_section_glnum sc where sc.group_type = 'BFGL';
delete from SLY_TWO_ZC_ERROR sc where sc.group_type = 'BFGL';
drop table if exists cm_link_t2;
drop table IF EXISTS sly_device_gl_info_1 ;

drop table sly_device_netsource_1;

drop table IF EXISTS sly_device_gl_bk_1 ;
drop table IF EXISTS CR_DEVICE_WARE_z;
drop table IF EXISTS sly_device_gl_bk;


drop table if EXISTS cm_link_gl;

drop table if EXISTS cr_link_link_t2;
drop table if EXISTS cr_link_link_t4;
drop table if EXISTS cm_link_t13;
 drop table IF EXISTS sly_dwdm_gl_no_1 ;
 drop table  if exists  sly_dwdm_gl_no;
 drop table IF EXISTS sly_dwdm_gl_no_1 ;
 drop table IF EXISTS sly_dwdm_gl_no_room ;
 drop table IF EXISTS sly_dwdm_protect;
 drop table IF EXISTS sly_trans_system_gl ;
 drop table IF EXISTS sly_trans_system_gl_correct ;
 drop table IF EXISTS sly_trans_system_gl_bk_1 ;
 drop table IF EXISTS sly_trans_system_gl_bk;
 drop table IF EXISTS sly_dwdm_gl_olp_protect ;
 drop table IF EXISTS sly_olp_protect_gl_check ;
 DROP TABLE IF EXISTS sly_dwdm_gl_no_2;
 DROP TABLE IF EXISTS sly_dwdm_gl_no_1;
drop table if EXISTS cm_link_gl;
drop table if EXISTS cr_link_link_t4 ;
drop table if EXISTS cm_link_t13;
drop table if EXISTS cr_link_link_t2;
drop table IF EXISTS sly_dwdm_gl_cable_1 ;
--drop table IF EXISTS sly_dwdm_gl_cable ;
drop table IF EXISTS temp_sly_dwdm_num ;
drop table IF EXISTS temp_sly_dwdm_num2 ;
drop table IF EXISTS temp_sly_cable_err ;

drop table IF EXISTS temp_sly_dwdm_num3 ;
drop table IF EXISTS temp_sly_zc_err ;
drop table IF EXISTS device_white;
drop table IF EXISTS device_white1;
drop table IF EXISTS device_white2;
drop table IF EXISTS device_white3;
drop table IF EXISTS device_white4;
drop table IF EXISTS device_white5;
drop table IF EXISTS device_white6;
drop table IF EXISTS device_white7;
drop table IF EXISTS device_white8;
drop table IF EXISTS device_white9;
drop table IF EXISTS device_white10;
drop table IF EXISTS device_white11;
drop table IF EXISTS device_white_list;