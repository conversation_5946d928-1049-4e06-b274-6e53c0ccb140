DROP TABLE if exists MID_XHJ_TRANS_DWDM_GL ;--select TRANS_SYSTEM_ID from MID_XHJ_TRANS_DWDM_GL GROUP BY TRANS_SYSTEM_ID limit 100
CREATE TABLE MID_XHJ_TRANS_DWDM_GL--select DISTINCT ID from  ${o3_res_schema}.CM_NET  where SPEC_ID = 1212000007 
AS 
SELECT '${areaName}' city_name,
T.ID TRANS_SYSTEM_ID,  --传输系统ID
       SA.id AREA_ID,
       SA.NAME           AS AREA_NAME,
       SA.PARENT_ID PARENT_AREA_ID,
       T.NAME            AS TRANS_NAME, --传输系统名称
       T.CODE              AS TRANS_NO,  --传输系统编码
       LBL.id AS GL_ID,
       LBL.NAME         AS GL_NAME,
       LBL.code           AS GL_NO,
       PR.DESC_CHINA AS PROTECT_TYPE, -- 保护类型规格名称
       LBL.a_physic_device_id   A_DEVICE_ID,  --A端设备ID
       LBL.z_physic_device_id  Z_DEVICE_ID,  --Z端设备ID
			 lbl.a_port_id,
			  lbl.z_port_id,
       ALE.code  AS ALE_NO, --A端逻辑设备编码
       ALP.code  AS ALP_NO, --A端端口编码
       APRS.res_type AS ALE_SPEC, --A端规格编码
       ZLE.code  AS ZLE_NO,  --Z端逻辑设备编码
       ZLP.code  AS ZLP_NO,  --Z端端口编码
       ZPRS.res_type AS ZLE_SPEC  --Z端规格编码
  FROM ${o3_res_schema}.CM_NET T  --传输系统  SELECT * FROM ${o3_res_schema}.CM_NET LIMIT 100
  LEFT JOIN ${o3_res_schema}.CR_NET_ENTITY LTS  --传输段  select * from ${o3_res_schema}.CR_NET_ENTITY  limit 100
    ON T.ID = LTS.net_id 
  LEFT JOIN ${o3_res_schema}.cm_link LBL  --业务通道
    ON LTS.entity_id = LBL.id
  LEFT JOIN ${o3_res_schema}.RM_AREA SA
    ON T.LEAF_REGION_ID = SA.ID
  LEFT JOIN ${o3_res_schema}.pm_pub_restriction PR  --数据字典
    ON T.PROTECT_WAY_ID = PR.SERIAL_NO
  --LEFT JOIN LNK_BUSI_LINK_2_LINK L2L  --业务通道与链路关系表
    --ON LBL.BUSI_LINK_ID = LBL.id
  LEFT JOIN ${o3_res_schema}.cm_device ALE  --直接关联逻辑设备
    ON LBL.a_physic_device_id = ALE.id
  LEFT JOIN ${o3_res_schema}.cm_port ALP  --逻辑端口
    ON LBL.A_PORT_ID = ALP.ID
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type APRS  --资源规格select * from ${o3_res_schema}.pm_pub_res_type limit 100
    ON APRS.RES_type_ID = ALE.SPEC_ID
  LEFT JOIN ${o3_res_schema}.cm_device ZLE
    ON LBL.z_physic_device_id = ZLE.ID
  LEFT JOIN ${o3_res_schema}.cm_port ZLP
    ON LBL.Z_PORT_ID = ZLP.ID
  LEFT JOIN ${o3_res_schema}.pm_pub_res_type ZPRS
    ON ZPRS.RES_type_ID = ZLE.SPEC_ID
 WHERE T.SPEC_ID = 1212000007
   AND T.PROTECT_WAY_ID NOT IN (81720253, 101611, 81705057)
   AND ( T.NETWORK_LAYER_ID <>81730625 OR T.NETWORK_LAYER_ID IS NULL );--SELECT * FROM ${o3_res_schema}.pm_pub_restriction WHERE DESC_CHINA like 'dwdm'
 
CREATE INDEX MID_XHJ_TRANS_DWDM_GL_TID ON MID_XHJ_TRANS_DWDM_GL(TRANS_SYSTEM_ID);
CREATE INDEX MID_XHJ_TRANS_DWDM_GL_GID ON MID_XHJ_TRANS_DWDM_GL(GL_ID);

drop table if exists MID_XHJ_TRANS_DWDM_GLerr;--select * from MID_XHJ_TRANS_DWDM_GLerr
create table MID_XHJ_TRANS_DWDM_GLerr as
select t.*,'传输段异常' err from MID_XHJ_TRANS_DWDM_GL t where exists (select 1 from MID_XHJ_TRANS_DWDM_GL where t.TRANS_SYSTEM_ID=TRANS_SYSTEM_ID and GL_ID IS NOT NULL AND PROTECT_TYPE <> 'OLP保护'  GROUP BY TRANS_SYSTEM_ID HAVING COUNT(1) <= 1);
--OLP保护的DWDM清单A/Z端设备ID和端口
DROP TABLE TMP_XHJ_TRANS_DWDM_GL2_2 ;--select * from TMP_XHJ_TRANS_DWDM_GL2_2
CREATE TABLE TMP_XHJ_TRANS_DWDM_GL2_2
AS
SELECT T.TRANS_SYSTEM_ID, --传输系统ID
       t.gl_id BUSI_LINK_ID, --支撑通道ID
       t.A_DEVICE_ID,  --A端设备ID
       t.A_PORT_ID,    --A端端口ID
       t.Z_DEVICE_ID,   --Z端设备ID
       t.Z_PORT_ID
  FROM MID_XHJ_TRANS_DWDM_GL T  --DWDM清单表
  --LEFT JOIN ${o3_res_schema}.CR_NET_ENTITY LTS  --传输段
    --ON T.TRANS_SYSTEM_ID = LTS.TRANS_SYSTEM_ID
  --LEFT JOIN LNK_BUSINESS_LINK LBL --业务通道
   -- ON LTS.BUSI_LINK_ID = LBL.BUSI_LINK_ID
--  LEFT JOIN LNK_BUSI_LINK_2_LINK L2L  --业务通道与链路关系
  --  ON LBL.BUSI_LINK_ID = L2L.BUSI_LINK_ID
  WHERE  T.PROTECT_TYPE = 'OLP保护';
	
	--对A/Z端设备进行去重
DROP TABLE TMP_XHJ_TRANS_DWDM_GL2_3 ;--select * from TMP_XHJ_TRANS_DWDM_GL2_3
CREATE TABLE TMP_XHJ_TRANS_DWDM_GL2_3
AS  
SELECT DISTINCT TMP.TRANS_SYSTEM_ID,
                TMP.A_DEVICE_ID,
                TMP.Z_DEVICE_ID,
                COUNT(DISTINCT TMP.BUSI_LINK_ID) AS NUM
  FROM (SELECT DISTINCT T.TRANS_SYSTEM_ID,
                        T.A_DEVICE_ID,
                        T.Z_DEVICE_ID,
                        T.BUSI_LINK_ID
          FROM TMP_XHJ_TRANS_DWDM_GL2_2 T       
        UNION
        SELECT DISTINCT T.TRANS_SYSTEM_ID,
                        T.Z_DEVICE_ID,
                        T.A_DEVICE_ID,
                        T.BUSI_LINK_ID
          FROM TMP_XHJ_TRANS_DWDM_GL2_2 T) TMP
 GROUP BY TMP.TRANS_SYSTEM_ID, TMP.A_DEVICE_ID, TMP.Z_DEVICE_ID;

insert into MID_XHJ_TRANS_DWDM_GLerr 
select t.*,'传输段异常' err from MID_XHJ_TRANS_DWDM_GL t where exists (select 1 from MID_XHJ_TRANS_DWDM_GL where t.TRANS_SYSTEM_ID=TRANS_SYSTEM_ID and GL_ID IS NOT NULL AND PROTECT_TYPE = 'OLP保护'  GROUP BY TRANS_SYSTEM_ID HAVING COUNT(1) in (1,3,5,7,9,11,13,15,17,19,21));

--找到OLT保护两两相对的光路信息
DROP TABLE MID_XHJ_TRANS_DWDM_GL3 ;--select * from MID_XHJ_TRANS_DWDM_GL3
CREATE TABLE MID_XHJ_TRANS_DWDM_GL3
AS
 SELECT DISTINCT T.TRANS_SYSTEM_ID,
                 T.GL_NO,
                 T.GL_ID,
                 T.A_DEVICE_ID,
                 T.Z_DEVICE_ID
   FROM MID_XHJ_TRANS_DWDM_GL T
  WHERE EXISTS (SELECT 1
           FROM TMP_XHJ_TRANS_DWDM_GL2_3 
          WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
            AND T.A_DEVICE_ID = A_DEVICE_ID
            AND T.Z_DEVICE_ID = Z_DEVICE_ID
            AND NUM IN (2, 4, 6, 8, 10));
<#if areaCode= 'lyg'> 
----对于连云港，查出dwdm系统的F光路，并判断是否为单纤单向，如果是单纤场景，
----就把能找到配对光路的这些波分段更新为波分段组（波分段组就是其中一个波分段）
drop table trans_dwdm_gl ;--SELECT * FROM trans_dwdm_gl  SELECT * FROM MID_XHJ_TRANS_DWDM_GL
create table trans_dwdm_gl as
select distinct t.trans_system_id,t.gl_id trans_section_id ,t.gl_name trans_section_name,t.gl_no trans_section_no,t4.ID gl_id, t4.CODE gl_no,
t4.spec_id gl_res_id from MID_XHJ_TRANS_DWDM_GL t 
inner join RES_LYG_SCH.cr_link_link  t1 on t1.upper_link_id = t.gl_id
inner join RES_LYG_SCH.cm_link t3 on t3.ID = t1.lower_link_id


inner join RES_LYG_SCH.cm_link t4 on t3.CODE = T4.CODE
--inner join lnk_busi_link_2_link t4 on t4.link_id = t3.link_id
--inner join lnk_business_link t5 on t5.busi_link_id = t4.busi_link_id 
where  t4.spec_id = 1132400006 --光纤光路 
order by t.gl_no;
--SELECT * FROM RES_LYG_SCH.CM_LINK WHERE CODE='F1510220490'
--单纤光路
drop table trans_dwdm_gl_dx ;
create table trans_dwdm_gl_dx as
select t.trans_system_id,t.trans_section_id,t.trans_section_no,t.gl_id from trans_dwdm_gl t
inner join RES_LYG_SCH.cm_link  t1 on t1.id = t.gl_id 
group by t.trans_system_id,t.trans_section_id,t.trans_section_no,t.gl_id having count(*) = 1;

--查询单纤光路两端的设备
drop table trans_dwdm_gl_dx_eqp ;--SELECT * FROM trans_dwdm_gl_dx_eqp
create table trans_dwdm_gl_dx_eqp as
select t.trans_system_id,t.trans_section_id,t.trans_section_no,t.gl_id,t1.a_physic_device_id  A_device_id,t1.Z_physic_device_id z_device_id from trans_dwdm_gl_dx t
inner join RES_LYG_SCH.CM_LINK t1 on t1.ID = t.gl_id
left join RES_LYG_SCH.cm_device pe1 on pe1.id = t1.a_physic_device_id
left join RES_LYG_SCH.cm_device pe2 on pe2.id = t1.Z_physic_device_id ;

--给单纤光路找配对光路
drop table trans_dwdm_gl_dx_pair ;--SELECT * FROM trans_dwdm_gl_dx_pair
create table trans_dwdm_gl_dx_pair as
select t.*,t1.gl_id pair_gl_id,t1.trans_section_id pair_trans_section_id from trans_dwdm_gl_dx_eqp t 
left join trans_dwdm_gl_dx_eqp t1 on t1.trans_system_id = t.trans_system_id 
and ((t.a_device_id = t1.a_device_id and t.z_device_id = t1.z_device_id) 
or (t.a_device_id = t1.z_device_id and t.z_device_id = t1.a_device_id))
and t.gl_id!=t1.gl_id 
where t1.gl_id is not null;

--给这些能找到配对光路的传输段设定传输段组（传输段id小的那个）
drop table trans_dwdm_gl_dx_group ;--SELECT * FROM trans_dwdm_gl_dx_pair
create table trans_dwdm_gl_dx_group as
select t.*,t1.gl_no trans_section_no_group  from trans_dwdm_gl_dx_pair t 
inner join MID_XHJ_TRANS_DWDM_GL t1 on t1.gl_id = least(t.trans_section_id,t.pair_trans_section_id);

 </#if>


DROP TABLE  IF EXISTS TMP_XHJ_TRANS_DWDM_CABLE;--select * from TMP_XHJ_TRANS_DWDM_CABLE
create table  TMP_XHJ_TRANS_DWDM_CABLE as
select distinct t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name, cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id,cn.net_type_id,pp.desc_china cable_level  from MID_XHJ_TRANS_DWDM_GL t
inner join ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id 
inner join ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id 
inner join ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
inner join ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
inner join ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  --光缆段
inner join ${o3_res_schema}.cr_net_entity cne on cs.id=cne.entity_ID
inner join ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join ${o3_res_schema}.pm_pub_restriction pp on pp.serial_no = cn.net_type_id;
CREATE INDEX TMP_XHJ_TRANS_D_CABLE_ID ON TMP_XHJ_TRANS_DWDM_CABLE(TRANS_SYSTEM_ID);
CREATE INDEX TMP_XHJ_TRANS_D_CABLE_GL ON TMP_XHJ_TRANS_DWDM_CABLE(GL_NO);
CREATE INDEX TMP_XHJ_TRANS_D_CABLE_SD ON TMP_XHJ_TRANS_DWDM_CABLE(cs_id);
CREATE INDEX TMP_XHJ_TRANS_D_CABLE_CI ON TMP_XHJ_TRANS_DWDM_CABLE(cable_code);

 <#if areaCode= 'lyg'> 

update   TMP_XHJ_TRANS_DWDM_CABLE  t set gl_no = 
(select t1.trans_section_no_group from trans_dwdm_gl_dx_group t1 where t1.trans_system_id = t.trans_system_id and t1.trans_section_no = t.gl_no)
where exists(select 1 from trans_dwdm_gl_dx_group t2 where t2.trans_system_id = t.trans_system_id
and t2.trans_section_no = t.gl_no);
commit;


 </#if>


delete from TMP_XHJ_TRANS_DWDM_CABLE where CABLE_LEVEL in ('局内', '联络');
drop table if exists TMP_XHJ_TRANS_DWDM_CABLEerr;--select * from TMP_XHJ_TRANS_DWDM_CABLEerr
create table TMP_XHJ_TRANS_DWDM_CABLEerr as select t.*,'双路由缆段重复'err from TMP_XHJ_TRANS_DWDM_CABLE t where  exists(select 1 from TMP_XHJ_TRANS_DWDM_CABLE where  t.TRANS_SYSTEM_ID=TRANS_SYSTEM_ID and t.cs_id=cs_id and PROTECT_TYPE <> 'OLP保护' GROUP BY TRANS_SYSTEM_ID, cs_id
        HAVING COUNT( DISTINCT gl_no) > 1 ) ;
				
				
				
DROP TABLE TMP_XHJ_TRANS_DWDM_CABLE1 ;--select * from TMP_XHJ_TRANS_DWDM_CABLE1;
CREATE TABLE TMP_XHJ_TRANS_DWDM_CABLE1
AS
SELECT DISTINCT T.*
  FROM MID_XHJ_TRANS_DWDM_GL3 TMP  --OLT保护两两相对的光路信息
  JOIN TMP_XHJ_TRANS_DWDM_CABLE T
    ON T.TRANS_SYSTEM_ID = TMP.TRANS_SYSTEM_ID
   AND T.GL_NO = TMP.GL_NO  ;
--select * from    TMP_XHJ_TRANS_DWDM_CABLE_CRR 
insert into  TMP_XHJ_TRANS_DWDM_CABLEerr

SELECT t.*,'双路由缆段重复' err
  FROM  TMP_XHJ_TRANS_DWDM_CABLE1 T
 WHERE EXISTS (SELECT 1
          FROM  TMP_XHJ_TRANS_DWDM_CABLE1 
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND T.A_DEVICE_ID = A_DEVICE_ID
           AND T.Z_DEVICE_ID = Z_DEVICE_ID
           AND T.GL_ID <> GL_ID
           AND T.cs_id = cs_id)
 UNION 
 SELECT t.*,'双路由缆段重复' err
  FROM  TMP_XHJ_TRANS_DWDM_CABLE1 T
 WHERE EXISTS (SELECT 1
          FROM  TMP_XHJ_TRANS_DWDM_CABLE1 
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND T.A_DEVICE_ID = Z_DEVICE_ID
           AND T.Z_DEVICE_ID = A_DEVICE_ID
           AND T.GL_ID <> GL_ID
           AND cs_id = T.cs_id);



drop table IF EXISTS TMP_XHJ_TRANS_DWDM_ZC_KPI ;--select * from TMP_XHJ_TRANS_DWDM_ZC_KPI
create table  TMP_XHJ_TRANS_DWDM_ZC_KPI as
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from TMP_XHJ_TRANS_DWDM_CABLE t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001 --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
union 
select distinct t.*,t5.id zc_section_id,t2.code,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from TMP_XHJ_TRANS_DWDM_CABLE t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id  --子管孔
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id !=1111100001  --去掉管孔
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id 
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id
UNION
select distinct t.*,t2.id zc_section_id, '' code,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code 
from TMP_XHJ_TRANS_DWDM_CABLE t
left join   ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id --光缆段
left join   ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id !=1111100001  --去掉管孔
--left join   ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id !=1111100001
left join   ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join   ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id;
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_KPI_csid ON TMP_XHJ_TRANS_DWDM_ZC_KPI(cs_id);
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_KPI_zc ON TMP_XHJ_TRANS_DWDM_ZC_KPI(zc_section_code);

--如果管道只连接一个人井可不纳入稽核，并且把两端都没有人井但却有管道段编码的排除掉(两段都无人井应该是关联到了管孔之类的)
delete from TMP_XHJ_TRANS_DWDM_ZC_KPI where zc_section_code is not null and (z_zc_eqp_code is null or a_zc_eqp_code is null );

DELETE FROM TMP_XHJ_TRANS_DWDM_ZC_KPI T WHERE zc_section_code IS  NULL AND  EXISTS (SELECT 1 from TMP_XHJ_TRANS_DWDM_ZC_KPI T1 WHERE T.CS_ID=T1.CS_ID AND T1.zc_section_code IS  NOT NULL limit 1);

DROP TABLE TMP_XHJ_TRANS_DWDM_ZC_KPIERR;
CREATE TABLE TMP_XHJ_TRANS_DWDM_ZC_KPIERR AS--SELECT * FROM TMP_XHJ_TRANS_DWDM_ZC_KPIERR
SELECT t.* ,'无穿管'err from TMP_XHJ_TRANS_DWDM_ZC_KPI T where zc_section_code IS  NULL;
delete from TMP_XHJ_TRANS_DWDM_ZC_KPI where zc_section_code IS  NULL ;



--剔除地下进线室
DELETE FROM TMP_XHJ_TRANS_DWDM_ZC_KPI --SELECT * FROM 
 where z_zc_eqp_code like '%进线室%' or a_zc_eqp_code like '%进线室%'  ;
commit;





drop table TMP_TRANS_DWDM_ZC3_KPI_CXR;
create table TMP_TRANS_DWDM_ZC3_KPI_CXR as--select * from TMP_TRANS_DWDM_ZC3_KPI_CXR
select DISTINCT t.TRANS_SYSTEM_ID,t.GL_NO, t.zc_section_code,t.a_zc_eqp_code  zc_eqp_code,notes  from TMP_XHJ_TRANS_DWDM_ZC_KPI t join ${o3_res_schema}.cm_facility t1 on t1.code=t.a_zc_eqp_code 
union 
select DISTINCT t.TRANS_SYSTEM_ID,t.GL_NO, t.zc_section_code,t.z_zc_eqp_code  zc_eqp_code,notes  from TMP_XHJ_TRANS_DWDM_ZC_KPI t join ${o3_res_schema}.cm_facility t1 on t1.code=t.z_zc_eqp_code; 

DROP TABLE TMP_TRANS_DWDM_ZC3_KPI_CXR_1 ;
CREATE TABLE TMP_TRANS_DWDM_ZC3_KPI_CXR_1
AS
 SELECT DISTINCT TRANS_SYSTEM_ID, GL_NO,zc_eqp_code
   FROM TMP_TRANS_DWDM_ZC3_KPI_CXR T
  where t.NOTES Like '%单路由%'
  GROUP BY TRANS_SYSTEM_ID, GL_NO,zc_eqp_code
 HAVING COUNT(DISTINCT zc_section_code) = 1;

<#if areaCode = 'nj'>
insert into TMP_TRANS_DWDM_ZC3_KPI_CXR_1
select t.phy_eqp_id,null gl_no,t.bse_eqp_no from sly_sys_white_config t where t.white_type = '局前井白名单' and t.function_type = 'DWDM传输系统' ;
</#if>
--增加白名单

drop table IF EXISTS device_whitedwdm;
drop table IF EXISTS device_whitedwdm1;
drop table IF EXISTS device_whitedwdm2;
drop table IF EXISTS device_whitedwdm3;
drop table IF EXISTS device_whitedwdm4;
drop table IF EXISTS device_whitedwdm5;
drop table IF EXISTS device_whitedwdm6;
drop table IF EXISTS device_whitedwdm7;
drop table IF EXISTS device_whitedwdm8;
drop table IF EXISTS device_whitedwdm9;
drop table IF EXISTS device_whitedwdm_list;

create table device_whitedwdm as
select distinct t.TRANS_SYSTEM_ID group_code,t.zc_eqp_code eqp1 from TMP_TRANS_DWDM_ZC3_KPI_CXR_1 t ;


create table device_whitedwdm1 as
select distinct t.group_code,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from device_whitedwdm t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp1 = t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID
union
select distinct t.group_code,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from device_whitedwdm t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp1 = t1.z_zc_eqp_code  and t.group_code = t1.TRANS_SYSTEM_ID;


create table device_whitedwdm2 as
select distinct t.group_code,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from device_whitedwdm1 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from device_whitedwdm1 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null;


create table device_whitedwdm3 as
select distinct t.group_code,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from device_whitedwdm2 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from device_whitedwdm2 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;


create table device_whitedwdm4 as
select distinct t.group_code,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from device_whitedwdm3 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from device_whitedwdm3 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;


create table device_whitedwdm5 as
select distinct t.group_code,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from device_whitedwdm4 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from device_whitedwdm4 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;


create table device_whitedwdm6 as
select distinct t.group_code,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from device_whitedwdm5 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from device_whitedwdm5 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;


create table device_whitedwdm7 as
select distinct t.group_code,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from device_whitedwdm6 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from device_whitedwdm6 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;


create table device_whitedwdm8 as
select distinct t.group_code,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from device_whitedwdm7 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from device_whitedwdm7 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;
--/**/
drop table IF EXISTS device_whitedwdm9;
create table device_whitedwdm9 as
select distinct t.group_code,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from device_whitedwdm8 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from device_whitedwdm8 t 
left join TMP_XHJ_TRANS_DWDM_ZC_KPI t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.group_code = t1.TRANS_SYSTEM_ID where t1.z_zc_eqp_code is not null;
--/**/

drop table IF EXISTS device_whitedwdm_list;
create table device_whitedwdm_list as
select t.group_code,t.eqp1 zc_eqp_no from device_whitedwdm t
union 
select t1.group_code,t1.eqp2 from device_whitedwdm1 t1
union 
select t2.group_code,t2.eqp3 from device_whitedwdm2 t2
union 
select t3.group_code,t3.eqp4 from device_whitedwdm3 t3
union 
select t4.group_code,t4.eqp5 from device_whitedwdm4 t4
union 
select t5.group_code,t5.eqp6 from device_whitedwdm5 t5
union 
select t6.group_code,t6.eqp7 from device_whitedwdm6 t6
union 
select t7.group_code,t7.eqp8 from device_whitedwdm7 t7
union 
select t8.group_code,t8.eqp9 from device_whitedwdm8 t8
union 
select t9.group_code,t9.eqp10 from device_whitedwdm9 t9;

DELETE FROM TMP_XHJ_TRANS_DWDM_ZC_KPI T WHERE EXISTS (SELECT 1 FROM device_whitedwdm_list T1 WHERE T1.group_code=T.TRANS_SYSTEM_ID AND T1.zc_eqp_no=T.a_zc_eqp_code ) OR EXISTS (SELECT 1 FROM device_whitedwdm_list T1 WHERE T1.group_code=T.TRANS_SYSTEM_ID AND T1.zc_eqp_no=T.Z_zc_eqp_code );

CREATE INDEX TMP_XHJ_TRANS_D_ZC2_KPI_ID ON TMP_XHJ_TRANS_DWDM_ZC_KPI(TRANS_SYSTEM_ID);
CREATE INDEX TMP_XHJ_TRANS_D_ZC2_KPI_SE ON TMP_XHJ_TRANS_DWDM_ZC_KPI(CS_ID);
CREATE INDEX TMP_XHJ_TRANS_D_ZC2_KPI_ZCNO ON TMP_XHJ_TRANS_DWDM_ZC_KPI(zc_section_code);
CREATE INDEX TMP_XHJ_TRANS_D_ZC2_KPI_GLNO ON TMP_XHJ_TRANS_DWDM_ZC_KPI(GL_NO);

--不为OLP保护时大于1的
DROP TABLE TMP_XHJ_TRANS_DWDM_ZCID_KPI ;--SELECT TRANS_SYSTEM_ID FROM TMP_XHJ_TRANS_DWDM_ZCID_KPI GROUP BY TRANS_SYSTEM_ID
CREATE TABLE TMP_XHJ_TRANS_DWDM_ZCID_KPI
AS
SELECT TMP.TRANS_SYSTEM_ID, TMP.zc_section_code
  FROM (SELECT DISTINCT T.TRANS_SYSTEM_ID, T.GL_NO, T.zc_section_id,T.zc_section_code
          FROM TMP_XHJ_TRANS_DWDM_ZC_KPI T
         WHERE T.PROTECT_TYPE <> 'OLP保护') TMP
 GROUP BY TMP.TRANS_SYSTEM_ID, TMP.zc_section_id,TMP.zc_section_code
HAVING COUNT( DISTINCT GL_NO) > 1;
CREATE INDEX TMP_XHJ_TRANS_D_ZCID_KPI_ID ON TMP_XHJ_TRANS_DWDM_ZCID_KPI(TRANS_SYSTEM_ID);
CREATE INDEX TMP_XHJ_TRANS_D_ZCID_SENO ON TMP_XHJ_TRANS_DWDM_ZCID_KPI(zc_section_code);

--创建临时表(重复支撑管道率)
--找到管线重复，排除缆段重复
DROP TABLE IF EXISTS TMP_XHJ_TRANS_DWDM_ZC_KPIERR1;--SELECT TRANS_NAME FROM TMP_XHJ_TRANS_DWDM_ZC_KPIERR1 GROUP BY TRANS_NAME
 CREATE TABLE TMP_XHJ_TRANS_DWDM_ZC_KPIERR1 AS 
 SELECT t.*,'同管道' err FROM TMP_XHJ_TRANS_DWDM_ZC_KPI  T WHERE EXISTS (SELECT 1 FROM TMP_XHJ_TRANS_DWDM_ZC_KPI  WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID AND T.A_DEVICE_ID = A_DEVICE_ID AND T.Z_DEVICE_ID = Z_DEVICE_ID AND T.GL_ID <> GL_ID AND zc_section_id = T.zc_section_id) UNION SELECT t.*,'同管道' err FROM TMP_XHJ_TRANS_DWDM_ZC_KPI  T WHERE EXISTS (SELECT 1 FROM TMP_XHJ_TRANS_DWDM_ZC_KPI  WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID AND T.A_DEVICE_ID = Z_DEVICE_ID AND T.Z_DEVICE_ID = A_DEVICE_ID AND T.GL_ID <> GL_ID AND zc_section_id = T.zc_section_id);
  insert into TMP_XHJ_TRANS_DWDM_ZC_KPIERR SELECT * FROM TMP_XHJ_TRANS_DWDM_ZC_KPIERR1;

--OLP情形
DROP TABLE TMP_XHJ_TRANS_DWDM_ZC ;
CREATE TABLE TMP_XHJ_TRANS_DWDM_ZC
AS
SELECT DISTINCT T.*
  FROM MID_XHJ_TRANS_DWDM_GL3 TMP
  JOIN TMP_XHJ_TRANS_DWDM_ZC_KPI T
    ON T.TRANS_SYSTEM_ID = TMP.TRANS_SYSTEM_ID
   AND T.GL_NO = TMP.GL_NO;

CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_sy1 ON TMP_XHJ_TRANS_DWDM_ZC(TRANS_SYSTEM_ID);	 
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_sy2 ON TMP_XHJ_TRANS_DWDM_ZC(A_DEVICE_ID);	 
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_sy3 ON TMP_XHJ_TRANS_DWDM_ZC(Z_DEVICE_ID);
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_sy4 ON TMP_XHJ_TRANS_DWDM_ZC(GL_ID);
CREATE INDEX TMP_XHJ_TRANS_DWDM_ZC_sy5 ON TMP_XHJ_TRANS_DWDM_ZC(zc_section_code);

	 
	drop table if exists TMP_XHJ_TRANS_DWDM_ZC_KPIERR2;
create table TMP_XHJ_TRANS_DWDM_ZC_KPIERR2 as
SELECT t.*,'同管道' err
  FROM TMP_XHJ_TRANS_DWDM_ZC T
 WHERE EXISTS (SELECT 1
          FROM TMP_XHJ_TRANS_DWDM_ZC
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND T.A_DEVICE_ID = A_DEVICE_ID
           AND T.Z_DEVICE_ID = Z_DEVICE_ID
           AND T.GL_ID <> GL_ID
           AND zc_section_code = T.zc_section_code)
 UNION 
 SELECT t.*,'同管道' err
  FROM TMP_XHJ_TRANS_DWDM_ZC T
 WHERE EXISTS (SELECT 1
          FROM TMP_XHJ_TRANS_DWDM_ZC
         WHERE T.TRANS_SYSTEM_ID = TRANS_SYSTEM_ID
           AND T.A_DEVICE_ID = Z_DEVICE_ID
           AND T.Z_DEVICE_ID = A_DEVICE_ID
           AND T.GL_ID <> GL_ID
           AND zc_section_code = T.zc_section_code);
DELETE FROM TMP_XHJ_TRANS_DWDM_ZC_KPIERR T WHERE  EXISTS (
SELECT 1 FROM TMP_XHJ_TRANS_DWDM_CABLEerr T2 where T.TRANS_SYSTEM_ID=T2.TRANS_SYSTEM_ID
);

 insert into TMP_XHJ_TRANS_DWDM_ZC_KPIERR  select t.*,'同管道'err from TMP_XHJ_TRANS_DWDM_ZC_KPI  t join  TMP_XHJ_TRANS_DWDM_ZCID_KPI t1
 on t.zc_section_code=t1.zc_section_code and t.trans_system_id=t1.trans_system_id ;

insert into TMP_XHJ_TRANS_DWDM_ZC_KPIERR select * from TMP_XHJ_TRANS_DWDM_ZC_KPIERR2;--select trans_name from TMP_XHJ_TRANS_DWDM_ZC_KPIERR group by trans_name
COMMIT ;
	



delete from  sly_dwdm_tj_info t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_dwdm_tj_info (city_name,device_num,create_time)
select t.city_name ,count(DISTINCT TRANS_SYSTEM_ID),to_char(CURRENT_DATE,'yyyy-mm-dd')  from (select * from MID_XHJ_TRANS_DWDM_GL t ) t
group by t.city_name ;

update sly_dwdm_tj_info t set gl_err1=n.num from (select CITY_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from MID_XHJ_TRANS_DWDM_GLerr group by CITY_NAME ) n where t.city_name=n.city_name and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_dwdm_tj_info t set gl_err1=0 where gl_err1 is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_dwdm_tj_info t set cs_err=n.num from (select CITY_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from TMP_XHJ_TRANS_DWDM_CABLEerr group by CITY_NAME ) n where t.city_name=n.city_name and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_dwdm_tj_info t set cs_err=0 where cs_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_dwdm_tj_info t set zc_err=n.num from (select CITY_NAME,count(DISTINCT TRANS_SYSTEM_ID)  num from TMP_XHJ_TRANS_DWDM_ZC_KPIERR group by CITY_NAME ) n where t.city_name=n.city_name and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_dwdm_tj_info t set zc_err=0 where zc_err is null and t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');



