drop table if exists  sly_eqp_jq;
create table sly_eqp_jq as select city_name,bse_eqp_no,group_name device_no from sly_sys_white_config;

 create index sly_eqp_jq1 on sly_eqp_jq(bse_eqp_no);
  create index sly_eqp_jq2 on sly_eqp_jq(device_no);
	drop table if exists station_bse_white_list1;
	create table  station_bse_white_list1  as select t.city_name,t.bse_eqp_no,t2.name bse_eqp_name,t.device_no,t1.name device_name ,t1.tml_id,t2.ID FACILITY_ID from sly_eqp_jq t
	join ${o3_res_schema}.cm_device t1 on t.device_no=t1.code
	join ${o3_res_schema}.cm_facility t2 on t.bse_eqp_no=t2.code;
	create index station_bse_white_list1_1 on station_bse_white_list1(FACILITY_ID);

drop table if exists station_bse_white_list;--select * from station_bse_white_list
create table station_bse_white_list as select DISTINCT t.* ,t3.NAME AS STATION_NAME,t4.device_spec,t5.res_type,t6.name device_STATION_NAME from station_bse_white_list1 t  join ${o3_res_schema}.CM_FACILITY t2 on  t.facility_id=t2.id join ${o3_res_schema}.RM_AREA t3 on t2.TML_ID = t3.ID join sly_device_base_info t4  on t.device_no=t4.device_code  join ${o3_res_schema}.pm_pub_res_type t5 on t2.spec_id=t5.res_type_id join ${o3_res_schema}.RM_AREA t6 on t.TML_ID = t6.ID ;