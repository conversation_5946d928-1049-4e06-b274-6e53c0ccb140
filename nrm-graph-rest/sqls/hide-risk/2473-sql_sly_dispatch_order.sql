------------------------------------------正式脚本----------------------------------------------------------------
----BAS SR ER OLT2000
insert into sly_dispatch_order(city_name,area_name,device_id,device_code,device_spec,major_id,insert_time)
select t.city_name,t.area_name,t.device_id,t.device_code ,t.device_spec,'3',CURRENT_DATE from sly_device_base_info t
where not EXISTS (select 1 from sly_dispatch_order t1 where t1.city_name = t.city_name and t1.device_id = t.device_id) 
and t.device_spec in('路由器','宽带接入设备');


insert into sly_dispatch_order(city_name,area_name,device_id,device_code,device_spec,major_id,insert_time)
select t.city_name,t.area_name,t.device_id,t.device_code,t.device_spec,'3',CURRENT_DATE from sly_device_base_info t
inner join ywkt_olt_user_num t2 on t2.olt_id = t.device_id and t2.user_num >= 2000
where not EXISTS (select 1 from sly_dispatch_order t1 where t1.city_name = t.city_name and t1.device_id = t.device_id) 
and t.device_spec in('OLT设备');

----已经不在检测范围的
update sly_dispatch_order t set order_status = 1
where not EXISTS (select 1 from sly_device_base_info t1 where t1.city_name = t.city_name and t1.device_id = t.device_id) ;

--设备白名单的
update sly_dispatch_order t set order_status = 2
where EXISTS (select 1 from sly_sys_white_config t1 where t1.city_name = t.city_name and t1.group_name = t.device_code 
and t1.function_type='设备白名单') ;

----更新各个标志位
update sly_dispatch_order t  set netresource = 1 
where EXISTS (select 1 from sly_device_netsource t1 where t1.city_name = t.city_name and t1.device_id = t.device_id );

update sly_dispatch_order t  set bk = 1 
where EXISTS (select 1 from sly_device_gl_bk_err t1 where t1.city_name = t.city_name and t1.device_id = t.device_id );

update sly_dispatch_order t  set cable_same = 1 
where EXISTS (select 1 from sly_dwdm_gl_route_err t1 where t1.city_name = t.city_name and t1.device_id = t.device_id 
and t1.error = '同光缆');

update sly_dispatch_order t  set pipe_null = 1 
where EXISTS (select 1 from sly_dwdm_gl_route_err t1 where t1.city_name = t.city_name and t1.device_id = t.device_id 
and t1.error = '无穿管');

update sly_dispatch_order t  set pipe_same = 1 
where EXISTS (select 1 from sly_dwdm_gl_route_err t1 where t1.city_name = t.city_name and t1.device_id = t.device_id 
and t1.error = '同管道');

----更新数量

update sly_dispatch_order t  set cable_same_num = n.num
from (select t1.city_name , device_id , count(distinct t1.cable_id) num from sly_dwdm_gl_route_err t1 
where  t1.error = '同光缆' group by t1.city_name , device_id) n
where  n.city_name = t.city_name and n.device_id = t.device_id  ;

update sly_dispatch_order t  set pipe_null_num = n.num
from (select t1.city_name , device_id , count(distinct t1.cable_id) num from sly_dwdm_gl_route_err t1 
where  t1.error = '无穿管' group by t1.city_name , device_id) n
where  n.city_name = t.city_name and n.device_id = t.device_id  ;

update sly_dispatch_order t  set pipe_same_num = n.num
from (select t1.city_name , device_id , count(distinct t1.zc_section_id) num from sly_dwdm_gl_route_err t1 
where  t1.error = '同管道' group by t1.city_name , device_id) n
where  n.city_name = t.city_name and n.device_id = t.device_id  ;


----更新内容
update sly_dispatch_order t set netresource_err = n.err
from 
(select t1.city_name ,t1.device_id,string_agg(t1.error,',') err from sly_device_netsource t1 group by t1.city_name,t1.device_id )n 
where t.city_name = n.city_name and t.device_id = n.device_id ;


update sly_dispatch_order t set bk_err = n.err
from 
(select t1.city_name ,t1.device_id,string_agg(t1.error,',') err from sly_device_gl_bk_err t1 group by t1.city_name,t1.device_id )n 
where t.city_name = n.city_name and t.device_id = n.device_id ;

update sly_dispatch_order t set cable_same_err = n.err
from 
(select t1.city_name ,t1.device_id,substring('【同光缆异常】光路'||string_agg(t1.gl_code,',')||'在'||string_agg(t1.cs_code,',') ||'上同光缆',1,4000) err from sly_dwdm_gl_route_err t1 where t1.error = '同光缆' group by t1.city_name,t1.device_id )n 
where t.city_name = n.city_name and t.device_id = n.device_id ;

update sly_dispatch_order t set pipe_null_err = n.err
from 
(select t1.city_name ,t1.device_id,substring('【无穿管异常】'||string_agg(t1.gl_code||' - '||t1.cs_code,','),1,4000) err from sly_dwdm_gl_route_err t1 where t1.error = '无穿管' group by t1.city_name,t1.device_id )n 
where t.city_name = n.city_name and t.device_id = n.device_id ;

update sly_dispatch_order t set pipe_same_err = n.err
from 
(select t1.city_name ,t1.device_id,substring('【同管道异常】光路'||string_agg(t1.gl_code,',')||'在'||string_agg(t1.zc_section_code,',') ||'上同管道', 1,4000) err from sly_dwdm_gl_route_err t1 where t1.error = '同管道' group by t1.city_name,t1.device_id )n 
where t.city_name = n.city_name and t.device_id = n.device_id ;


-----更新工单状态
update sly_dispatch_order t set has_err = '1' where t.netresource = '1' or cable_same = '1' or pipe_null = '1' or pipe_same = '1' ;
update sly_dispatch_order t set has_err = '0' where t.netresource = '0' and  cable_same = '0' and pipe_null = '0' and pipe_same = '0' ;