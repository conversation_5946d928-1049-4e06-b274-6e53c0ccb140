
CREATE TABLE if not exists  TMP_XHJ_PHY_ZC_EQPALL(--select * from TMP_XHJ_PHY_ZC_EQPALL
  "city_name" varchar(200) COLLATE "pg_catalog"."default",
  "ip" varchar(200) COLLATE "pg_catalog"."default",
  "phy_eqp_code" varchar(200) COLLATE "pg_catalog"."default",
  "gl_no2" varchar(200) COLLATE "pg_catalog"."default",
  "bse_eqp_code" varchar(200) COLLATE "pg_catalog"."default"
)
;

CREATE TABLE if not exists gl_group_sly_detection_new (--select * from gl_group_sly_detection_new
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "gl_group_name" varchar(500) COLLATE "pg_catalog"."default",
  "business_type" varchar(100) COLLATE "pg_catalog"."default",
  "user_name" varchar(100) COLLATE "pg_catalog"."default",
  "business_no" varchar(100) COLLATE "pg_catalog"."default",
  "gl_no" varchar(300) COLLATE "pg_catalog"."default",
  "create_date" date
)
;




CREATE TABLE if not exists SLY_GL_GROUP_ROUTE_JS (--select * from SLY_GL_GROUP_ROUTE_JS
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "gl_group_name" varchar(500) COLLATE "pg_catalog"."default",
  "business_type" varchar(100) COLLATE "pg_catalog"."default",
  "user_name" varchar(100) COLLATE "pg_catalog"."default",
  "business_no" varchar(100) COLLATE "pg_catalog"."default",
  "gl_no" varchar(300) COLLATE "pg_catalog"."default",
  "create_date" date,
  "id" numeric(24),
  "cs_id" numeric(24),
  "cs_code" varchar(255) COLLATE "pg_catalog"."default",
  "cs_name" varchar(255) COLLATE "pg_catalog"."default",
  "fiber_id" numeric(24),
  "fiber_code" varchar(255) COLLATE "pg_catalog"."default",
  "fiber_name" varchar(255) COLLATE "pg_catalog"."default",
  "cable_id" numeric(24),
  "cable_code" varchar(255) COLLATE "pg_catalog"."default",
  "cable_name" varchar(255) COLLATE "pg_catalog"."default",
  "spec_id" int8,
  "net_type_id" int4,
  "cable_level" varchar(80) COLLATE "pg_catalog"."default",
  "zc_section_id" numeric(24),
  "zc_section_code" varchar(255) COLLATE "pg_catalog"."default",
  "zc_section_name" varchar(255) COLLATE "pg_catalog"."default",
  "a_zc_eqp_code" varchar(255) COLLATE "pg_catalog"."default",
  "z_zc_eqp_code" varchar(255) COLLATE "pg_catalog"."default",
  "error" text COLLATE "pg_catalog"."default",
  "update_time" date,
  "is_type" text COLLATE "pg_catalog"."default"
)
;





CREATE TABLE if not exists SLY_GL_GROUP_GL_ERR_JS (--select * from SLY_GL_GROUP_GL_ERR_JS
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "gl_group_name" varchar(500) COLLATE "pg_catalog"."default",
  "business_type" varchar(100) COLLATE "pg_catalog"."default",
  "user_name" varchar(100) COLLATE "pg_catalog"."default",
  "business_no" varchar(100) COLLATE "pg_catalog"."default",
  "gl_no" varchar(300) COLLATE "pg_catalog"."default",
  "create_date" date,
  "id" numeric(24),
  "error" text COLLATE "pg_catalog"."default",
  "update_time" date,
  "is_type" text COLLATE "pg_catalog"."default"
)
;






CREATE TABLE if not exists SLY_GL_GROUP_ROUTE_TJ ( --select * from SLY_GL_GROUP_ROUTE_TJ
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "business_type" varchar(100) COLLATE "pg_catalog"."default",
  "num0" int8,
  "num1" int8,
  "num2" int8,
  "num3" int8,
  "num4" int8,
  "update_time" date
)
;



CREATE TABLE if not exists gl_group_sly_detection_new_js (  --select * from gl_group_sly_detection_new_js
  "city_name" varchar(100) COLLATE "pg_catalog"."default",
  "gl_group_name" varchar(500) COLLATE "pg_catalog"."default",
  "business_type" varchar(100) COLLATE "pg_catalog"."default",
  "user_name" varchar(100) COLLATE "pg_catalog"."default",
  "business_no" varchar(100) COLLATE "pg_catalog"."default",
  "gl_no" varchar(300) COLLATE "pg_catalog"."default",
  "create_date" date,
  "update_time" date
)
;






CREATE TABLE if not exists sly_sys_white_gl_group
(
  white_id      numeric,
  city_name     VARCHAR(255),
  phy_eqp_id    numeric,
  phy_eqp_name  VARCHAR(200),
  phy_eqp_no    VARCHAR(200),
  all_num       numeric,
  bse_eqp_id    numeric,
  bse_eqp_no    VARCHAR(200),
  bse_type      VARCHAR(255),
  zc_section_id numeric,
  zc_section_no VARCHAR(255),
  create_time   DATE default clock_timestamp()::timestamp(0) without time zone,
  staff_name    VARCHAR(50),
  is_detele     DATE,
  del_username  VARCHAR(50),
  function_type VARCHAR(50),
  plan1         VARCHAR(50),
  plan2         VARCHAR(50),
  group_name    VARCHAR(200)
);
