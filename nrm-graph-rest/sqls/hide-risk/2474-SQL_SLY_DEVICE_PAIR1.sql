drop table IF EXISTS sly_pair_device_list_1 ;--select * from sly_pair_device_list_1
create table sly_pair_device_list_1 as
select distinct --t2.name city_name,t3.name area_name,
t.b_pair,k.ip device_ip,--rr.entity_id ,
--cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,
k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b1_ip 
	and k.area_name = t.p_area 
	and k.eqp_spec = t.eqp_spec
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
--left join  ${o3_res_schema}.cm_device cd on cd.id = rr.entity_id 
--left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
--left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备')
union
select distinct --t2.name city_name,t3.name area_name,
t.b_pair,k.ip device_ip,--rr.entity_id ,
--cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,
k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b2_ip 
	and k.area_name = t.p_area 
	and k.eqp_spec = t.eqp_spec
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
--left join  ${o3_res_schema}.cm_device cd on cd.id = rr.entity_id 
--left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
--left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备');
 create index device_ip on sly_pair_device_list_1(device_ip);

drop table if exists sly_pair_device_list_2;
create table sly_pair_device_list_2 as
select distinct t.b_pair,t.device_ip,t.eqp_spec,rr.phy_eqp_id entity_id  from sly_pair_device_list_1 t
left join ${o3_odso_schema}.res_phy_dev_daily rr on t.device_ip =rr.ip_addr;
--left join  ${o3_res_schema}.rm_number r on t.device_ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id;


 create index entity_id11 on sly_pair_device_list_2(entity_id);


drop table if exists sly_pair_device_list;
create table sly_pair_device_list as
select distinct t2.name city_name,t3.name area_name,t.b_pair,t.device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,t.eqp_spec from sly_pair_device_list_2 t
left join  ${o3_res_schema}.cm_device cd on cd.id = t.entity_id 
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id ;
--南京额外添加几台设备
<#if areaCode= 'nj'> 
insert into sly_pair_device_list
select '南京'as city_name,area_name,b_pair,device_ip,t1.id as device_id,device_code,device_name,t.create_date,eqp_spec from sly_pair_device_list_tmp t left join res_nj_sch.cm_device t1 on t.device_code=t1.code ;
 </#if>
insert into sly_pair_device_list
select distinct t2.name city_name,t3.name area_name,K.EQP_SPEC || '：' || K.IP b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id 
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('B设备' , 'ITV设备')
and not exists (select 1
   from mid_noc_sly_eqp_pair
   where (k.ip = b1_ip or k.ip = b2_ip)
   and CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = p_area
	 and eqp_spec = k.eqp_spec);
commit;
------------DR设备--------------------
insert into sly_pair_device_list
select distinct t2.name city_name,t3.name area_name,t.b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b1_ip 
	and k.area_name = t.p_area 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('DR设备','DSW设备','BSC CE')
union
select distinct t2.name city_name,t3.name area_name,t.b_pair,k.ip device_ip,cd.id device_id,cd.code device_code,cd.name device_name,cd.create_date,k.eqp_spec
from mid_noc_sly_eqp_list k 
join mid_noc_sly_eqp_pair t 
on k.ip = t.b2_ip 
	and k.area_name = t.p_area 
left join ${o3_odso_schema}.res_phy_dev_daily rr on k.ip =rr.ip_addr
--left join  ${o3_res_schema}.rm_number r on k.ip = r.code
--left join  ${o3_res_schema}.rr_number_entity rr on rr.number_id = r.id 
left join  ${o3_res_schema}.cm_device cd on cd.id = rr.phy_eqp_id
left join  ${o3_res_schema}.rm_area t2 on t2.id = cd.region_id 
left join  ${o3_res_schema}.rm_area t3 on t3.id = cd.leaf_region_id 
where
CASE k.area_name WHEN '江苏省中心' THEN '南京' ELSE k.area_name END = '${areaName}'
and k.eqp_spec in ('DR设备','DSW设备','BSC CE');
-- 去掉无效数据
DELETE FROM sly_pair_device_list T
 WHERE T.device_code IS NULL
   AND EXISTS (SELECT 1
          FROM sly_pair_device_list
         WHERE device_code IS NOT NULL
           AND T.device_ip = device_ip);
COMMIT;

------------ 设备到光路 100362：上联 100366：下联 100363： 上下联 ---------------------------
drop table IF EXISTS sly_pair_gl_info;
create table sly_pair_gl_info as
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code from sly_pair_device_list t 
left join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id  and t1.up_down_id = 100362
left join ${o3_res_schema}.cm_link t2 on t2.a_physic_device_id = t.device_id and t2.a_port_id = t1.id and t2.spec_id = 1132400006 
where   (t2.notes not like '%逃生通道%' or t2.notes is null)
union
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code from sly_pair_device_list t 
left join ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id  and t1.up_down_id = 100362
left join ${o3_res_schema}.cm_link t2 on t2.z_physic_device_id = t.device_id and t2.z_port_id = t1.id and  t2.spec_id = 1132400006
where    (t2.notes not like '%逃生通道%' or t2.notes is null);
drop table if exists sly_pair_gl_info_2;
create table sly_pair_gl_info_2 as select * from sly_pair_gl_info;
drop table IF EXISTS sly_pair_gl_info1;
create table sly_pair_gl_info1 as
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code,t2.a_physic_device_id,t2.z_physic_device_id from sly_pair_device_list t 
left join  ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join  ${o3_res_schema}.cm_link t2 on t2.a_physic_device_id = t.device_id and t2.a_port_id = t1.id and t1.up_down_id = 100363 and t2.spec_id = 1132400006
union
select t.*,t1.id port_id,t1.code port_code,t2.id gl_id,t2.code gl_code,t2.a_physic_device_id,t2.z_physic_device_id from sly_pair_device_list t 
left join  ${o3_res_schema}.cm_port t1 on t1.physic_device_id = t.device_id 
left join  ${o3_res_schema}.cm_link t2 on t2.z_physic_device_id = t.device_id and t2.z_port_id = t1.id and t1.up_down_id = 100363 and t2.spec_id = 1132400006;
drop table IF EXISTS sly_pair_gl_info2;
create table sly_pair_gl_info2 as
select * from sly_pair_gl_info1 t1 where t1.device_id = t1.a_physic_device_id 
and  exists(select 1 from sly_pair_device_list t where t.b_pair = t1.b_pair and t.device_id = t1.z_physic_device_id)
union
select * from sly_pair_gl_info1 t1 where t1.device_id = t1.z_physic_device_id 
and  exists(select 1 from sly_pair_device_list t where t.b_pair = t1.b_pair and t.device_id = t1.a_physic_device_id);
insert into sly_pair_gl_info
select distinct t.city_name,t.area_name,t.b_pair,t.device_ip,t.device_id,t.device_code,t.device_name,t.create_date,t.eqp_spec,t.port_id,t.port_code,t.gl_id,t.gl_code from sly_pair_gl_info2 t;
drop table IF EXISTS sly_pair_device_netsource;
create table sly_pair_device_netsource as
select T.* ,'缺端口' ERROR from sly_pair_gl_info T
where t.device_id in 
(select t1.device_id from sly_pair_gl_info t1 
group by t1.device_id having count(distinct t1.port_code)<2);
insert into sly_pair_device_netsource
select T.* ,'缺光路' ERROR from sly_pair_gl_info T
where t.device_id in(select t1.device_id from sly_pair_gl_info t1 
group by t1.device_id having count (distinct t1.gl_code)<2 ) and t.device_id in(select t1.device_id from SLY_CN_BASE_INFO t1 
group by t1.device_id having count (distinct t1.port_code)>=2 );
commit;
--同板卡
drop table IF EXISTS sly_pair_gl_bk ;
create table sly_pair_gl_bk as
select cd.*,
(case when cdw2.code='' then '' else cdw2.code || '/' end)||
(case when cdw1.code='' then '' else cdw1.code || '/' end)||
(case when pcdw.code='' then '' else pcdw.code end) AS bk_code
from sly_pair_gl_info cd
left join  ${o3_res_schema}.cm_port cm on cm."id" = cd.port_id
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) pcdw on pcdw.child_id = cm.ware_id 
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw1 on cdw1.child_id = pcdw.parent_id and cdw1.child_id IS NOT NULL
left join (SELECT	cdw.child_id,cdw.parent_id, cw.code FROM	 ${o3_res_schema}.CR_DEVICE_WARE cdw	JOIN  ${o3_res_schema}.cm_ware cw ON cw.ID = cdw.child_id) cdw2 on cdw2.child_id = cdw1.parent_id and cdw2.child_id IS NOT NULL;
drop table IF EXISTS sly_pair_gl_bk_err ;
create table sly_pair_gl_bk_err as
select t.* from sly_pair_gl_bk t 
where t.device_id in(
select sdgi.device_id  from sly_pair_gl_bk sdgi 
group by sdgi.device_id having count(distinct sdgi.bk_code) = 1) and  t.device_id in  (select device_id from sly_pair_device_netsource t  where t.error = '缺光路') ;

delete from sly_three_gl_group_info t where t.group_type = '设备对';
insert into sly_three_gl_group_info (group_code,device_id,device_code,device_name,group_type,gl_id,gl_code,eqp_spec)
select t.b_pair,t.device_id,t.device_code,t.device_name,'设备对',t.gl_id,t.gl_code,t.eqp_spec from sly_pair_gl_info t;

call sly_three_route('设备对');
DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR_1 ;
COMMIT;
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR_1 --select * from TMP_PHYPAIR_ZC_EQP_CXR_1
AS
 SELECT DISTINCT t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.a_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.B_PAIR,zc_section_code BSE_SECT_ID

   FROM sly_three_cable_zc_section T--管道人井表
	 join  sly_pair_gl_info  t3 on t.group_code=t3.b_pair and t.gl_code=t3.gl_code and t.group_type='设备对' 
	 --join ${o3_res_schema}.cm_device t1 on t1.code=t.group_code 
	 join ${o3_res_schema}.cm_facility t2 on t.a_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id
 union
  SELECT DISTINCT t3.device_id LGC_EQP_ID,t.gl_code GL_NO2,t2.id BSE_EQP_ID,T.z_zc_eqp_code BEQ_NO,t4.res_type BSE_SPEC,T3.B_PAIR,zc_section_code BSE_SECT_ID
   FROM sly_three_cable_zc_section T--管道人井表
	 join  sly_pair_gl_info  t3 on t.group_code=t3.b_pair and t.gl_code=t3.gl_code and t.group_type='设备对' 
	 --join ${o3_res_schema}.cm_device t1 on t1.code=t.group_code 
	 join ${o3_res_schema}.cm_facility t2 on t.z_zc_eqp_code=t2.code and t2.notes like'%单路由局前井%'
	 join ${o3_res_schema}.pm_pub_res_type t4 on t4.res_type_id=t2.spec_id;
	 DROP TABLE if exists TMP_PHYPAIR_ZC_EQP_CXR ;
COMMIT;
CREATE TABLE TMP_PHYPAIR_ZC_EQP_CXR --select * from TMP_PHYPAIR_ZC_EQP_CXR_1
AS
SELECT DISTINCT LGC_EQP_ID,GL_NO2,BSE_EQP_ID, BEQ_NO, BSE_SPEC,B_PAIR

   FROM TMP_PHYPAIR_ZC_EQP_CXR_1 T
  GROUP BY LGC_EQP_ID, GL_NO2, BSE_EQP_ID, BSE_SPEC,T.B_PAIR,T.BEQ_NO
 HAVING COUNT(DISTINCT BSE_SECT_ID) = 1;
 COMMIT;


drop table IF EXISTS white;
create table white as
select distinct t.group_name group_code,t.bse_eqp_no eqp1 from sly_sys_white_config t where t.function_type = '设备对'
union
select  distinct  B_PAIR,BEQ_NO from TMP_PHYPAIR_ZC_EQP_CXR ;;

drop table IF EXISTS white1;
create table white1 as
select distinct t.group_code,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from white t 
left join sly_three_cable_zc_section t1 on t.eqp1 = t1.a_zc_eqp_code and t.group_code = t1.group_code
union
select distinct t.group_code,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from white t 
left join sly_three_cable_zc_section t1 on t.eqp1 = t1.z_zc_eqp_code  and t.group_code = t1.group_code;

drop table IF EXISTS white2;
create table white2 as
select distinct t.group_code,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from white1 t 
left join sly_three_cable_zc_section t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from white1 t 
left join sly_three_cable_zc_section t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white3;
create table white3 as
select distinct t.group_code,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from white2 t 
left join sly_three_cable_zc_section t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from white2 t 
left join sly_three_cable_zc_section t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white4;
create table white4 as
select distinct t.group_code,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from white3 t 
left join sly_three_cable_zc_section t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from white3 t 
left join sly_three_cable_zc_section t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white5;
create table white5 as
select distinct t.group_code,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from white4 t 
left join sly_three_cable_zc_section t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from white4 t 
left join sly_three_cable_zc_section t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white6;
create table white6 as
select distinct t.group_code,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from white5 t 
left join sly_three_cable_zc_section t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from white5 t 
left join sly_three_cable_zc_section t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white7;
create table white7 as
select distinct t.group_code,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from white6 t 
left join sly_three_cable_zc_section t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from white6 t 
left join sly_three_cable_zc_section t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white8;
create table white8 as
select distinct t.group_code,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from white7 t 
left join sly_three_cable_zc_section t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from white7 t 
left join sly_three_cable_zc_section t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white9;
create table white9 as
select distinct t.group_code,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from white8 t 
left join sly_three_cable_zc_section t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and t.group_code = t1.group_code 
where t1.z_zc_eqp_code is not null
union
select distinct t.group_code,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from white8 t 
left join sly_three_cable_zc_section t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and t.group_code = t1.group_code where t1.z_zc_eqp_code is not null;

drop table IF EXISTS white_list;
create table white_list as
select t.group_code,t.eqp1 zc_eqp_no from white t
union 
select t1.group_code,t1.eqp2 from white1 t1
union 
select t2.group_code,t2.eqp3 from white2 t2
union 
select t3.group_code,t3.eqp4 from white3 t3
union 
select t4.group_code,t4.eqp5 from white4 t4
union 
select t5.group_code,t5.eqp6 from white5 t5
union 
select t6.group_code,t6.eqp7 from white6 t6
union 
select t7.group_code,t7.eqp8 from white7 t7
union 
select t8.group_code,t8.eqp9 from white8 t8
union 
select t9.group_code,t9.eqp10 from white9 t9;


drop table IF EXISTS SLY_PAIR_ROUTE;
create table SLY_PAIR_ROUTE as
select t.city_name,t.area_name,t1.*,clock_timestamp()::timestamp(0) without time zone create_date from sly_pair_gl_info t left join SLY_THREE_ROUTE t1 on t.b_pair = t1.group_code and t.gl_code = t1.gl_code where t1.group_type = '设备对';
 
delete from SLY_PAIR_ROUTE t where exists(select 1 from white_list t1 where t.group_code = t1.group_code and(t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code)) and error='同管道'
;
		delete from SLY_PAIR_ROUTE t1 where EXISTS (select 1 from SLY_PAIR_ROUTE t where t.group_code =t1.group_code and t.error='同光缆') and t1.error='同管道';
----------------------------------------统计脚本
drop table IF EXISTS SLY_DEVICE_PAIR_ROUTE;
create table SLY_DEVICE_PAIR_ROUTE as
select t.city_name,t.area_name,t1.*,clock_timestamp()::timestamp(0) without time zone create_date from sly_pair_gl_info t left join SLY_THREE_ROUTE t1 on t.b_pair = group_code and t.gl_code = t1.gl_code where t1.group_type = '设备对';
--------------------------------统计脚本----------------
delete from SLY_PAIR_ROUTE   where cable_level='楼间' and error='无穿管' ; 

delete from SLY_PAIR_ROUTE       t where error in ('无穿管','同光缆','同管道') and exists (select 1 from  ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );
delete from SLY_DEVICE_PAIR_CHECK_INFO t where t.create_time = CURRENT_DATE;

insert into SLY_DEVICE_PAIR_CHECK_INFO (city_name,area_name,eqp_spec,eqp_num)
select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) from sly_pair_gl_info t group by t.city_name,t.area_name,t.eqp_spec;

update SLY_DEVICE_PAIR_CHECK_INFO a set port_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_device_netsource t where t.error = '缺端口' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set port_err = 0 where port_err is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO a set gl_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_device_netsource t where t.error = '缺光路' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set gl_err = 0 where gl_err is null and create_time = CURRENT_DATE;
 
update SLY_DEVICE_PAIR_CHECK_INFO a set bk_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_gl_bk_err t  group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set bk_err = 0 where bk_err is null and create_time = CURRENT_DATE;
 

update SLY_DEVICE_PAIR_CHECK_INFO a set cable_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE t where t.error = '同光缆' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set cable_err = 0 where cable_err is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO a set zc_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE t where t.error = '同管道' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set zc_err = 0 where zc_err is null and create_time = CURRENT_DATE;
--删除光缆级别为楼间的无穿管


update SLY_DEVICE_PAIR_CHECK_INFO a set none_zc = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE t where t.error = '无穿管' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set none_zc = 0 where none_zc is null and create_time = CURRENT_DATE;


drop table if exists SLY_PAIR_ROUTE_2;
create table SLY_PAIR_ROUTE_2 as select t.* from SLY_PAIR_ROUTE t join sly_pair_gl_info_2 t1 on t.gl_id=t1.gl_id;
drop table if exists SLY_PAIR_ROUTEzc_err;--select * from SLY_PAIR_ROUTEzc_err
create table SLY_PAIR_ROUTEzc_err as select group_code,zc_section_id from SLY_PAIR_ROUTE_2 t where error='同管道' group by group_code,zc_section_id having count(DISTINCT gl_id)>2;


drop table if exists SLY_PAIR_ROUTEcs_err;--select * from SLY_PAIR_ROUTEcs_err
create table SLY_PAIR_ROUTEcs_err as select group_code,cs_id from SLY_PAIR_ROUTE_2 t where error='同光缆' group by group_code,cs_id having count(DISTINCT gl_id)>2;

drop table if exists SLY_PAIR_ROUTE_z;
create table SLY_PAIR_ROUTE_z as select t.* from SLY_PAIR_ROUTE_2 t join SLY_PAIR_ROUTEzc_err t1 on t.group_code=t1.group_code and t.zc_section_id=t1.zc_section_id and t.error='同管道'
union
select t.* from SLY_PAIR_ROUTE_2 t join SLY_PAIR_ROUTEcs_err t1 on t.group_code=t1.group_code and t.cs_id=t1.cs_id and t.error='同光缆'
union select * from SLY_PAIR_ROUTE_2 where error='无穿管';


update SLY_DEVICE_PAIR_CHECK_INFO a set cable_err2 = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE_z t where t.error = '同光缆' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set cable_err2 = 0 where cable_err2 is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO a set zc_err2 = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE_z t where t.error = '同管道' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set zc_err2 = 0 where zc_err2 is null and create_time = CURRENT_DATE;

update SLY_DEVICE_PAIR_CHECK_INFO a set none_zc2 = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ROUTE_z t where t.error = '无穿管' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set none_zc2 = 0 where none_zc2 is null and create_time = CURRENT_DATE;

drop table if EXISTS SLY_PAIR_ERR_4_DATE;
create table SLY_PAIR_ERR_4_DATE AS
SELECT *,CURRENT_DATE as sj,NULL::date AS SJ_his,null as port_code FROM SLY_PAIR_ROUTE ;
insert into SLY_PAIR_ERR_4_DATE (city_name,area_name,group_code,device_id,device_code,device_name,create_date,eqp_spec,port_code,gl_id,gl_code,error,sj)
select city_name,area_name,b_pair,device_id,device_code,device_name,create_date,eqp_spec,port_code,gl_id,gl_code,error,CURRENT_DATE as sj from sly_pair_device_netsource;
update SLY_PAIR_ERR_4_DATE t set SJ_his=t1.SJ_his from SLY_PAIR_ERR_4_DATE_his t1 where t1.group_code=t.group_code and t1.error=t.error;
update SLY_PAIR_ERR_4_DATE t set sj_his=sj where sj_his is null;
drop table SLY_PAIR_ERR_4_DATE_his;
create table SLY_PAIR_ERR_4_DATE_his as 
select * ,SJ-SJ_his as err_sj from SLY_PAIR_ERR_4_DATE;


update SLY_DEVICE_PAIR_CHECK_INFO a set day_err= n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.group_code) count1 from SLY_PAIR_ERR_4_DATE_his  t where t.err_sj  >=4  group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set  day_err= 0 where  day_err  is null and create_time = CURRENT_DATE;
---select * from SLY_DEVICE_PAIR_CHECK_INFO 