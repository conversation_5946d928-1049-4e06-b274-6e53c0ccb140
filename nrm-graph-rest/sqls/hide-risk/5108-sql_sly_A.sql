DROP TABLE  IF EXISTS tmp_noc_sly_eqp_a1;
create table  tmp_noc_sly_eqp_a1  as
select * from mid_noc_sly_eqp_a where city_name='${areaName}'  and  ring_num !='None';
drop table if exists tmp_noc_sly_eqp_a;
create table tmp_noc_sly_eqp_a as
select city_name,area_name,t1.name b1_name,b1_ip,t2.name b2_name,b2_ip,ring_num,lgc_eqp_spec_no,t3.name lgc_eqp_name,t3.no lgc_eqp_no,lgc_eqp_ip,admin_ip,eqp_res from tmp_noc_sly_eqp_a1 t 
join ${o3_odso_schema}.res_phy_dev_daily t1 on t1.ip_addr=t.b1_ip 
join  ${o3_odso_schema}.res_phy_dev_daily t2 on t2.ip_addr=t.b2_ip
join  ${o3_odso_schema}.res_phy_dev_daily t3 on t3.ip_addr=t.admin_ip;
DROP TABLE  IF EXISTS TMP_NOC_LGC_A1 ;
CREATE TABLE TMP_NOC_LGC_A1 as
select distinct t.*,cd1.id b1_id,cd1.code b1_code,cd2.id b2_id,cd2.code b2_code,cd.id a_id,cd.code a_CODE  from tmp_noc_sly_eqp_a t join ${o3_res_schema}.cm_device cd1 on cd1.name=t.b1_name
join ${o3_res_schema}.cm_device cd2 on cd2.name=t.b2_name
join ${o3_res_schema}.cm_device cd on cd.name=t.lgc_eqp_name 
union
select distinct t.*,cd1.id b1_id,cd1.code b1_code,cd2.id b2_id,cd2.code b2_code,cd.id a_id,cd.code a_CODE  from tmp_noc_sly_eqp_a t join ${o3_res_schema}.cm_device cd1 on cd1.name=t.b1_name
join ${o3_res_schema}.cm_device cd2 on cd2.name=t.b2_name
join ${o3_res_schema}.cm_device cd on t.lgc_eqp_no=cd.code
union
select distinct t.*,cd1.id b1_id,cd1.code b1_code,cd2.id b2_id,cd2.code b2_code,cd.id a_id,cd.code a_CODE  from tmp_noc_sly_eqp_a1 t join ${o3_res_schema}.cm_device cd1 on cd1.name=t.b1_name
join ${o3_res_schema}.cm_device cd2 on cd2.name=t.b2_name
join ${o3_res_schema}.cm_device cd on t.lgc_eqp_no=cd.code
union
select distinct t.*,cd1.id b1_id,cd1.code b1_code,cd2.id b2_id,cd2.code b2_code,cd.id a_id,cd.code a_CODE  from tmp_noc_sly_eqp_a t join ${o3_res_schema}.cm_device cd1 on cd1.name=t.b1_name
join ${o3_res_schema}.cm_device cd2 on cd2.name=t.b2_name
join ${o3_res_schema}.cm_device cd on cd.name=t.lgc_eqp_name ;
/*SELECT * FROM ${o3_res_schema}.cm_device WHERE NAME=''
SELECT * FROM ${o3_res_schema}.CM_LINK LIMIT 100;
SELECT * FROM ${o3_res_schema}.CM_LINK T WHERE T.a_physic_device_id    z_physic_device_id
select * from TMP_NOC_LGC_A1 where b2_name='泰州新区11/B78-B-11-CX600-X3' limit 100;321232500000000008465944
select * from ${o3_res_schema}.cm_link where code='F1807230141' limit 100;
select * from ${o3_res_schema}.cm_port limit 100;
--B1   泰州海陵7/B78-B-7-CX600-X3  B2  泰州新区11/B78-B-11-CX600-X3     A  泰州金融中心1/B78-A-1-ATN910I   泰州市区体育馆B78/A-1-ATN910I
--泰州新区政企专用POP12 泰州海陵政企专用POP11  泰州市区体育馆B78/A-1-ATN910I  泰州海陵政企专用POP12*/



/*SELECT distinct a1.*,cl.id gl_id,cl.code gl_code FROM TMP_NOC_LGC_A1 a1 left join ${o3_res_schema}.cm_port cp on cp.physic_device_id=a1.a_id left join ${o3_res_schema}.cm_link cl on cl.z_physic_device_id=a1.a_id and cl.a_physic_device_id=a1.a_id and cl.z_port_id=cp.id  where a_id in (select a_id from tmp_noc_lgc_a1 t where a1.b1_id=t.b1_id and a1.b2_id=t.b2_id and a1.ring_num=t.ring_num )and spec_id = 1132400006*/

DROP TABLE IF EXISTS cm_link_CL;
CREATE TABLE cm_link_CL AS 
SELECT * FROM ${o3_res_schema}.cm_link  WHERE spec_id = 1132400006;

create index cm_link_CL22 on cm_link_CL(a_physic_device_id);
create index cm_link_CL23 on cm_link_CL(z_physic_device_id);





DROP TABLE  IF EXISTS TMP_NOC_gl_A;
create table TMP_NOC_gl_A as select distinct * from(
SELECT  a1.*,a1.b1_id gl_device_id,cl.id gl_id,cl.code gl_code FROM TMP_NOC_LGC_A1 a1  left join cm_link_CL cl on ((cl.a_physic_device_id=a1.b1_id and cl.z_physic_device_id=a_id) or (cl.z_physic_device_id=a1.b1_id and cl.a_physic_device_id=a_id))  
where a_id in (select a_id from tmp_noc_lgc_a1 t where a1.b1_id=t.b1_id and a1.b2_id=t.b2_id and a1.ring_num=t.ring_num ) --and a1.b1_name='泰州海陵7/B78-B-7-CX600-X3'limit 100  F2006221018  F1808200418
union
SELECT  a1.*,a1.b2_id gl_device_id,cl.id gl_id,cl.code gl_code FROM TMP_NOC_LGC_A1 a1  left join cm_link_CL cl on ((cl.z_physic_device_id=a1.b2_id and cl.a_physic_device_id=a1.a_id) or (cl.a_physic_device_id=a1.b2_id and cl.z_physic_device_id=a1.a_id))   where a_id in (select a_id from tmp_noc_lgc_a1 t where a1.b1_id=t.b1_id and a1.b2_id=t.b2_id and a1.ring_num=t.ring_num ) --and a1.b2_name='泰州新区11/B78-B-11-CX600-X3'F1807230141

union
select  a1.*,a2.a_id gl_device_id,cl.id gl_id,cl.code gl_code from TMP_NOC_LGC_A1 a1  left join cm_link_CL cl on cl.z_physic_device_id=a1.a_id       join TMP_NOC_LGC_A1 a2 on a2.b1_id=a1.b1_id and a2.b2_id=a1.b2_id and a1.ring_num=a2.ring_num  and a2.a_id=cl.a_physic_device_id 
union

select  a1.*,a2.a_id gl_device_id, cl.id gl_id,cl.code gl_code from TMP_NOC_LGC_A1 a1  left join cm_link_CL cl on cl.a_physic_device_id=a1.a_id       join TMP_NOC_LGC_A1 a2 on a2.b1_id=a1.b1_id and a2.b2_id=a1.b2_id and a1.ring_num=a2.ring_num  and a2.a_id=cl.z_physic_device_id ) a ;
/*select * from TMP_NOC_gl_A where b1_id=3453434
insert into TMP_NOC_LGC_A1 (b1_id,ring_num,b2_id,a_id) values (3453434,2,2412431,53234234)

select * from TMP_NOC_LGC_A1 where b1_id=3453434*/


		drop table TMP_NOC_LGC_A3_TJF ; --TMP_NOC_gl_A1
  CREATE TABLE TMP_NOC_LGC_A3_TJF AS
SELECT T1.*,
       --T2.PHY_EQP_ID,
       T3.FACILITY_ID BSE_EQP_ID ,
       SS.id STATION_ID,
       T2.a_id AS PHY_EQP_ID_2,
       T5.FACILITY_ID AS BSE_EQP_ID_2,
       SS_2.id AS STATION_ID_2
  FROM TMP_NOC_LGC_A1 T1
  --LEFT JOIN LGC_PHYEQP_2_LGCEQP T2
    --ON T1.LGC_EQP_ID = T2.LGC_EQP_ID
  JOIN ${o3_res_schema}.cm_device T3
    ON T1.a_id = T3.ID
  LEFT JOIN ${o3_res_schema}.CE_FACILITY_ROOM BR
    ON T3.FACILITY_ID=BR.FACILITY_ID  
		 left join ${o3_res_schema}.CM_FACILITY brr
		  on br.facility_id=brr.id
  LEFT JOIN    ${o3_res_schema}.RM_AREA SS
    ON BRr.TML_ID=SS.ID  
  --LEFT JOIN LGC_PHYEQP_2_LGCEQP T4
    --ON T1.LGC_EQP_ID_2 = T4.LGC_EQP_ID
		join TMP_NOC_LGC_A1 t2
		 on t2.b1_name=t1.b1_name and t2.b2_name=t1.b2_name and t2.ring_num=t1.ring_num
  JOIN ${o3_res_schema}.cm_device  T5
    ON t2.a_id = T5.ID
  LEFT JOIN ${o3_res_schema}.CE_FACILITY_ROOM BR_2
    ON T5.FACILITY_ID=BR_2.FACILITY_ID
		left join ${o3_res_schema}.CM_FACILITY brr_2
		  on br_2.facility_id=brr_2.id
  LEFT JOIN   ${o3_res_schema}.RM_AREA  SS_2
    ON BRr_2.TML_ID=SS_2.ID;

insert into TMP_NOC_gl_A
select DISTINCT t.city_name,t.area_name,t.b1_name,t.b1_ip,t.b2_name,t.b2_ip,t.ring_num,t.lgc_eqp_spec_no,t.lgc_eqp_name,t.lgc_eqp_no,t.lgc_eqp_ip,t.admin_ip,t.eqp_res,t.b1_id,t.b1_code,t.b2_id,t.b2_code,t.a_id,t.a_code,PHY_EQP_ID_2 gl_device_id,12345 gl_id,'同机房检测' gl_code 
from TMP_NOC_gl_A t join TMP_NOC_LGC_A3_TJF t1 on t.b1_name=t1.b1_name and t.b2_name=t1.b2_name and t.ring_num=t1.ring_num and t.lgc_eqp_name=t1.lgc_eqp_name where station_id=station_id_2 and t1.a_id !=PHY_EQP_ID_2;






delete from TMP_NOC_gl_A a where gl_code is null and exists(select 1 from TMP_NOC_gl_A a2 where a.b1_id=a2.b1_id and a.b2_id=a2.b2_id and a.ring_num=a2.ring_num and a.a_id=a2.a_id and a2.gl_code is not null);
DROP TABLE  IF EXISTS TMP_NOC_gl_A1;
create table TMP_NOC_gl_A1 as select *,CURRENT_DATE create_time from TMP_NOC_gl_A;
DROP TABLE  IF EXISTS TMP_NOC_gl_jz_A;--select * from TMP_NOC_gl_jz_A
create table TMP_NOC_gl_jz_A as
select t.*,cd.FACILITY_ID,ra.id STATION_ID,cd1.FACILITY_ID FACILITY_ID1,ra1.id station_id1
from TMP_NOC_gl_A t 
left join ${o3_res_schema}.cm_device cd on t.a_id=cd.id
left join ${o3_res_schema}.Cm_FACILITY cf on cd.FACILITY_ID=cf.ID
left join ${o3_res_schema}.rm_area ra on cf.TML_ID=ra.id
left join ${o3_res_schema}.cm_device cd1 on t.gl_device_id=cd1.id
left join ${o3_res_schema}.Cm_FACILITY cf1 on cd1.FACILITY_ID=cf1.ID
left join ${o3_res_schema}.rm_area ra1 on cf1.TML_ID=ra1.id;
drop table IF EXISTS TMP_NOC_gl_A1_num;
create table TMP_NOC_gl_A1_num as
select b1_name,b2_name,ring_num,lgc_eqp_name,count(gl_id)from TMP_NOC_gl_A1   group by b1_name,b2_name,ring_num,lgc_eqp_name having count(gl_id)<2;
---删除历史白名单（现网同步）
DELETE  FROM  TMP_NOC_gl_A1_num t1 WHERE EXISTS (SELECT 1 FROM tmp_noc_lgc_a_white_list t2
WHERE t1.b1_name=t2.b1_name AND t1.b2_name=t2.b2_name AND t1.ring_num=t2.ring_num);
DROP TABLE  IF EXISTS TMP_NOC_A_HUAN_ERR ;--select * from TMP_NOC_A_HUAN_ERR     
CREATE TABLE  TMP_NOC_A_HUAN_ERR AS 
SELECT t1.*,'A环路异常' AS ERR FROM TMP_NOC_gl_A1 T1 join TMP_NOC_gl_A1_num t2 on t1.b1_name=t2.b1_name and t1.b2_name=t2.b2_name and t1.ring_num=t2.ring_num and t1.lgc_eqp_name=t2.lgc_eqp_name; 



DELETE FROM TMP_NOC_gl_A t WHERE EXISTS (
SELECT 1 FROM TMP_NOC_gl_jz_A t2 WHERE t2.Station_Id=t2.Station_Id1
AND t.b1_name=t2.b1_name AND t.b2_name=t2.b2_name AND t.a_id=t2.a_id
AND t.gl_device_id=t2.gl_device_id AND t.ring_num=t2.ring_num
);

--去除环路异常的A环
DELETE FROM TMP_NOC_gl_A t1 WHERE EXISTS (SELECT 1 FROM TMP_NOC_A_HUAN_ERR t2 WHERE
t1.b1_name=t2.b1_name AND t1.b2_name=t2.b2_name AND t1.ring_num=t2.ring_num  );
COMMIT;
/*DROP TABLE  IF EXISTS   TMP_NOC_A_GL_ERR ;
CREATE TABLE TMP_NOC_A_GL_ERR AS 
SELECT DISTINCT T.*, '异常光路原因：' || M.备注 AS ERR
  FROM TMP_NOC_LGC_A3 T
  JOIN REPORTDEV.RLT_NOC_XHJ_QXGL_ERROR_NEW@REPORTDEV M
    ON T.GL_ID = M.BUSI_LINK_ID
  AND M.AREA = '${areaName}';
	--去除缺光路的A环
DELETE FROM TMP_NOC_LGC_A3 t1 WHERE EXISTS (SELECT 1 FROM TMP_NOC_LGC_GL_ERR t2 WHERE
t1.b1_name=t2.b1_name AND t1.b2_name=t2.b2_name AND t1.ring_num=t2.ring_num  );
COMMIT;*/
DROP TABLE  IF EXISTS TMP_NOC_gl_cable_A;
create table  TMP_NOC_gl_cable_A as--select * from TMP_NOC_gl_cable_A
select distinct t.*,cs.id cs_id,cs.code cs_code,cs.name cs_name,fiber.id fiber_id,fiber.code fiber_code ,fiber.name fiber_name, cn.id cable_id , cn.code cable_code,cn.name cable_name,cn.spec_id,cn.net_type_id,pp.desc_china cable_level from TMP_NOC_gl_A t
inner join ${o3_res_schema}.cr_link_link cll1  on cll1.upper_link_id = t.gl_id and cll1.spec_id=1133111310000
inner join ${o3_res_schema}.cr_link_link cll2  on cll2.upper_link_id  = cll1.lower_link_id and cll2.spec_id=1133511310000
inner join ${o3_res_schema}.CR_LINK_CABLE clc on clc.link_id = cll2.lower_link_id
inner join ${o3_res_schema}.cm_CABLE fiber on fiber.id = clc.cable_id
inner join ${o3_res_schema}.cm_CABLE cs on cs.id = fiber.parent_id and cs.spec_id = 1121000002  --光缆段
inner join ${o3_res_schema}.cr_net_entity cne on cs.id=cne.entity_ID
inner join ${o3_res_schema}.cm_net cn on cn.id = cne.NET_iD
left join ${o3_res_schema}.pm_pub_restriction pp on pp.serial_no = cn.net_type_id
where cll1.other_state_id !=100378 or   cll1.other_state_id is null ;

delete from TMP_NOC_gl_cable_A T 
where T.Fiber_Id is null

and exists (select 1 from TMP_NOC_gl_cable_A T1 where T1.gl_code =T.gl_code and T1.Fiber_Id is not null );
commit;


DROP TABLE  IF EXISTS TMP_NOC_gl_cable_A_glnum;
create table TMP_NOC_gl_cable_A_glnum as--select * from TMP_NOC_gl_cable_A_glnum
select t.b1_id,t.b2_id ,t.ring_num,t.cs_id, count(distinct t.gl_code) glnum from TMP_NOC_gl_cable_A t
where t.cable_level <> '局内' and t.cable_level <> '联络'

group by t.b1_id,t.b2_id ,t.ring_num,t.cs_id;
DROP TABLE  IF EXISTS TMP_NOC_A_SECTION_ERR;
create table TMP_NOC_A_SECTION_ERR--select distinct b1_id,b2_id,ring_num  from TMP_NOC_A_SECTION_ERR where err='无光缆' 
as
select a.*,'同光缆'err,CURRENT_DATE create_time from TMP_NOC_gl_cable_A a join TMP_NOC_gl_cable_A_glnum anum on a.b1_id=anum.b1_id and a.b2_id=anum.b2_id and a.ring_num=anum.ring_num and a.cs_id=anum.cs_id and anum.glnum >=2;
insert into TMP_NOC_A_SECTION_ERR 
select a.*,'无光缆'err,CURRENT_DATE create_time from TMP_NOC_gl_cable_A a join TMP_NOC_gl_cable_A_glnum anum on a.b1_id=anum.b1_id and a.b2_id=anum.b2_id and a.ring_num=anum.ring_num and a.cs_id=anum.cs_id and anum.cs_id is null;



/*insert into SLY_A_ROUTE(CITY_NAME,AREA_NAME,B1_NAME,B1_IP,B2_NAME,B2_IP,RING_NUM,LGC_EQP_SPEC_NO,LGC_EQP_NAME,LGC_EQP_NO,LGC_EQP_IP,ADMIN_IP,EQP_RES,B1_ID,B1_CODE,B2_ID,B2_CODE,A_ID,A_CODE,GL_DEVICE_ID,gl_id,gl_code,cs_id,cs_code,cs_name,fiber_id,fiber_code,fiber_name,cable_id,cable_code,cable_name,spec_id,net_type_id,cable_level,error)
select T.*,'同光缆'ERROR from TMP_NOC_gl_cable_A T 
where exists(select 1 from TMP_NOC_A_SECTION_ERR T1 where T.B1_ID=T1.B1_ID AND T.B2_ID=T1.B2_ID AND T.RING_NUM=T1.RING_NUM and T1.cs_id = 
cs_id);*/
commit;	
--同机房删除
  DROP TABLE  IF EXISTS TMP_NOC_A_SECTION_ERR_2 ;
  CREATE TABLE TMP_NOC_A_SECTION_ERR_2 AS
SELECT t1.*,t3.FACILITY_ID,t5.FACILITY_ID AS FACILITY_ID_2
 FROM TMP_NOC_A_SECTION_ERR t1
JOIN ${o3_res_schema}.CM_DEVICE t3
ON t1.a_id=t3.id
JOIN ${o3_res_schema}.CM_DEVICE t5
ON t1.gl_device_id=t5.id
;
DELETE FROM TMP_NOC_A_SECTION_ERR t WHERE t.err='无光缆' AND EXISTS (
SELECT 1 FROM TMP_NOC_A_SECTION_ERR_2 t2 WHERE t2.FACILITY_ID=t2.FACILITY_ID_2
AND t.b1_name=t2.b1_name AND t.b2_name=t2.b2_name AND t.a_id=t2.a_id
AND t.gl_device_id=t2.gl_device_id AND t.ring_num=t2.ring_num
);
 commit;
 /*<#if areaCode = 'SZ'>
--20180807---剔除苏州光路无光缆的问题
DELETE FROM TMP_NOC_LGC_SECTION_ERR t WHERE t.err='光路无光缆';
COMMIT;
</#if>
  <#if areaCode= 'NJ'> 
--剔除南京无光缆的问题
DELETE FROM  TMP_NOC_LGC_SECTION_ERR t1 WHERE EXISTS (SELECT 1 FROM TMP_A_SECTION_WHITE_LIST t2
WHERE t1.b1_name=t2.b1_name AND t1.b2_name=t2.b2_name AND t1.ring_num=t2.ring_num);
COMMIT;
</#if> */

--删除两种异常对后续影响
delete from TMP_NOC_gl_cable_A a where exists (select 1 from TMP_NOC_A_SECTION_ERR t where a.b1_name=t.b1_name and a.b2_name=t.b2_name and a.ring_num=t.ring_num );



-------稽核同管道begin-------------



 DROP TABLE  IF EXISTS sly_A_cable_zc_section;
 create table sly_A_cable_zc_section as
--insert into sly_three_cable_zc_section
select distinct t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from TMP_NOC_gl_cable_A t
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id
left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t2.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id 
 
union
select distinct t.*,t5.id zc_section_id,t5.code zc_section_code,t5.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from TMP_NOC_gl_cable_A t
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id
left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id and t5.spec_id in (1111000001,1111000002,1111000003,1111000004)
left join ${o3_res_schema}.cm_facility t3 on t3.id = t5.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t5.z_facility_id 


union
select distinct t.*,t2.id zc_section_id,t2.code zc_section_code,t2.name zc_section_name,t3.code a_zc_eqp_code,t4.code z_zc_eqp_code from TMP_NOC_gl_cable_A t
left join ${o3_res_schema}.cr_pipeline_cable t1 on t1.cable_id  = t.cs_id
left join ${o3_res_schema}.CM_PIPELINE t2 on t2.id = pipeline_id and t2.spec_id in (1111000001,1111000002,1111000003,1111000004)
--left join   ${o3_res_schema}.CM_PIPELINE t6 on t6.id =t2.parent_id --管孔
--left join ${o3_res_schema}.CM_PIPELINE t5 on t5.id = t6.parent_id
left join ${o3_res_schema}.cm_facility t3 on t3.id = t2.a_facility_id
left join ${o3_res_schema}.cm_facility t4 on t4.id = t2.z_facility_id ;


create index sly_A_cable_zc_section_l on sly_A_cable_zc_section(zc_section_id);
create index sly_A_cable_zc_section_l1 on sly_A_cable_zc_section(cs_id);




delete from sly_A_cable_zc_section T --SELECT * from sly_A_cable_zc_section
where T.zc_section_id is null

and exists (select 1 from sly_A_cable_zc_section T1 where T1.cs_id =T.cs_id and T1.zc_section_id is not null limit 1 );
commit;


 DROP TABLE  IF EXISTS SLY_A_ROUTE;--select cable_name from SLY_A_ROUTE group by cable_name
 create table SLY_A_ROUTE as
--insert into SLY_THREE_ROUTE 
select T.* , '无穿管' ERROR,CURRENT_DATE create_time from sly_A_cable_zc_section T 
where T.zc_section_id is null 
and T.Cable_Level<> '局内'  
and T.Cable_Level<> '联络' and not exists
(select 1 from sly_A_cable_zc_section T1 where T.B1_ID=T1.B1_ID AND T.B2_ID=T1.B2_ID AND T.RING_NUM=T1.RING_NUM
and T1.cs_id = T.cs_id and T1.zc_section_id is not null)
;
--删除光缆级别为楼间的无穿管。
delete from SLY_A_ROUTE  where cable_level='楼间'; 
delete from sly_A_cable_zc_section T 
where exists (select 1 from SLY_A_ROUTE T1 where T.B1_ID=T1.B1_ID AND T.B2_ID=T1.B2_ID AND T.RING_NUM=T1.RING_NUM
and T1.Fiber_Id = T.Fiber_Id and T1.Error = '无穿管' ) ;
commit;
delete from sly_A_cable_zc_section where a_zc_eqp_code is null and z_zc_eqp_code is null ;


delete from SLY_A_ROUTE  t where error='无穿管' and exists (select 1 from ${o3_res_schema}.cm_cable t1 where t.cs_id=t1.id  and t1.long_local_id =108439   );

 DROP TABLE  IF EXISTS sly_A_zc_section_glnum;
 create table sly_A_zc_section_glnum as--SELECT * FROM sly_A_zc_section_glnum
--insert into sly_THREE_zc_section_glnum
select t.B1_ID,T.B2_ID,t.ring_num , t.zc_section_code,count(distinct t.gl_code) glnum from sly_A_cable_zc_section t
where t.cable_level <> '局内' 
group by t.B1_ID,T.B2_ID,t.ring_num , t.zc_section_code;

DROP TABLE  IF EXISTS SLY_A_ZC_ERROR;
create table SLY_A_ZC_ERROR as
--insert into SLY_A_ZC_ERROR
select t.* from sly_A_zc_section_glnum t
where  t.glnum >= 2;


create index zc_section_codel on sly_A_cable_zc_section(zc_section_code);
create index B1_IDl on sly_A_cable_zc_section(B1_ID);
create index B2_IDl on sly_A_cable_zc_section(B2_ID);
create index RING_NUMl on sly_A_cable_zc_section(RING_NUM);
create index zc_section_codell on SLY_A_ZC_ERROR(zc_section_code);
create index B1_IDll on SLY_A_ZC_ERROR(B1_ID);
create index B2_IDll on SLY_A_ZC_ERROR(B2_ID);
create index RING_NUMll on SLY_A_ZC_ERROR(RING_NUM);


drop table if exists sly_A_cable_zc_section_err;

create table sly_A_cable_zc_section_err as
select * from sly_A_cable_zc_section t where exists (select 1 from SLY_A_ZC_ERROR t1 where T.B1_ID=T1.B1_ID AND T.B2_ID=T1.B2_ID AND T.RING_NUM=T1.RING_NUM and T1.zc_section_code = T.zc_section_code);
 

insert into  SLY_A_ROUTE --SELECT b1_id,b2_id,ring_num FROM SLY_A_ROUTE  WHERE ERROR='同管道'  group by b1_id,b2_id,ring_num
select T.*,'同管道' error,CURRENT_DATE create_time from sly_A_cable_zc_section_err T 
where T.Cable_Level <> '局内' ;
-------稽核同管道end-------------SELECT * FROM MID_NOC_SLY_A_JQ_WHITE_LIST
-------局前井白名单----

drop table IF EXISTS MID_NOC_SLY_A_JQ;
create table MID_NOC_SLY_A_JQ as
select b1_name,b2_name, lgc_eqp_no,gl_code,a_zc_eqp_code bse_eqp_no  from sly_A_cable_zc_section  where exists(select 1 from ${o3_res_schema}.CM_FACILITY where code=a_zc_eqp_code and notes like '%局前井%') group by b1_name,b2_name, lgc_eqp_no,gl_code,a_zc_eqp_code having COUNT(DISTINCT zc_section_id) = 1
union
select b1_name,b2_name, lgc_eqp_no,gl_code,z_zc_eqp_code bse_eqp_no from sly_A_cable_zc_section  where exists(select 1 from ${o3_res_schema}.CM_FACILITY where code=z_zc_eqp_code and notes like '%局前井%') group by b1_name,b2_name, lgc_eqp_no,gl_code,z_zc_eqp_code having COUNT(DISTINCT zc_section_id) = 1 ;



DROP TABLE  IF EXISTS  white_A;
create table white_A as
select distinct t.b1_name,t.b2_name,t.bse_eqp_no eqp1 from MID_NOC_SLY_A_JQ t ;--SELECT * FROM white_A

DROP TABLE  IF EXISTS  white_A1;--SELECT 8 FROM 
create table white_A1 as
select distinct t.b1_name,t.b2_name,t.eqp1,t1.z_zc_eqp_code eqp2,t1.zc_section_code 
from white_A t 
left join sly_A_cable_zc_section t1 on t.eqp1 = t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME   
union
select distinct t.b1_name,t.b2_name,t.eqp1,t1.a_zc_eqp_code eqp2,t1.zc_section_code
from white_A t 
left join sly_A_cable_zc_section t1 on t.eqp1 = t1.z_zc_eqp_code  and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME   ;

DROP TABLE  IF EXISTS  white_A2;
create table white_A2 as
select distinct t.b1_name,t.b2_name,t.eqp2,t1.z_zc_eqp_code eqp3,t1.zc_section_code 
from white_A1 t 
left join sly_A_cable_zc_section t1 on t.eqp2 = t1.a_zc_eqp_code and t.eqp1 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp2,t1.a_zc_eqp_code eqp3,t1.zc_section_code
from white_A1 t 
left join sly_A_cable_zc_section t1 on t.eqp2 = t1.z_zc_eqp_code and t.eqp1 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A3;
create table white_A3 as
select distinct t.b1_name,t.b2_name,t.eqp3,t1.z_zc_eqp_code eqp4,t1.zc_section_code 
from white_A2 t 
left join sly_A_cable_zc_section t1 on t.eqp3 = t1.a_zc_eqp_code and t.eqp2 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp3,t1.a_zc_eqp_code eqp4,t1.zc_section_code
from white_A2 t 
left join sly_A_cable_zc_section t1 on t.eqp3 = t1.z_zc_eqp_code and t.eqp2 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A4;
create table white_A4 as
select distinct t.b1_name,t.b2_name,t.eqp4,t1.z_zc_eqp_code eqp5,t1.zc_section_code 
from white_A3 t 
left join sly_A_cable_zc_section t1 on t.eqp4 = t1.a_zc_eqp_code and t.eqp3 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp4,t1.a_zc_eqp_code eqp5,t1.zc_section_code
from white_A3 t 
left join sly_A_cable_zc_section t1 on t.eqp4 = t1.z_zc_eqp_code and t.eqp3 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A5;
create table white_A5 as
select distinct t.b1_name,t.b2_name,t.eqp5,t1.z_zc_eqp_code eqp6,t1.zc_section_code 
from white_A4 t 
left join sly_A_cable_zc_section t1 on t.eqp5 = t1.a_zc_eqp_code and t.eqp4 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp5,t1.a_zc_eqp_code eqp6,t1.zc_section_code
from white_A4 t 
left join sly_A_cable_zc_section t1 on t.eqp5 = t1.z_zc_eqp_code and t.eqp4 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A6;
create table white_A6 as
select distinct t.b1_name,t.b2_name,t.eqp6,t1.z_zc_eqp_code eqp7,t1.zc_section_code 
from white_A5 t 
left join sly_A_cable_zc_section t1 on t.eqp6 = t1.a_zc_eqp_code and t.eqp5 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp6,t1.a_zc_eqp_code eqp7,t1.zc_section_code
from white_A5 t 
left join sly_A_cable_zc_section t1 on t.eqp6 = t1.z_zc_eqp_code and t.eqp5 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A7;
create table white_A7 as
select distinct t.b1_name,t.b2_name,t.eqp7,t1.z_zc_eqp_code eqp8,t1.zc_section_code 
from white_A6 t 
left join sly_A_cable_zc_section t1 on t.eqp7 = t1.a_zc_eqp_code and t.eqp6 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp7,t1.a_zc_eqp_code eqp8,t1.zc_section_code
from white_A6 t 
left join sly_A_cable_zc_section t1 on t.eqp7 = t1.z_zc_eqp_code and t.eqp6 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A8;
create table white_A8 as
select distinct t.b1_name,t.b2_name,t.eqp8,t1.z_zc_eqp_code eqp9,t1.zc_section_code 
from white_A7 t 
left join sly_A_cable_zc_section t1 on t.eqp8 = t1.a_zc_eqp_code and t.eqp7 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp8,t1.a_zc_eqp_code eqp9,t1.zc_section_code
from white_A7 t 
left join sly_A_cable_zc_section t1 on t.eqp8 = t1.z_zc_eqp_code and t.eqp7 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A9;
create table white_A9 as
select distinct t.b1_name,t.b2_name,t.eqp9,t1.z_zc_eqp_code eqp10,t1.zc_section_code 
from white_A8 t 
left join sly_A_cable_zc_section t1 on t.eqp9 = t1.a_zc_eqp_code and t.eqp8 <> t1.z_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    
where t1.z_zc_eqp_code is not null
union
select distinct t.b1_name,t.b2_name,t.eqp9,t1.a_zc_eqp_code eqp10,t1.zc_section_code
from white_A8 t 
left join sly_A_cable_zc_section t1 on t.eqp9 = t1.z_zc_eqp_code and t.eqp8 <> t1.a_zc_eqp_code and  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    where t1.z_zc_eqp_code is not null;

DROP TABLE  IF EXISTS  white_A_list;
create table white_A_list as
select t.b1_name,t.b2_name,t.eqp1 zc_eqp_no from white_A t
union 
select t1.b1_name,t1.b2_name,t1.eqp2 from white_A1 t1
union 
select t2.b1_name,t2.b2_name,t2.eqp3 from white_A2 t2
union 
select t3.b1_name,t3.b2_name,t3.eqp4 from white_A3 t3
union 
select t4.b1_name,t4.b2_name,t4.eqp5 from white_A4 t4
union 
select t5.b1_name,t5.b2_name,t5.eqp6 from white_A5 t5
union 
select t6.b1_name,t6.b2_name,t6.eqp7 from white_A6 t6
union 
select t7.b1_name,t7.b2_name,t7.eqp8 from white_A7 t7
union 
select t8.b1_name,t8.b2_name,t8.eqp9 from white_A8 t8
union 
select t9.b1_name,t9.b2_name,t9.eqp10 from white_A9 t9;

DROP TABLE  IF EXISTS  SLY_AA_ROUTE;  
create table SLY_AA_ROUTE as
select t1.*  from TMP_NOC_gl_A t left join SLY_A_ROUTE t1 on  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME  and t.gl_code = t1.gl_code ;
 
delete from SLY_A_ROUTE t where exists(select 1 from white_A_list t1 where  T.B1_NAME=T1.B1_NAME AND T.B2_NAME=T1.B2_NAME    and(t1.zc_eqp_no = t.a_zc_eqp_code or t1.zc_eqp_no = t.z_zc_eqp_code));
---------------------------------------------------------------------------------------------------------------------------------------------------------汇总---

/*DROP TABLE  IF EXISTS SLY_DEVICE_A_CHECK_INFO;
create table SLY_DEVICE_A_CHECK_INFO (
city_name varchar(100),
area_name varchar(100),
eqp_num   numeric,
AH_YC     numeric,
gl_yc numeric,
w_gl  numeric,
t_gl numeric,
w_cg numeric,
t_gd numeric,
create_time date DEFAULT (clock_timestamp())::timestamp(0) without time zone
);*/
delete from SLY_DEVICE_A_CHECK_INFO t where t.create_time = CURRENT_DATE;
insert into SLY_DEVICE_A_CHECK_INFO (city_name,area_name,eqp_num)
select t.city_name,t.area_name, count(*) from (
select t.city_name,t.area_name, b1_id,b2_id,ring_num from TMP_NOC_gl_A1 t group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name
;
 
 update SLY_DEVICE_A_CHECK_INFO a set ah_yc = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from TMP_NOC_A_HUAN_ERR t where t.err = 'A环路异常' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set ah_yc = 0 where ah_yc is null and create_time = CURRENT_DATE;
 
 
---已有gl_yc(光路异常字段等待数据)

  update SLY_DEVICE_A_CHECK_INFO a set w_gl = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from TMP_NOC_A_SECTION_ERR t where t.err = '无光缆' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set w_gl = 0 where w_gl is null and create_time = CURRENT_DATE;
 
 
  update SLY_DEVICE_A_CHECK_INFO a set t_gl = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from TMP_NOC_A_SECTION_ERR t where t.err = '同光缆' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set t_gl = 0 where t_gl is null and create_time = CURRENT_DATE;

update SLY_DEVICE_A_CHECK_INFO a set w_cg = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from SLY_A_ROUTE t where t.error = '无穿管' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set w_cg = 0 where w_cg is null and create_time = CURRENT_DATE;
 
 update SLY_DEVICE_A_CHECK_INFO a set t_gd = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from SLY_A_ROUTE t where t.error = '同管道' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set t_gd = 0 where t_gd is null and create_time = CURRENT_DATE;
DROP TABLE  IF EXISTS tmp_noc_sly_eqp_a1;
drop table if exists tmp_noc_sly_eqp_a;
DROP TABLE  IF EXISTS TMP_NOC_LGC_A1 ;
DROP TABLE IF EXISTS cm_link_CL;

drop table TMP_NOC_LGC_A3_TJF ;

DROP TABLE  IF EXISTS TMP_NOC_gl_jz_A;
drop table IF EXISTS TMP_NOC_gl_A1_num;


DROP TABLE  IF EXISTS TMP_NOC_gl_cable_A_glnum;

  DROP TABLE  IF EXISTS TMP_NOC_A_SECTION_ERR_2 ;


		 DROP TABLE  IF EXISTS sly_A_zc_section_glnum;
		 DROP TABLE  IF EXISTS SLY_A_ZC_ERROR;
		 drop table if exists sly_A_cable_zc_section_err;
		 drop table IF EXISTS MID_NOC_SLY_A_JQ;
		 DROP TABLE  IF EXISTS  white_A;
		 DROP TABLE  IF EXISTS  white_A1;
		 DROP TABLE  IF EXISTS  white_A2;
		 DROP TABLE  IF EXISTS  white_A3;
		 DROP TABLE  IF EXISTS  white_A4;
		 DROP TABLE  IF EXISTS  white_A5;
		 DROP TABLE  IF EXISTS  white_A6;
		 DROP TABLE  IF EXISTS  white_A7;
		 DROP TABLE  IF EXISTS  white_A8;
		 DROP TABLE  IF EXISTS  white_A9;
		 DROP TABLE  IF EXISTS  white_A_list;
		 DROP TABLE  IF EXISTS  SLY_AA_ROUTE;