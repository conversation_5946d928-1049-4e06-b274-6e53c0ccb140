SLY_YCGL_160--alter table SLY_CN_NETRESOURCE add err_info varchar(300);

--单设备双路由异常光路统计
drop table if exists sly_device_gl_infoerr;
create table sly_device_gl_infoerr as
select DISTINCT t.*,'异常光路'error ,t1.err_info from sly_device_gl_info t join 
(select * from RLT_NOC_YCP_QXGL_ERROR_NEW ) t1  on t.gl_id=t1.fiber_busi_id::numeric;
--select * from sly_deivice_tj_info;
delete from sly_device_netsource where error='异常光路';
insert into sly_device_netsource select * from sly_device_gl_infoerr;
update sly_deivice_tj_info t  set gl_err2=(select count(DISTINCT device_code) from sly_device_gl_infoerr t1 where t.area_name=t1.area_name and t.device_spec=t1.device_spec and t.city_name=t1.city_name   ) where t.create_time=to_char(CURRENT_DATE,'yyyy-mm-dd');
---dcsw异常光路统计
delete from SLY_DCSW_NETRESOURCE where error='异常光路';
insert into SLY_DCSW_NETRESOURCE
select distinct T.city_name,T.area_name,T.v_name,T.v_no,T.lgc_eqp_name,T.lgc_eqp_no,T.port_code,T.gl_code ,'异常光路' ERROR,t1.err_info from TMP_DCSW_PHY_GL T join RLT_NOC_YCP_QXGL_ERROR_NEW t1 on  t.gl_id=t1.fiber_busi_id::numeric;

update SLY_DCSW_CHECK_INFO  t  set gl_err2=(select count(DISTINCT v_name) from SLY_DCSW_NETRESOURCE t1 where t.city_name=t1.city_name and t1.ERROR='异常光路'   ) where create_time = CURRENT_DATE;

update SLY_DCSW_CHECK_INFO set gl_err2 = 0 where gl_err is null and create_time = CURRENT_DATE; 
--A环异常光路统计
delete from TMP_NOC_A_HUAN_ERR  where ERR ='异常光路';
insert into TMP_NOC_A_HUAN_ERR SELECT DISTINCT t.city_name	,
t.area_name	,
t.b1_name	,
t.b1_ip	,
t.b2_name	,
t.b2_ip	,
t.ring_num	,
t.lgc_eqp_spec_no	,
t.lgc_eqp_name	,
t.lgc_eqp_no	,
t.lgc_eqp_ip	,
t.admin_ip	,
t.eqp_res	,
t.b1_id	,
t.b1_code	,
t.b2_id	,
t.b2_code	,
t.a_id	,
t.a_code	,
t.gl_device_id	,
t.gl_id	,
t.gl_code	,
'异常光路' AS ERR,
t.create_time	,
 t1.err_info FROM TMP_NOC_gl_A1 T join RLT_NOC_YCP_QXGL_ERROR_NEW t1  on t.gl_id=t1.fiber_busi_id::numeric;

 update SLY_DEVICE_A_CHECK_INFO a set gl_yc = n.count1
from (select t.city_name,t.area_name,count(*) count1 from  (select t.city_name,t.area_name,b1_id,b2_id,ring_num  from TMP_NOC_A_HUAN_ERR t where t.err = '异常光路' group by t.city_name,t.area_name,b1_id,b2_id,ring_num)t group by t.city_name,t.area_name )n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name ;
update SLY_DEVICE_A_CHECK_INFO set gl_yc = 0 where gl_yc is null and create_time = CURRENT_DATE;
--设备对异常光路
delete from sly_pair_device_netsource where ERROR ='异常光路';
insert into sly_pair_device_netsource
select T.* ,'异常光路' ERROR,t1.err_info from sly_pair_gl_info T join  RLT_NOC_YCP_QXGL_ERROR_NEW t1  on t.gl_id=t1.fiber_busi_id::numeric;
update SLY_DEVICE_PAIR_CHECK_INFO a set link_err = n.count1
from (select t.city_name,t.area_name,t.eqp_spec,count(distinct t.b_pair) count1 from sly_pair_device_netsource t where t.error = '异常光路' group by t.city_name,t.area_name,t.eqp_spec) n where a.create_time = CURRENT_DATE and a.city_name = n.city_name and  a.area_name = n.area_name and a.eqp_spec = n.eqp_spec;
update SLY_DEVICE_PAIR_CHECK_INFO set link_err = 0 where link_err is null and create_time = CURRENT_DATE;
--CN2异常光路
delete from SLY_CN_NETRESOURCE where  ERROR ='异常光路';
insert into SLY_CN_NETRESOURCE
select T.* ,'异常光路' ERROR,t1.err_info from SLY_CN_BASE_INFO T join RLT_NOC_YCP_QXGL_ERROR_NEW t1 on t.gl_id=t1.fiber_busi_id::numeric;
update SLY_CN_CHECK_INFO t1 set link_err = (select count(distinct t.device_code) count1 from SLY_CN_NETRESOURCE  t where t.error = '异常光路' and t1.city_name=t.city_name ) where create_time = CURRENT_DATE;
update SLY_CN_CHECK_INFO set link_err = 0 where link_err is null and create_time = CURRENT_DATE;