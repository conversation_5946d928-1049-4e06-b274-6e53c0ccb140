----生成大客户OLT清单
drop table sly_olt_important ;
create table sly_olt_important as
select t.*,t1.user_num , case when t1.user_num >= 1000 and t1.user_num <2000 then '1000-2000'
when t1.user_num >= 2000 and t1.user_num <4000 then '2000-4000'
when t1.user_num >= 4000  then '4000以上' end olt_spec 
 from sly_device_base_info t
left join ywkt_olt_user_num t1 on t1.olt_id = t.device_id
where t.device_spec = 'OLT设备' and t1.user_num >=1000;

----生成当天的统计数据
delete from  sly_olt_important_tj t where  t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
insert into sly_olt_important_tj (city_name,area_name,device_spec,device_num,create_time)
select t.city_name,t.area_name,t.olt_spec ,count(1),to_char(CURRENT_DATE,'yyyy-mm-dd')  from sly_olt_important t
group by t.city_name,t.area_name,t.olt_spec ;

----更新统计信息
update sly_olt_important_tj t set port_err = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_netsource t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id 
where t1.error = '缺端口' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set gl_err1 = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_netsource t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '缺光路' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set gl_err2 = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_netsource t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '光路异常' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');


update sly_olt_important_tj t set resource_err = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_netsource t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set bk_err1 = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_gl_bk_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '无板卡' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set bk_err2 = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_device_gl_bk_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '同板卡' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set cs_err = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_dwdm_gl_route_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '同光缆' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set zc_null = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_dwdm_gl_route_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '无穿管' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set zc_err = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_dwdm_gl_route_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where error = '同管道' group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');

update sly_olt_important_tj t set route_err = n.num 
from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM sly_dwdm_gl_route_err t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
 group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');


--将空填补为0
update sly_olt_important_tj t set port_err=0 where port_err is null  ;
update sly_olt_important_tj t set gl_err1=0 where gl_err1 is null;
update sly_olt_important_tj t set gl_err2=0 where gl_err2 is null  ;
update sly_olt_important_tj t set bk_err1=0 where bk_err1 is null  ;
update sly_olt_important_tj t set bk_err2=0 where bk_err2 is null  ;
update sly_olt_important_tj t set resource_err=0 where resource_err is null  ;
update sly_olt_important_tj t set cs_err=0 where cs_err is null  ;
update sly_olt_important_tj t set zc_null=0 where zc_null is null  ;
update sly_olt_important_tj t set zc_err=0 where zc_err is null  ;
update sly_olt_important_tj t set route_err=0 where route_err is null  ;
drop table if EXISTS SLY_ERR_4_DATE;
create table SLY_ERR_4_DATE AS
SELECT *,CURRENT_DATE as sj,NULL::date AS SJ_his FROM sly_dwdm_gl_route_err  ;
insert into SLY_ERR_4_DATE ( ip,
city_name,
area_name,
device_id,
device_code,
device_name,
device_spec,
create_date,
port_id,
port_code,
gl_id,
gl_code,
error,sj)
select ip,
city_name,
area_name,
device_id,
device_code,
device_name,
device_spec,
create_date,
port_id,
port_code,
gl_id,
gl_code,
error,
CURRENT_DATE as sj from sly_device_netsource  ;



update SLY_ERR_4_DATE t set SJ_his=t1.SJ_his from SLY_ERR_4_DATE_his t1 where t1.device_code=t.device_code and t1.error=t.error;
update SLY_ERR_4_DATE t set sj_his=sj where sj_his is null;
drop table if EXISTS  SLY_ERR_4_DATE_his;
create table SLY_ERR_4_DATE_his as 
select * ,SJ-SJ_his as err_sj from SLY_ERR_4_DATE;
update sly_olt_important_tj t  set day_err= n.num from (SELECT	t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec, COUNT(DISTINCT t1.device_id) AS num	FROM SLY_ERR_4_DATE_his t1
inner join sly_olt_important t2 on t2.device_id = t1.device_id
where err_sj >= 4 group by t1.CITY_NAME, t1.AREA_NAME, t2.olt_spec)n 
where T.CITY_NAME = n.CITY_NAME AND T.AREA_NAME = n.AREA_NAME AND T.device_spec = n.olt_spec AND t.create_time = to_char(CURRENT_DATE,'yyyy-mm-dd');
update sly_olt_important_tj t set day_err=0 where  day_err  is null  ;
