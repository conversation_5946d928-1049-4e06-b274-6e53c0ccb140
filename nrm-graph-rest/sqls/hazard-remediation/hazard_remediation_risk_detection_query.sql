// hazard_remediation_risk_detection_query.sql
// 查询隐患整改风险检测记录
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

var queryFun = @@sql(p)<%
    SELECT 
        id, remediation_id, detection_type, detection_phase, check_result, exception_info,
        risk_level, route_num, optical_path_count, detection_snapshot, optical_paths_snapshot,
        detection_time, detector, detection_status, error_message, create_time, update_time
    FROM hazard_remediation_risk_detection
    WHERE remediation_id = #{p.remediationId}::numeric
    ORDER BY detection_time DESC
    LIMIT 100
%>;

var data = queryFun(p);

return {
    "success": true,
    "message": "查询成功",
    "data": data
};
