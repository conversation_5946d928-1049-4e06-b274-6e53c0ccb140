// hazard_remediation_optical_path_query.sql
// 查询隐患整改光路列表

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var queryFun = @@sql(p)<%
    SELECT id, remediation_id, optical_path_code, sort_order, create_time
    FROM hazard_remediation_optical_path
    WHERE remediation_id = #{p.remediationId}::numeric
    ORDER BY sort_order ASC
    LIMIT 1000
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})
var data = queryFun(p);

return {
    "success": true,
    "message": "查询成功",
    "data": data
};
