// hazard_remediation_attachment_insert.sql
// 插入隐患整改附件记录
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var insertFun = @@sql(p)<%
    INSERT INTO hazard_remediation_attachment (
        remediation_id, original_filename, stored_filename, file_size, file_type,
        s3_bucket, s3_key, uploader
    ) VALUES (
        #{p.remediationId}::numeric, #{p.originalFilename}, #{p.storedFilename}, 
        #{p.fileSize}::numeric, #{p.fileType}, #{p.s3Bucket}, #{p.s3Key}, #{p.uploader}
    ) RETURNING id
%>;

var result = insertFun(${param});

return {
    "success": true,
    "message": "附件记录保存成功",
    "data": result
};
