// hazard_remediation_create.sql
// 创建隐患整改单
// limit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.DateTimeUdfSource' as time;

var insertFun = @@sql(p)<%
    INSERT INTO hazard_remediation (
        city_code, district, speciality, hazard_source, hazard_type, hazard_level, title,
        remediation_description, expected_completion_date, actual_completion_date,
        responsible_person, responsible_person_contact, remediation_person, remediation_person_contact,
        protection_scenario, custom_protection_scenario, device_type, custom_device_type,
        selected_protection_group_id, optical_path_group_code, optical_path_group_name,
        is_lifeline, lifeline_business_id, lifeline_business_name, circuit_code, circuit_name,
        customer_name, customer_account, customer_manager, customer_manager_department,
        customer_level, differentiated_service_level, self_discovered_description,
        discovered_by, discovered_date, creator, creator_id, remark, status
    ) VALUES (
        #{p.cityCode}, #{p.district}, #{p.speciality}, #{p.hazardSource}, #{p.hazardType}, #{p.hazardLevel}, #{p.title},
        #{p.remediationDescription}, #{p.expectedCompletionDate}::date, #{p.actualCompletionDate}::date,
        #{p.responsiblePerson}, #{p.responsiblePersonContact}, #{p.remediationPerson}, #{p.remediationPersonContact},
        #{p.protectionScenario}, #{p.customProtectionScenario}, #{p.deviceType}, #{p.customDeviceType},
        #{p.selectedProtectionGroupId}, #{p.opticalPathGroupCode}, #{p.opticalPathGroupName},
        #{p.isLifeline}, #{p.lifelineBusinessId}, #{p.lifelineBusinessName}, #{p.circuitCode}, #{p.circuitName},
        #{p.customerName}, #{p.customerAccount}, #{p.customerManager}, #{p.customerManagerDepartment},
        #{p.customerLevel}, #{p.differentiatedServiceLevel}, #{p.selfDiscoveredDescription},
        #{p.discoveredBy}, #{p.discoveredDate}::date, #{p.creator}, #{p.creatorId}, #{p.remark}, #{p.status}
    ) RETURNING id
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})
var result = insertFun(p);

return {
    "success": true,
    "message": "隐患整改单创建成功",
    "data": result
};
