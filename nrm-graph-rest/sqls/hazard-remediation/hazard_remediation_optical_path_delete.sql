// hazard_remediation_optical_path_delete.sql
// 删除隐患整改光路

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

// 删除该整改单的所有光路记录
var deleteFun = @@sql(p)<%
    DELETE FROM hazard_remediation_optical_path WHERE remediation_id = #{p.remediationId}::numeric
%>;

var result = deleteFun(p);

return {
    "success": true,
    "message": "光路信息删除成功",
    "data": {
        "deletedRows": result
    }
};
