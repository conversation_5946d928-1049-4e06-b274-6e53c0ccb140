-- 隐患整改测试数据插入脚本
-- 用于测试功能是否正常

-- 1. 插入测试隐患整改单
INSERT INTO hazard_remediation (
    city_code, hazard_source, hazard_type, hazard_level, title, 
    remediation_description, expected_completion_date, responsible_person, 
    remediation_person, creator
) VALUES 
(
    'xz', 'selfDiscovered', 'singlePath', 'high', '徐州地区单路由隐患整改',
    '发现某光路存在单路由风险，需要进行双路由改造，确保业务连续性', '2024-12-31', '张三', '李四', '管理员'
),
(
    'nj', 'businessGroup', 'pathConflict', 'medium', '南京地区路由冲突整改',
    '发现业务保护组存在路由冲突问题，需要重新规划路由', '2024-12-25', '王五', '赵六', '系统管理员'
),
(
    'sz', 'deviceGroup', 'deviceRisk', 'low', '苏州地区设备风险整改',
    '设备保护组中发现设备老化风险，需要制定设备更换计划', '2025-01-15', '钱七', '孙八', '运维人员'
);

-- 2. 获取刚插入的隐患整改单ID（假设从1开始）
-- 为第一个隐患整改单插入光路信息
INSERT INTO hazard_remediation_optical_path (remediation_id, optical_path_code, sort_order)
VALUES 
    (1, 'XZ-OPT-001', 1),
    (1, 'XZ-OPT-002', 2);

-- 为第二个隐患整改单插入光路信息
INSERT INTO hazard_remediation_optical_path (remediation_id, optical_path_code, sort_order)
VALUES 
    (2, 'NJ-OPT-001', 1),
    (2, 'NJ-OPT-002', 2),
    (2, 'NJ-OPT-003', 3);

-- 为第三个隐患整改单插入光路信息
INSERT INTO hazard_remediation_optical_path (remediation_id, optical_path_code, sort_order)
VALUES 
    (3, 'SZ-OPT-001', 1);

-- 3. 插入测试附件记录
INSERT INTO hazard_remediation_attachment (
    remediation_id, original_filename, stored_filename, file_size, file_type, 
    s3_bucket, s3_key, uploader
) VALUES 
(
    1, '徐州隐患整改方案.pdf', 'uuid-12345-67890-abcdef.pdf', 1024000, 'application/pdf',
    'yuanfeng', 'shuangluyou/hazard-remediation/uuid-12345-67890-abcdef.pdf', '管理员'
),
(
    1, '现场照片1.jpg', 'uuid-23456-78901-bcdefg.jpg', 512000, 'image/jpeg',
    'yuanfeng', 'shuangluyou/hazard-remediation/uuid-23456-78901-bcdefg.jpg', '管理员'
),
(
    2, '南京路由冲突分析报告.docx', 'uuid-34567-89012-cdefgh.docx', 2048000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'yuanfeng', 'shuangluyou/hazard-remediation/uuid-34567-89012-cdefgh.docx', '系统管理员'
),
(
    3, '设备清单.xlsx', 'uuid-45678-90123-defghi.xlsx', 768000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'yuanfeng', 'shuangluyou/hazard-remediation/uuid-45678-90123-defghi.xlsx', '运维人员'
);

-- 4. 验证数据插入结果
-- 查询隐患整改单列表
SELECT 
    hr.id,
    hr.title,
    hr.status,
    hr.city_code,
    hr.hazard_source,
    hr.hazard_type,
    hr.hazard_level,
    hr.responsible_person,
    hr.remediation_person,
    hr.create_time,
    COUNT(DISTINCT hrop.id) as optical_path_count,
    COUNT(DISTINCT hra.id) as attachment_count
FROM hazard_remediation hr
LEFT JOIN hazard_remediation_optical_path hrop ON hr.id = hrop.remediation_id
LEFT JOIN hazard_remediation_attachment hra ON hr.id = hra.remediation_id
GROUP BY hr.id, hr.title, hr.status, hr.city_code, hr.hazard_source, hr.hazard_type, 
         hr.hazard_level, hr.responsible_person, hr.remediation_person, hr.create_time
ORDER BY hr.create_time DESC;

-- 查询第一个隐患整改单的详细信息
SELECT 
    hr.*,
    json_agg(
        json_build_object(
            'id', hrop.id,
            'optical_path_code', hrop.optical_path_code,
            'sort_order', hrop.sort_order
        ) ORDER BY hrop.sort_order
    ) FILTER (WHERE hrop.id IS NOT NULL) as optical_paths,
    json_agg(
        json_build_object(
            'id', hra.id,
            'original_filename', hra.original_filename,
            'stored_filename', hra.stored_filename,
            'file_size', hra.file_size,
            'file_type', hra.file_type,
            's3_key', hra.s3_key,
            'upload_time', hra.upload_time,
            'uploader', hra.uploader
        ) ORDER BY hra.upload_time
    ) FILTER (WHERE hra.id IS NOT NULL) as attachments
FROM hazard_remediation hr
LEFT JOIN hazard_remediation_optical_path hrop ON hr.id = hrop.remediation_id
LEFT JOIN hazard_remediation_attachment hra ON hr.id = hra.remediation_id
WHERE hr.id = 1
GROUP BY hr.id;

-- 统计查询示例
-- 按状态统计
SELECT 
    status,
    COUNT(*) as count,
    COUNT(CASE WHEN hazard_level = 'high' THEN 1 END) as high_risk_count,
    COUNT(CASE WHEN hazard_level = 'medium' THEN 1 END) as medium_risk_count,
    COUNT(CASE WHEN hazard_level = 'low' THEN 1 END) as low_risk_count
FROM hazard_remediation 
GROUP BY status
ORDER BY 
    CASE status 
        WHEN 'draft' THEN 1 
        WHEN 'inProgress' THEN 2 
        WHEN 'completed' THEN 3 
        ELSE 4 
    END;

-- 按地市统计
SELECT 
    city_code,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
    COUNT(CASE WHEN status = 'inProgress' THEN 1 END) as in_progress_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
FROM hazard_remediation 
GROUP BY city_code
ORDER BY total_count DESC;
