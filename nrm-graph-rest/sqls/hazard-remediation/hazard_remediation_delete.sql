// hazard_remediation_delete.sql
// 删除隐患整改单（级联删除关联数据）

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param});

// 参数验证
if (p.is_empty || collect.isEmpty(p.id)) {
    return {
        "success": false,
        "message": "缺少必要参数：id",
        "data": null
    };
}

// 删除主表记录（外键约束会自动级联删除关联表数据）
var deleteFun = @@sql(p)<%
    DELETE FROM hazard_remediation WHERE id = #{p.id}::numeric
%>;

var result = deleteFun(p);

return {
    "success": true,
    "message": "隐患整改单删除成功",
    "data": result
};
