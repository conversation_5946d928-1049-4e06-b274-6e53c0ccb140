// hazard_remediation_attachment_query.sql
// 查询隐患整改附件列表

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

var queryFun = @@sql(p)<%
    SELECT id, remediation_id, original_filename, stored_filename, file_size, file_type,
           s3_bucket, s3_key, upload_time, uploader
    FROM hazard_remediation_attachment
    WHERE remediation_id = #{p.remediationId}::numeric
    ORDER BY upload_time ASC
    LIMIT 100
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
    run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})
var data = queryFun(p);

return {
    "success": true,
    "message": "查询成功",
    "data": data
};
