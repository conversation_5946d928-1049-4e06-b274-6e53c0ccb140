// hazard_remediation_attachment_delete.sql
// 删除隐患整改附件记录

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 先查询附件信息，用于返回S3文件信息
var queryFun = @@sql(p)<%
    SELECT id, stored_filename, s3_bucket, s3_key, original_filename
    FROM hazard_remediation_attachment 
    WHERE id = #{p.id}::numeric 
    LIMIT 1
%>;

var attachmentInfo = queryFun(p);
if (collect.isEmpty(attachmentInfo)) {
    return {
        "success": false,
        "message": "附件记录不存在",
        "data": null
    };
}

// 删除附件记录
var deleteFun = @@sql(p)<%
    DELETE FROM hazard_remediation_attachment WHERE id = #{p.id}::numeric
%>;

var result = deleteFun(p);

return {
    "success": true,
    "message": "附件记录删除成功",
    "data": {
        "affectedRows": result,
        "attachmentInfo": attachmentInfo
    }
};
