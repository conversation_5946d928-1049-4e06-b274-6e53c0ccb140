// hazard_remediation_statistics.sql
// 隐患整改统计查询

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 按状态统计
var statusStatsFun = @@sql(p)<%
    SELECT 
        status,
        COUNT(*) as count,
        COUNT(CASE WHEN hazard_level = 'high' THEN 1 END) as high_risk_count,
        COUNT(CASE WHEN hazard_level = 'medium' THEN 1 END) as medium_risk_count,
        COUNT(CASE WHEN hazard_level = 'low' THEN 1 END) as low_risk_count
    FROM hazard_remediation 
    WHERE 1=1
    <if test="p.cityCode != null and p.cityCode != ''">
        AND city_code = #{p.cityCode}
    </if>
    <if test="p.startDate != null and p.startDate != ''">
        AND create_time >= #{p.startDate}::timestamp
    </if>
    <if test="p.endDate != null and p.endDate != ''">
        AND create_time <= #{p.endDate}::timestamp
    </if>
    GROUP BY status
    ORDER BY
        CASE status
            WHEN 'draft' THEN 1
            WHEN 'inProgress' THEN 2
            WHEN 'completed' THEN 3
            ELSE 4
        END
    LIMIT 10
%>;

// 按地市统计
var cityStatsFun = @@sql(p)<%
    SELECT 
        city_code,
        COUNT(*) as total_count,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
        COUNT(CASE WHEN status = 'inProgress' THEN 1 END) as in_progress_count,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        AVG(CASE WHEN actual_completion_date IS NOT NULL AND expected_completion_date IS NOT NULL 
            THEN EXTRACT(DAY FROM actual_completion_date - expected_completion_date) END) as avg_delay_days
    FROM hazard_remediation 
    WHERE 1=1
    <if test="p.startDate != null and p.startDate != ''">
        AND create_time >= #{p.startDate}::timestamp
    </if>
    <if test="p.endDate != null and p.endDate != ''">
        AND create_time <= #{p.endDate}::timestamp
    </if>
    GROUP BY city_code
    ORDER BY total_count DESC
    LIMIT 50
%>;

// 按隐患类型统计
var typeStatsFun = @@sql(p)<%
    SELECT 
        hazard_type,
        hazard_level,
        COUNT(*) as count
    FROM hazard_remediation 
    WHERE 1=1
    <if test="p.cityCode != null and p.cityCode != ''">
        AND city_code = #{p.cityCode}
    </if>
    <if test="p.startDate != null and p.startDate != ''">
        AND create_time >= #{p.startDate}::timestamp
    </if>
    <if test="p.endDate != null and p.endDate != ''">
        AND create_time <= #{p.endDate}::timestamp
    </if>
    GROUP BY hazard_type, hazard_level
    ORDER BY hazard_type, hazard_level
    LIMIT 50
%>;

var statusStats = statusStatsFun(${param});
var cityStats = cityStatsFun(${param});
var typeStats = typeStatsFun(${param});

return {
    "success": true,
    "message": "统计查询成功",
    "data": {
        "statusStats": statusStats,
        "cityStats": cityStats,
        "typeStats": typeStats
    }
};
