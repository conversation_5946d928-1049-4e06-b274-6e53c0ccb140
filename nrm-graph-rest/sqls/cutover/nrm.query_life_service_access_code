// nrm.query_life_service_access_code

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var dataSetFun = @@mybatis(p)<%
    <select >
        select scene.name scene_name,a.name area_name,m.access_code from pm_busi_user_scene scene
        inner join rm_area a on scene.region_id=a.id
        inner join pm_busi_user_scene_member m on scene.id= m.scene_id
        where scene.name like '%生命线%'
        and is_life='0' and a.name = #{p.area_name}
    </select>
 %>;

var pageQuery =  dataSetFun(${param});

run pageQuery.setPageInfo({
  "pageSize"    : #{pageSize}, // 页大小
  "currentPage" : #{currentPage}// 第n页
});

var pageInfo= pageQuery.pageInfo();
var data=pageQuery.data();

return {
'pageInfo': pageInfo,
'data': data
}