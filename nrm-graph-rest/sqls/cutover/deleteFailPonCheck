
// nrm.deleteFailPonCheck
hint FRAGMENT_SQL_COLUMN_CASE = "lower";


// rownum<1
var update02 = @@sql(p)<%

update pr_project_pon_check set row_status=0 where project_id=#{p.project_id}::numeric and action=#{p.action} and row_status=1 and (olt_ip,olt_port_code)
not in (
select distinct olt_ip,olt_port_code from pr_project_pon_check where project_id=#{p.project_id}::numeric and action=#{p.action} and row_status=1 and online_status='online'
)

%>;


var result02 = update02(${param});

return result02;

