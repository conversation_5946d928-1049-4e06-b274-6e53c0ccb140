create sequence seq_pm_project start with 1;

create table pm_project(
    id numeric(24) primary key,
    area_code varchar(200),
    name varchar(200),
    project_type_id numeric(12),
    create_op varchar(200),
    create_time timestamp,
    remark varchar(4000)
);


create table pe_project_cutover(
    project_id numeric(24) primary key,
    start_time timestamp,
    end_time timestamp,
    duty_op varchar(200),
    cause_id varchar(200),
    cause_name varchar(200),
    action_op varchar(200),
    status varchar(200),
    order_no varchar(200)
);

create table pr_project_res(
    id numeric(24) primary key,
    project_id numeric(24),
    entity_id numeric(24),
    entity_spec_id numeric(24),
    entity_code varchar(200),
    entity_name varchar(200),
    ip varchar(200),
    create_time timestamp
);

-- 工程业务分析批次号, action包含 影响分析、PON口检测
create table pr_project_service_action(
    id numeric(24) primary key,
    batch_no varchar(200),
    status varchar(200)
);
create unique index uidx_pr_project_service_action_batch on pr_project_service_action(batch_no);

create table pr_project_service(
    id numeric(24) primary key,
    project_id numeric(24),
    action varchar(200), -- 影响分析,割接前检测,割接后检测 三个动作
    batch_no varchar(200),
    crm_product_id numeric(24),
    access_code varchar(200),
    circuit_code varchar(200),
    service_type varchar(200),
    service_level varchar(200),
    customer varchar(1000),
    customer_manager varchar(200),
    customer_manager_phone varchar(200),
    is_lifecycle varchar(200),
    address varchar(1000),
    entity_id numeric(24),
    entity_code varchar(200),
    entity_name varchar(200),
    olt_ip varchar(200),
    olt_nm_ip varchar(200),
    olt_port_code varchar(200),
    access_device_code varchar(200),
    access_device_code_loid varchar(200),
    online_status varchar(200)
);

create index idx_pr_project_service01 on pr_project_pon_check(crm_product_id,project_id,action,row_status);

alter table pr_project_service drop column olt_ip;
alter table pr_project_service drop column olt_nm_ip;
alter table pr_project_service drop column olt_port_code;
alter table pr_project_service drop column access_device_code;
alter table pr_project_service drop column access_device_code_loid;
alter table pr_project_service drop column online_status;

create table pr_project_pon_check(
    id numeric(24) primary key,
    project_id numeric(24),
    action varchar(200),
    batch_no varchar(200),
    crm_product_id numeric(24),
    olt_ip varchar(200),
    olt_nm_ip varchar(200),
    olt_port_code varchar(200),
    access_device_code varchar(200),
    access_device_code_loid varchar(200),
    online_status varchar(200),
    row_status numeric(5)
);
create index idx_pr_project_pon_check01 on pr_project_pon_check(crm_product_id,project_id,action,row_status);




drop table pr_project_opt_group;
create table pr_project_opt_group(
	id numeric(24) primary key,
  project_id numeric(24),
  action varchar(200), -- 影响分析,割接前检测,割接后检测 三个动作
  batch_no varchar(200),
	opt_group_id numeric(24),
	opt_group_code varchar(200),
	opt_group_name varchar(200),
	opt_codes varchar(2000), --光路组中的光路使用逗号分隔
	effect_opt_codes varchar(2000), --本次割接受影响的光路使用逗号分隔
	opt_code_num numeric(12),
	effect_opt_code_num numeric(12),
	risk_check_result varchar(200), --双路由隐患风险检测结果
	cutover_check_result varchar(200), --割接场景下全阻、半阻判定
	row_status numeric(12)
);

create index idx_pr_project_opt_group_projectid on pr_project_opt_group(project_id);

alter table pr_project_opt_group alter column risk_check_result varchar(100000);