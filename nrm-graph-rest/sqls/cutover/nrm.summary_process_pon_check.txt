// nrm.summary_process_pon_check

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var queryFun = @@mybatis(p)<%
<select>
with t as (
select a.olt_ip olt_ip,a.olt_port_code olt_port_code,b.olt_ip pre_olt_ip,b.olt_port_code pre_olt_port,b.online_status pre_online_status,c.olt_ip post_olt_ip,c.olt_port_code post_olt_port,c.online_status post_online_status,
count(distinct c.crm_product_id) post_online_num,count(distinct b.crm_product_id) pre_online_num,count(distinct a.crm_product_id) num
from pr_project_pon_check a
left join pr_project_pon_check b on a.project_id=b.project_id and a.action=b.action and a.crm_product_id=b.crm_product_id and b.row_status=1  and a.id=b.id
left join pr_project_pon_check c on a.project_id=c.project_id and c.action=#{p.action} and a.crm_product_id=c.crm_product_id and c.row_status=1
where a.row_status=1 and a.project_id = #{p.project_id}::numeric and a.action='2'
group by a.olt_ip,a.olt_port_code,b.olt_ip,b.olt_port_code,c.olt_ip,c.olt_port_code,b.online_status,c.online_status
),t_all as
(
select olt_ip,olt_port_code,sum(num) all_num from t group by olt_ip,olt_port_code
),t_pre as
(
select olt_ip,olt_port_code,pre_online_status,sum(num) pre_num from t group by olt_ip,olt_port_code,pre_online_status
)
select t_all.all_num,t_pre.pre_num,t.* from t_all
left join t_pre on t_all.olt_ip=t_pre.olt_ip and t_all.olt_port_code=t_pre.olt_port_code
left join t on t_all.olt_ip=t.olt_ip and t_pre.olt_port_code=t.olt_port_code and t_pre.pre_online_status=t.pre_online_status
order by t.olt_ip,t.olt_port_code,t.pre_online_status,t.post_online_status
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};