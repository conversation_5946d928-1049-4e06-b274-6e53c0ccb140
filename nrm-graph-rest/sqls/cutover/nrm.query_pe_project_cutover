// nrm.query_pe_project_cutover

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
var queryFun = @@mybatis(p)<%
<select>

select proj.id,proj.area_code,proj.name,proj.project_type_id,proj.create_op,proj.create_time,
proj.remark,cutover.project_id,cutover.start_time,cutover.end_time,cutover.duty_op,cutover.action_op,
cutover.cause_id,cutover.cause_name,cutover.status,cutover.order_no
from pm_project proj
inner join pe_project_cutover cutover on proj.id = cutover.project_id

<if test="p.causeName != null and p.causeName != ''">
	and cutover.cause_name like '%'+#{p.causeName}+'%'
</if>
<if test="p.area_code != null and p.area_code != ''">
	and proj.area_code = #{p.area_code}
</if>
<if test="p.status != null and p.status != ''">
	and cutover.status = #{p.status}
</if>

order by create_time desc
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
	"pageInfo": pageInfo,
	"data": data
};