// insert_pm_opt_road_carry_cfs

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var saveFun = @@sql[](p)<%
insert into pm_opt_road_carry_cfs
(id, opt_road_code, access_code, circuit_code, crm_product_id)
values
(
    nextval('seq_pm_opt_road_carry_cfs'),
    #{p.opt_road_code},
    #{p.access_code},
    #{p.circuit_code},
    #{p.crm_product_id}::numeric
)
%>;


var result =  saveFun(${param}.carry_list);
return result ;