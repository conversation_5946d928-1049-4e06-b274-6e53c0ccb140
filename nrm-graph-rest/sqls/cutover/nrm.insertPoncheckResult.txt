// nrm.insertPoncheckResult

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var saveFun = @@sql[](p)<%
insert into pr_project_pon_check
(id,project_id, action, batch_no, crm_product_id, olt_ip, olt_nm_ip, olt_port_code, access_device_code, access_device_code_loid, online_status, row_status,obd_name,obd_code,obd_port_code,rx_power)
values
(
    nextval('seq_pm_project'),
    #{p.project_id}::numeric,
    #{p.action}::varchar,
    #{p.batch_no},
    #{p.crm_product_id}::numeric,
    #{p.olt_ip},
    #{p.olt_nm_ip},
    #{p.olt_port_code},
    #{p.access_device_code},
    #{p.access_device_code_loid},
    #{p.online_status},
    #{p.row_status}::int,
    #{p.obd_name},
    #{p.obd_code},
    #{p.obd_port_code},
    #{p.rx_power}
)
%>;

var result =  saveFun(${param}.pon_check_info);
return result ;