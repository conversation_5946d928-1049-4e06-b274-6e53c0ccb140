// query_cfs_influence_by_cm_net

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
import 'net.hasor.dataql.fx.basic.JsonUdfSource' as json;

var queryFun1 = @@mybatis(p)<%
<select>
	with t_node as (
	select net.id,net.name,net.code,net.spec_id,jx.id jx_id ,jx.name jx_name,jx.code jx_code,opt_road.code opt_road_code,
	opt_road.name opt_road_name,opt_road.a_physic_device_id,opt_road.z_physic_device_id,opt_road.a_port_id,opt_road.z_port_id,
		opt_road.a_physic_device_id  as a_road_device_id,opt_road.a_port_id as a_road_port_id ,
		opt_road.z_physic_device_id  as z_road_device_id,opt_road.z_port_id as z_road_port_id ,
		road_ad.name as a_road_device_name,
		road_zd.name as z_road_device_name,
		road_ap.code as a_road_port_code,
		road_zp.code as z_road_port_code,
		cable_ad.name as a_cable_device_name,
        cable_zd.name as z_cable_device_name,
        cable_ap.code as a_cable_port_code,
        cable_zp.code as z_cable_port_code
		from cm_net net
		left join ce_net_cable ce on net.id=ce.net_id
		left join cr_net_entity cr  on cr.net_id=net.id
		left join cm_link jx on jx.id=cr.entity_id and jx.spec_id=cr.entity_spec_id
		left join cr_link_link route2jx  on route2jx.lower_link_id=jx.id
		left join cm_link r  on r.id=route2jx.upper_link_id
		left join cr_link_link road2route on road2route.lower_link_id=r.id
		left join cm_link opt_road on  opt_road.id=road2route.upper_link_id
		left join cm_device road_ad on road_ad.id=opt_road.a_physic_device_id
        left join cm_device road_zd on road_zd.id=opt_road.z_physic_device_id
        left join cm_port road_ap on opt_road.a_port_id=road_ap.id
        left join cm_port road_zp on opt_road.z_port_id=road_zp.id
        left join cm_device cable_ad on cable_ad.id=jx.a_physic_device_id
        left join cm_device cable_zd on cable_zd.id=jx.z_physic_device_id
        left join cm_port cable_ap on cable_ap.id=jx.a_port_id
        left join cm_port cable_zp on cable_zp.id=jx.z_port_id

		where 1=1
		and  jx.spec_id=1131100003 and net.spec_id=1211200002 and opt_road.spec_id = 1132400006
						<if test="p.net_code != null  and p.net_code != ''">
			and net.code =  #{p.net_code}
		</if>
	),t_node2 as(
	select a_physic_device_id as physic_device_id,a_port_id as port_id,code as net_code,jx_code from t_node
	UNION
	select z_physic_device_id as physic_device_id,z_port_id as port_id,code as net_code,jx_code from t_node
	),t_node3 as (

	select t_node2.net_code,t_node2.jx_code,carrier.prod_id::VARCHAR as crm_prod_id from t_node2 join PROD_CARRIER_DEV_DAILY  carrier on  carrier.phy_eqp_id = t_node2.physic_device_id::VARCHAR  and  carrier.phy_port_id = t_node2.port_id::VARCHAR
    union
    select t_node.code as net_code,t_node.jx_code,carrier.crm_product_id::varchar from t_node inner join pm_opt_road_carry_cfs carrier on t_node.opt_road_code=carrier.opt_road_code

),t_node4 as (
select
              DISTINCT (prod.crm_prod_id,prod.cust_id ),
       prod.crm_prod_id,
       prod.ACCS_NBR_NO ACCS_NBR_NO,
       tp.product_name || '-' || prod.accs_nbr_no service_name,
       tp.product_name service_spec_name,
       cust.nm cust_name,
       custge.khjl_name,
       custge.khjl_phone,
       custge.p_name,
       custge.customer_level_name,
''  scene_name
  from  PROD_USE_RES_SERV_DAILY_NEW prod
  left JOIN cust_daily_info cust
    on prod.cust_id ::numeric = cust.cust_id
  left join cust_ge_info_daily custge
    on  prod.cust_id  = custge.cust_id
  left join PM_SRV_PRODUCT tp
    on prod.crm_prod_spec_id = tp.product_no
  AND TP.product_type_id = '12000024'
where 1=1
and  prod.crm_prod_id in (select crm_prod_id  from t_node3)
)
select t_node.*,t_node4.* from t_node
join t_node3 on t_node.code=t_node3.net_code and t_node.jx_code=t_node3.jx_code
join t_node4 on t_node3.crm_prod_id = t_node4.crm_prod_id
</select>
%>;



var pageQuery1 = queryFun1(${param});

// limit 1000
return  {
	  'cfs_influence' : pageQuery1
}