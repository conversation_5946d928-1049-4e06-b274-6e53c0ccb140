// nrm.get_cutover_project.sql

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "on";

var get_fun=@@sql(p)<%
select proj.id,proj.area_code,proj.name,proj.project_type_id,proj.create_op,proj.create_time,
       proj.remark,cutover.start_time,cutover.end_time,cutover.duty_op,cutover.action_op,
       cutover.cause_id,cutover.cause_name,cutover.status,cutover.order_no
from pm_project proj
inner join pe_project_cutover cutover on proj.id = cutover.project_id
where proj.id=#{p.id}::numeric limit 1%>;
var data = get_fun(${param});

return data;