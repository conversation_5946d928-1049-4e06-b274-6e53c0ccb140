
// nrm.delete_offline_pon_check
hint FRAGMENT_SQL_COLUMN_CASE = "lower";


// rownum<1
var update02 = @@sql(p)<%
update pr_project_pon_check t1 set row_status=0 where t1.project_id=#{p.project_id}::numeric and t1.row_status=1 and t1.action = #{p.action} and t1.online_status<>'online'
and exists (
select * from pr_project_pon_check t2 where t2.project_id=#{p.project_id}::numeric and t2.row_status=1 and t2.action = #{p.action}
and t1.crm_product_id = t2.crm_product_id and t2.online_status='online'
);
%>;

var update03 = @@sql(p)<%
update pr_project_pon_check t1 set row_status=0 where t1.project_id=#{p.project_id}::numeric and t1.row_status=1 and t1.action = #{p.action} and t1.online_status='unknown'
and exists (
select * from pr_project_pon_check t2 where t2.project_id=#{p.project_id}::numeric and t2.row_status=1 and t2.action = #{p.action}
and t1.crm_product_id = t2.crm_product_id and t2.online_status='unknown' and t1.batch_no<t2.batch_no
);
%>;

var update04 = @@sql(p)<%
update pr_project_pon_check t1 set row_status=0 where t1.project_id=#{p.project_id}::numeric and t1.row_status=1 and t1.action = #{p.action} and t1.online_status='unknown'
and exists (
select * from pr_project_pon_check t2 where t2.project_id=#{p.project_id}::numeric and t2.row_status=1 and t2.action = #{p.action}
and t1.crm_product_id = t2.crm_product_id and t2.online_status='offline'
);
%>;

var update05 = @@sql(p)<%
update pr_project_pon_check t1 set row_status=0 where t1.project_id=#{p.project_id}::numeric and t1.row_status=1 and t1.action = #{p.action}
and exists (
select * from pr_project_pon_check t2 where t2.project_id=#{p.project_id}::numeric and t2.row_status=1 and t2.action = #{p.action}
and t1.crm_product_id = t2.crm_product_id and t2.online_status=t1.online_status and t1.batch_no<t2.batch_no
);
%>;


var result02 = update02(${param});
var result03 = update03(${param});
var result04 = update04(${param});
var result05 = update05(${param});

return result02;