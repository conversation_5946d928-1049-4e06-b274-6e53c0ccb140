// nrm.query_project_service

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;
import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;
var queryFun = @@mybatis(p)<%
<select>
select distinct project_id,action,batch_no,access_code,circuit_code,service_type,service_level,customer,string_agg(customer_manager,',') as customer_manager,string_agg(customer_manager_phone,','),is_lifecycle,string_agg(address,',') as address,entity_id,entity_code,entity_name,row_status,customer_type,string_agg(line_no,',') as line_no,route_name,route_code,a_road_device_name,z_road_device_name,string_agg(a_road_port_code,',') as a_road_port_code,string_agg(z_road_port_code,',') as z_road_port_code,a_cable_device_name,z_cable_device_name,string_agg(a_cable_port_code,',') as a_cable_port_code,string_agg(z_cable_port_code,','),string_agg(customer_manager_org,',') as customer_manager_org,string_agg(customer_manager_post,','),dev_ip,day30_times
from
(select distinct project_id,action,batch_no,a.access_code,circuit_code,replace(a.service_type,'接入端','') service_type,service_level,customer,customer_manager,customer_manager_phone,is_lifecycle,address,entity_id,entity_code,entity_name,row_status,customer_type,line_no,route_name,route_code,a_road_device_name,z_road_device_name,a_road_port_code,z_road_port_code,a_cable_device_name,z_cable_device_name,a_cable_port_code,z_cable_port_code,customer_manager_org,customer_manager_post,dev_ip,day30_times
from pr_project_service a
where a.row_status=1 and a.action = '1'
<if test="p.project_id != null and p.project_id != ''">
    and a.project_id = #{p.project_id}::numeric
</if>
) t
group by project_id,action,batch_no,access_code,circuit_code,service_type,service_level,customer,is_lifecycle,entity_id,entity_code,entity_name,row_status,customer_type,route_name,route_code,a_road_device_name,z_road_device_name,a_cable_device_name,z_cable_device_name,dev_ip,day30_times
order by entity_code,line_no
</select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = pageQuery.pageInfo();
return {
    "pageInfo": pageInfo,
    "data": data
};