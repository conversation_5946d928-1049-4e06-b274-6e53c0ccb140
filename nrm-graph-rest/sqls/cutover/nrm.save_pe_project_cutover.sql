// nrm.pe_project_cutover_insert

hint FRAGMENT_SQL_COLUMN_CASE = "lower";

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var saveFun01 = @@sql(p)<%
insert into pm_project
(id, area_code , name, project_type_id, create_op, create_time, remark)
values
(
    #{p.id},
    #{p.area_code},
    #{p.name},
    #{p.project_type_id},
    #{p.create_op},
    now(),
    #{p.remark}
) ON CONFLICT(id) do update set name=#{p.name},area_code=#{p.area_code},project_type_id=#{p.project_type_id},remark=#{p.remark}
%>;


var saveFun02 = @@sql(p)<%
insert into pe_project_cutover
(project_id, start_time , end_time, duty_op, action_op,status,order_no)
values
(
    #{p.id},
    #{p.start_time}::timestamp,
    #{p.end_time}::timestamp,
    #{p.duty_op},
    #{p.action_op},
    #{p.status},
    #{p.order_no}
) ON CONFLICT(project_id) do update set start_time=#{p.start_time}::timestamp,end_time=#{p.end_time}::timestamp,
duty_op=#{p.duty_op},action_op=#{p.action_op},status=#{p.status},order_no=#{p.order_no}
%>;


var generateId = (data) -> {
    var newMap = collect.newMap(data);
	if (data['id'] == null || data['id'] == '' ) {
		var newId = nextId('seq_pm_project',24,'32100002' )
		run newMap.put('id',newId.shortId);
}
    return newMap.data()
};

var p = generateId(${param});
var result01 =  saveFun01(p);
var result02 =  saveFun02(p);
return p;