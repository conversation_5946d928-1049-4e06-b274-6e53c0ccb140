-- noinspection SqlNoDataSourceInspectionForFile

CREATE SPACE network_graph(vid_type=fixed_string(30));

create tag if not exists node (code string, name string, api_code string, api_version string);

alter tag node add (notes string);
alter tag node add (labels string);
alter tag node add (data_source_type string);

drop tag index index_node_code;
create tag index index_node on node();



create edge if not exists contains (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_contains on contains();

create edge if not exists inherit (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_inherit on inherit();

create edge if not exists end_with (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_end_with on end_with();


create edge if not exists carry (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_carry on carry();

create edge if not exists link (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_carry on link();

create edge if not exists alias_of (api_code string, api_version string, notes string, data_source_type string);
create edge index if not exists index_alias on alias_of();

/*
alter edge contains add (data_source_type string);
alter edge inherit add ( data_source_type string);
alter edge end_with add (data_source_type string);
alter edge carry add (data_source_type string);
alter edge link add (data_source_type string);
*/



insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "device": ("device", "设备", "nrm.query_node_device", "V20230806231005648","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "port": ("port", "端口", "nrm.query_node_port", "V20230813162938941","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "room": ("room", "机房", "nrm.query_node_room", "V20230814231554848","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "olt": ("olt", "olt", "nrm.query_node_olt", "V20230814225233612","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "access_device": ("access_device", "接入设备", "nrm.query_node_access_device", "V20230816152414086","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "user_terminal": ("user_terminal", "用户终端", "nrm.query_node_user_terminal", "V20230819151356546","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "pon_link": ("pon_link", "PON链路", "nrm.query_node_pon_link", "V20230816162352236","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "pon_port": ("pon_port", "PON端口", "nrm.query_node_pon_port", "V20230820103209811","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "cfs": ("cfs", "cfs", "nrm.query_node_cfs", "V20230820152811421","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "mstp_circuit": ("mstp_circuit", "mstp电路", "nrm.query_node_mstp_circuit", "V20231204100339018","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "logic_link": ("logic_link", "逻辑链路", "nrm.query_node_logic_link", "V20231205090152164","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "link": ("link", "链路", "nrm.query_node_link", "V20231205183758658","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "address": ("address", "地址", "nrm.query_node_address", "V20231209111308064","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "pon_sub_port": ("pon_sub_port", "PON端子口", "nrm.query_node_pon_subport", "V20240113191129331","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "cable_segment": ("cable_segment", "光缆段", "nrm.query_node_cable_segment", "V20240114094413425","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "access_link": ("access_link", "接入链路", "nrm.query_node_access_link", "V20231215142135779","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "opt_road": ("opt_road", "光路", "nrm.query_node_opt_road", "V20231215145236962","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "jxgx": ("jxgx", "局向光纤", "nrm.query_node_jxgx", "V20240116220043587","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "opt_route": ("opt_route", "光路路由", "nrm.query_node_opt_route", "V20240130175603733","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "ether_link": ("ether_link", "以太网链路", "nrm.query_node_ether_link", "V20240130190748772","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "pipe_segment": ("pipe_segment", "管道段", "nrm.query_node_pipe_segment", "V20240131142842173","","city");


insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "kb_cable_segment_effect": ("kb_cable_segment_effect", "光缆段影响业务宽表", "nrm.query_node_kb_cable_segment_carry_cfs", "V20240218142441421","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "cable": ("cable", "光缆", "nrm.query_node_cable", "V20240302085108985","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "opt_rent": ("opt_rent", "光纤出租", "nrm.query_node_opt_rent", "V20240205124645592","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "otn_circuit": ("otn_circuit", "OTN电路", "nrm.query_node_otn_circuit", "V20240320203835006","","ds_graph_js");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "otn_circuit_route": ("otn_circuit_route", "OTN电路路由", "nrm.query_node_otn_circuit_route", "V20240323114521986","","ds_graph_js");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "otn_link": ("otn_link", "OTN链路", "nrm.query_node_otn_link", "V20240320220430932","","ds_graph_js");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "otn_device": ("otn_device", "OTN设备", "nrm.query_node_otn_device", "V20240324205509089","","ds_graph_js");


insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "changtu_circuit": ("changtu_circuit", "长途电路", "nrm.query_node_changtu_circuit", "V20240415192705076","","ds_graph_js");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "changtu_link": ("changtu_link", "长途链路", "nrm.query_node_changtu_link", "V20240422143910096","","ds_graph_js");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "changtu_device": ("changtu_device", "长途设备", "nrm.query_node_changtu_device", "V20240422200328563","","ds_graph_js");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "ware": ("ware", "板卡", "nrm.query_node_ware", "V20240429085326134","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "fiber": ("fiber", "纤芯", "nrm.query_node_fiber", "V20240509123041343","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "zq_service": ("zq_service", "政企业务", "nrm.query_node_zq_service", "V20240602103717243","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "cable_reel": ("cable_reel", "光缆盘留", "nrm.query_node_reel", "V20240619202133789","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "facility": ("facility", "支撑设施", "nrm.query_node_facility", "V20240619213834305","","city");

insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "switch_up_link": ("switch_up_link", "交换机上联链路", "nrm.query_node_switch_up_link", "V20240713205453875","","city");
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "switch": ("switch", "交换机", "nrm.query_node_switch", "V20240713205725053","","city");

-- insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "product": ("product", "product", "graph_query_product", "0","","");

-- delete edge contains "room"->"device";

delete edge end_with "pon_link"->"port";
delete edge end_with "pon_link"->"pon_port";
delete edge carry "pon_link"->"cfs";
delete edge carry "cfs"->"product";
delete edge carry "cable_segment"->"opt_road";
delete edge carry "opt_road"->"logic_link";
delete edge carry "opt_road"->"mstp_circuit";
delete edge carry "cable_segment"->"cfs";
delete edge end_with "changtu_link"->"changtu_circuit";
delete edge end_with "logic"->"port";
delete edge end_with "cfs"->"zq_service";
delete edge carry "opt_road"->"opt_route";
delete edge carry "opt_route"->"logic_link";
delete edge carry "opt_route"->"mstp_circuit";



insert edge alias_of(api_code,api_version,notes,data_source_type) values "opt_road"->"opt_route":("nrm.query_edge_opt_road_carry_link","V20240130192257134","1", "city");
-- insert edge carry(api_code,api_version,notes,data_source_type) values "opt_route"->"logic_link":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "opt_road"->"logic_link":("nrm.query_edge_opt_road_carry_link","V20240130192257134","1", "city");
-- insert edge carry(api_code,api_version,notes,data_source_type) values "opt_route"->"mstp_circuit":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge contains(api_code,api_version,notes,data_source_type) values "room"->"device":("nrm.query_edge_room_contains_device","V20230814232251411","1", "city");
insert edge contains(api_code,api_version,notes,data_source_type) values "device"->"port":("nrm.query_edge_device_contains_port","V20230813181948816","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "pon_link"->"olt":("nrm.query_edge_pon_link_end_with_device","V20230816091024745","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "pon_link"->"access_device":("nrm.query_edge_pon_link_end_with_device","V20230816091024745","1", "city");
insert edge link(api_code,api_version,notes,data_source_type) values "access_device"->"user_terminal":("nrm.query_edge_device_link_device","V20230820000536341","1", "city");
insert edge link(api_code,api_version,notes,data_source_type) values "olt"->"access_device":("nrm.query_edge_device_link_device","V20230820000536341","1", "city");
insert edge contains(api_code,api_version,notes,data_source_type) values "olt"->"pon_port":("nrm.query_edge_device_contains_port","V20230813181948816","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "pon_port"->"port":("nrm.query_edge_port_inherit_port","V20230820104613760","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "olt"->"device":("nrm.query_edge_device_inherit_device","V20230820103839122","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "access_device"->"device":("nrm.query_edge_device_inherit_device","V20230820103839122","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "user_terminal"->"device":("nrm.query_edge_device_inherit_device","V20230820103839122","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "user_terminal"->"cfs":("nrm.query_edge_device_carry_cfs","V20230820160337866","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "logic_link"->"mstp_circuit":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "logic_link"->"link":("nrm.query_edge_link_inherit_link","V20231205184407460","1", "city");
insert edge inherit(api_code,api_version,notes,data_source_type) values "mstp_circuit"->"link":("nrm.query_edge_link_inherit_link","V20231205184407460","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "link"->"device":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "logic_link"->"access_device":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");


insert edge end_with(api_code,api_version,notes,data_source_type) values "pon_link"->"pon_sub_port":("nrm.query_edge_link_end_with_port","V20231208184317296","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "pon_port"->"pon_sub_port":("nrm.query_edge_pon_carry_subport","V20240113190407345","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "address"->"cfs":("nrm.query_edge_address_carry_cfs","V20231209112243070","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "cable_segment"->"jxgx":("nrm.query_edge_cable_segment_carry_jxgx","V20240116214448269","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "jxgx"->"opt_road":("nrm.query_edge_jxgx_carry_opt_road","V20240116220855766","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "cable_segment"->"device":("nrm.query_edge_cable_end_with_device","V20240117152403656","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "jxgx"->"device":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "opt_road"->"device":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "cable"->"jxgx":("nrm.query_edge_cable_carry_jxgx","V20240302090019682","1", "city");
-- 刘旭添加
-- 新增接入链路、光路node
--接入链路 end_with 接入设备、 用户终端
insert edge end_with(api_code,api_version,notes,data_source_type) values "access_link"->"user_terminal":("nrm.query_edge_access_link_end_with_device","V20231215155207122","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "access_link"->"access_device":("nrm.query_edge_access_link_end_with_device","V20231215155207122","1", "city");
-- 光路carry PON链路
insert edge carry(api_code,api_version,notes,data_source_type) values "opt_road"->"pon_link":("nrm.query_edge_opt_road_carry_pon_link","V20231215144835331","1", "city");
 -- 接入链路inherit链路
insert edge inherit(api_code,api_version,notes,data_source_type) values "access_link"->"link":("nrm.query_edge_link_inherit_link","V20231205184407460","1", "city");
 -- 光路inherit链路
insert edge inherit(api_code,api_version,notes,data_source_type) values "opt_road"->"link":("nrm.query_edge_link_inherit_link","V20231205184407460","1", "city");
 -- PON链路inherit链路
insert edge inherit(api_code,api_version,notes,data_source_type) values "pon_link"->"link":("nrm.query_edge_link_inherit_link","V20231205184407460","1", "city");
-- 链路 end_with 端口
insert edge end_with(api_code,api_version,notes,data_source_type) values "link"->"port":("nrm.query_edge_link_end_with_port","V20231208184317296","1", "city");
-- 光路承载以太网链路
insert edge carry(api_code,api_version,notes,data_source_type) values "opt_road"->"ether_link":("nrm.query_edge_opt_road_carry_link","V20240130192257134","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "pipe_segment"->"cable_segment":("nrm.query_edge_pipe_carry_cable","V20240131111522326","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "ether_link"->"access_device":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "ether_link"->"user_terminal":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "opt_road"->"opt_rent":("nrm.query_edge_opt_road_carry_link","V20240130192257134","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "opt_rent"->"cfs":("nrm.query_edge_link_carry_cfs","V20240213084547373","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "mstp_circuit"->"cfs":("nrm.query_edge_link_carry_cfs","V20240213084547373","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "cable_segment"->"kb_cable_segment_effect":("nrm.query_edge_cable_inherit_cable","V20240218162901465","1", "city");

insert edge carry(api_code,api_version,notes,data_source_type) values "otn_link"->"otn_circuit_route":("nrm.query_edge_otn_link_carry_link","V20240323192111628","1", "ds_graph_js");
insert edge carry(api_code,api_version,notes,data_source_type) values "otn_circuit_route"->"otn_circuit":("nrm.query_edge_otn_link_carry_circuit","V20240323171044741","1", "ds_graph_js");
insert edge end_with(api_code,api_version,notes,data_source_type) values "otn_link"->"otn_device":("nrm.query_edge_otn_link_end_with_device","V20240324211155880","1", "ds_graph_js");


insert edge carry(api_code,api_version,notes,data_source_type) values "changtu_link"->"changtu_circuit":("nrm.query_edge_changtu_link_carry_link","V20240415193637300","1", "ds_graph_js");
insert edge end_with(api_code,api_version,notes,data_source_type) values "changtu_link"->"changtu_device":("nrm.query_edge_changtu_link_end_with_device","V20240422202015250","1", "ds_graph_js");
insert edge contains(api_code,api_version,notes,data_source_type) values "ware"->"port":("nrm.query_edge_ware_contains_port","V20240429100917503","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "logic_link"->"port":("nrm.query_edge_link_end_with_port","V20231208184317296","1", "city");


insert edge carry(api_code,api_version,notes,data_source_type) values "fiber"->"jxgx":("nrm.query_edge_fiber_carry_jxgx","V20240509140646765","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "fiber"->"device":("nrm.query_edge_fiber_end_with_device","V20240509183559483","1", "city");

insert edge carry(api_code,api_version,notes,data_source_type) values "cfs"->"zq_service":("nrm.query_edge_cfs_carry_zq_service","V20240602104833270","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "mstp_circuit"->"zq_service":("nrm.query_edge_mstp_circuit_carry_zq_service","V20240602104224534","1", "city");

insert edge contains(api_code,api_version,notes,data_source_type) values "cable_segment"->"cable_reel":("nrm.query_edge_cable_contain_reel","V20240619204053959","1", "city");
insert edge contains(api_code,api_version,notes,data_source_type) values "facility"->"cable_reel":("nrm.query_edge_facility_contain_reel","V20240619211236181","1", "city");

insert edge carry(api_code,api_version,notes,data_source_type) values "logic_link"->"pon_link":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "logic_link"->"ether_link":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "ether_link"->"olt":("nrm.query_edge_ether_link_end_with_device","V20240717214601800","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "ether_link"->"switch":("nrm.query_edge_ether_link_end_with_device","V20240717214601800","1", "city");
insert edge end_with(api_code,api_version,notes,data_source_type) values "switch_up_link"->"switch":("nrm.query_edge_link_end_with_device","V20231205110308572","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "logic_link"->"switch_up_link":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");


-- 硬跳
-- 硬跳承载光链路路由
-- 局向光纤承载光链路路由
insert vertex node (code,name,api_code,api_version,labels,data_source_type) values "jump_link": ("jump_link", "硬跳", "nrm.query_node_jump_link", "V20240820145311010","","city");
insert edge carry(api_code,api_version,notes,data_source_type) values "jump_link"->"opt_route":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");
insert edge carry(api_code,api_version,notes,data_source_type) values "jxgx"->"opt_route":("nrm.query_edge_link_carry_link","V20231205101229649","1", "city");

/*
lookup on node yield vertex as n;

lookup on node yield id(vertex);

lookup on node yield properties(vertex) as v;

-- lookup on node where contains(node.name,"备") yield properties(vertex) as v;


lookup on node where node.name == "设备" yield properties(vertex) as v;

match p=(d:node{code: "device"})-[r*]-(c:node {code: "cfs"}) match p2=(c)-->(prod:node) return p2 limit 1


find shortest path  from "device" to "cfs" over * BIDIRECT yield path as p|yield nodes($-.p) as n, relationships($-.p) as r;


find shortest path  from "device" to "cfs" over * BIDIRECT yield path as p|UNWIND nodes($-.p) as n, relationships($-.p) as r | yield $-.n, yield $-.r;

find shortest path  from "device" to "cfs" over * BIDIRECT yield path as p|UNWIND relationships($-.p) as r | yield $-.r;


go 1 step  from "device"  over * BIDIRECT where id($$)=="port" yield src(edge) as src,dst(edge) as dst;

go 2 step  from "device"  over * BIDIRECT yield id($^) as start, src(edge) as src,type(edge) as edge_type,dst(edge) as dst, id($$) as end, edge as e;
get subgraph 5 steps from "device" yield vertices AS n, EDGES AS r;

*/

