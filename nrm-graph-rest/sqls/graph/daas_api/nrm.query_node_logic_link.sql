// nrm.query_node_logic_link.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// -- and l.spec_id not in (1131100001, 1131100003)
var queryFun = @@mybatis(p)<%
<select>
select l.id,l.name,l.code,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.a_port_id,l.z_port_id,zcp.code as z_port_code,acp.code  as a_port_code,l.nm_code as nm_code,acp.spec_id a_port_spec_id,zcp.spec_id z_port_spec_id
from cm_link l
left join  cm_port zcp on zcp.id =l.z_port_id 
left join  cm_port acp on acp.id =l.a_port_id 
where 1=1 and not exists (
	select 1 from cr_link_link carry where l.id=carry.lower_link_id and carry.upper_link_spec_id=1131200002
)
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
</select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================