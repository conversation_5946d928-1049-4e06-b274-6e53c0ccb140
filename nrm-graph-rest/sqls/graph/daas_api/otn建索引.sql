create index idx_t_trans_ne_name on t_trans_ne  using gist (full_name);
create index idx_t_trans_ne_aliasname on t_trans_ne  using gist (aliasname);

create index idx_t_snc_userlabel1 on t_snc using gist(userlabel);
create index idx_t_snc_aliasname on t_snc using gist(aliasname);

alter table t_fdfr add constraint pk_t_fdfr primary key (fdfr_id);
create index idx_t_fdfr_aliasname on t_fdfr using gist(aliasname);

alter table t_trans_ptp add constraint pk_t_trans_port_ptp primary key (port_id);

alter table t_trans_link add constraint pk_t_trans_link primary key (link_id);

alter table t_snc add constraint pk_t_snc primary key (snc_id);
create index idx_t_snc_a on t_snc(a_ne_id);
create index idx_t_snc_z on t_snc(z_ne_id);

create index idx_t_snc_ap on t_snc(a_ptp_id);
create index idx_t_snc_zp on t_snc(z_ptp_id);

create index idx_t_trans_link_ap on t_trans_link(a_ptp_id);
create index idx_t_trans_link_zp on t_trans_link(z_ptp_id);

alter table  t_snc_route add constraint pk_t_snc_route primary key (route_id);
create index idx_t_snc_route_obj on t_snc_route(route_obj_id);
create index idx_t_snc_route_snc on t_snc_route(snc_id);
create index t_snc_route_id on t_snc_route(route_id);

create index idx_t_fdfr_route_id on t_fdfr_route(route_id);
create index idx_t_fdfr_route_fdfr_id on t_fdfr_route(fdfr_id);
create index idx_t_fdfr_route_route_obj_id on t_fdfr_route(route_obj_id);

