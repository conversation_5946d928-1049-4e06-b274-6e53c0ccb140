// nrm.query_node_switch_up_link.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select l.id,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.name,l.code,l.a_port_id,l.z_port_id from cm_link l
        inner join cm_device ad on l.a_physic_device_id = ad.id
        inner join cm_device zd on l.z_physic_device_id = zd.id
		where 1=1 and l.spec_id in (1132100021)
		and ((ad.spec_id =1024600001 and zd.spec_id=1024600002 ) or (ad.spec_id =1024600002 and zd.spec_id=1024600001 ))
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>

		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================