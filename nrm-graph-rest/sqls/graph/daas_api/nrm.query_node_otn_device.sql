// nrm.query_node_otn_device.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select
            ne_id as id,
            vendor_obj_name,
            software_version,
            ne_model,
            ne_category,
            installed_part_number,
            ne_stat,
            is_gateway_me,
            ne_ip,
            ason_enable,
            is_virtual_ne,
            aliasname as name,
            data_domain_id,
            res_domain_id,
            ems_id,
            is_ovpn,
            data_source,
            aliasname as code,
            vendor_name
        from ct_otn_schema.t_trans_ne d
		where 1=1
		<if test="p.ids != null">
			and d.ne_id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and d.full_name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and d.aliasname = #{p.code}
		</if>

    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================