// nrm.query_node_otn_link.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
		with t_edge as (
		select l.snc_id as id,l.aliasname as name,COALESCE (l.aliasname,l.userlabel) as code ,ap.full_name as a_port_code,zp.full_name as z_port_code,l.a_port_id,l.z_port_id,
                l.a_ptp_id,l.z_ptp_id,l.a_ne_id,l.z_ne_id,l.direction,l.layer_rate,l.data_domain_id,
                l.is_cross_domain,l.cross_obj_id,l.the_state,l.a_ems_id,l.z_ems_id,l.data_source,
                l.vendor_obj_name,l.is_split,l.protect_snc_id,l.snc_order,l.a_obj_type,l.a_snc_id,
                l.z_obj_type,l.z_snc_id,l.delay,l.snc_state,l.parent_snc_id,l.frequency,l.frequency_width,
                l.snc_type,l.channel_type,l.is_end_to_end_valid,l.static_protection_level,l.insert_time,
                l.update_time,l.is_ovpn,l.ovpn_id,l.a_board_id,l.z_board_id,l.a_shelf_id,l.z_shelf_id,l.res_id,
                l.res_domain_id,l.a_data_domain_id,l.z_data_domain_id,l.a_region_id,l.z_region_id,
                l.a_site_id,l.z_site_id,l.update_id,l.receive_time,l.is_ason_snc,l.is_vc4_servertrail,
                l.signal_route_type,l.trace_id,
                l.a_ne_id as a_physic_device_id,l.z_ne_id as z_physic_device_id,
                l.channel_type as spec_id,channel_type.dict_name as spec_name
		from ct_otn_schema.t_snc l
		left join ct_otn_schema.t_trans_port_ptp ap on l.a_ptp_id = ap.port_id
		left join ct_otn_schema.t_trans_port_ptp zp on l.z_ptp_id = zp.port_id
		left join ct_otn_schema.pm_otn_dict channel_type on channel_type.table_code='T_SNC' and channel_type.column_code='CHANNEL_TYPE' and channel_type.dict_value=l.channel_type

		union all

		select l.link_id as id,l.aliasname as name,l.aliasname as code ,ap.full_name as a_port_code,zp.full_name as z_port_code,a_ptp_id as a_port_id,z_ptp_id as z_port_id,
        		l.a_ptp_id,l.z_ptp_id,l.a_ne_id,l.z_ne_id,l.direction,null as layer_rate,null as data_domain_id,
        		null as is_cross_domain, null as cross_obj_id, null as the_state,a_ems_id,null as z_ems_id,null as data_source,
        		l.vendor_obj_name,null as  is_split,null as protect_snc_id,null as snc_order,null as a_obj_type,null as a_snc_id,
        		null as z_obj_type,null as z_snc_id,null as delay,null as snc_state,null as parent_snc_id,null as frequency,null as frequency_width,
        		null as snc_type,null as channel_type,null as is_end_to_end_valid,null as static_protection_level,null as insert_time,
        		null as update_time,null as is_ovpn,l.ovpn_id,null as a_board_id,null as z_board_id,null as a_shelf_id,null as z_shelf_id,null as res_id,
        		null as res_domain_id,null as a_data_domain_id,null as z_data_domain_id,null as a_region_id,null as z_region_id,
        		null as a_site_id,null as z_site_id,null as update_id,null as receive_time,null as is_ason_snc,null as is_vc4_servertrail,
        		null as signal_route_type,null as trace_id,
        		l.a_ne_id as a_physic_device_id,l.z_ne_id as z_physic_device_id,
        		'99' as spec_id,'拓扑连接' as spec_name
		from ct_otn_schema.t_trans_link l
        left join ct_otn_schema.t_trans_port_ptp ap on l.a_ptp_id = ap.port_id
        left join ct_otn_schema.t_trans_port_ptp zp on l.z_ptp_id = zp.port_id
		)
		select * from t_edge l
		where 1=1
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}
			</foreach>
		</if>
		
		<if test="p.codes != null">
			and l.code in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>

		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================