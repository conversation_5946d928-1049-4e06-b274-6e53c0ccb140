// nrm.query_node_user_terminal.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 		-- where 1=1 and spec_id in (1028492006,1028400006,1029400004,1024192001,1024192002,1024192003,1024192004,1024192005,1024192006)

var queryFun = @@mybatis(p)<%
    <select>
        select id,name,code,spec_id,device_type_id,model_id,manufactor_id from cm_device
        where 1=1 and spec_id in (1028492006,1028400006,1029400004,1024192001,1024192002,1024192003,1024192004,1024192005,1024192006)
		<if test="p.ids != null">
			and id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.spec_id_s != null">
			and spec_id in
			<foreach collection="p.spec_id_s" item="spec_id" open="(" close=")" separator=",">
				#{spec_id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================