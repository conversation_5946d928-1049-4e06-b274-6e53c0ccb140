hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
		with t_link as (
        select l.id,l.name,l.code,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.a_port_id,l.z_port_id,
		s.access_code , 
		zcp.code  as z_port_code,
		acp.code  as a_port_code,
		l.nm_code  as nm_code,
		acp.spec_id as a_port_spec_id,
		zcp.spec_id as z_port_spec_id
		from cm_link l
		inner join pe_link le on l.id=le.link_id
		left join rr_service_entity s2l on s2l.entity_id=l.id and s2l.entity_spec_id=l.spec_id
		left join rm_service s on s2l.service_id=s.id
		left join  cm_port zcp   on zcp.id =l.z_port_id 
		left join  cm_port acp   on acp.id =l.a_port_id 
		where le.service_type_id in (81708243,81708247,81708253,81708254)
		and not exists (select 1 from cr_link_link ll where ll.lower_link_id=l.id)
		union
		select l.id,l.name,l.code,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.a_port_id,l.z_port_id,s.access_code ,
		zcp.code  as z_port_code,
		acp.code  as a_port_code,
		l.nm_code  as nm_code,
		acp.spec_id as a_port_spec_id,
		zcp.spec_id as z_port_spec_id
		from rm_service s
		inner join rr_service_entity s2l on s2l.service_id=s.id
        inner join cm_link l on s2l.entity_id=l.id and s2l.entity_spec_id=l.spec_id
		left join pe_link le on l.id=le.link_id
			left join  cm_port zcp   on zcp.id =l.z_port_id 
		left join  cm_port acp   on acp.id =l.a_port_id 
		where s.spec_id in (2080,2840,2217,3933,2580,2089,2782,3861,2105,2464,4390,2733,3153,2082,2851,2839,2083,2124,2176,2842,2915,2706,2834,2716,2118,2783,700001221,2462,2693,2473,2805,9310192086,2784,2122)
		)
		select * from t_link l where 1=1
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		
		<if test="p.codes != null">
			and l.code in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>
		<if test="p.access_code != null and p.access_code != ''">
			and l.access_code = #{p.access_code}
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================