// nrm.query_node_cfs.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select s.id,(s.name||'-'||s.access_code) as name,s.access_code code,s.spec_id,s.access_code,s.a_access_code,s.z_access_code,cl.code link_code,cl.name link_name from rm_service s
		left join rr_service_entity rse on s.id = rse.service_id and rse.spec_id='2311611310000'
		left join cm_link cl on cl.id=rse.entity_id
		where 1=1 and access_code is not null
		<if test="p.ids != null">
			and s.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.id != null and p.id != ''">
			and s.id = #{p.id}::numeric
		</if>
		
		<if test="p.access_code != null and p.access_code != ''">
			s.and access_code = #{p.access_code}
		</if>
		<if test="p.access_code_s != null">
			and s.access_code in
			<foreach collection="p.access_code_s" item="access_code" open="(" close=")" separator=",">
				#{access_code}
			</foreach>
		</if>
		<if test="p.a_access_code != null and p.a_access_code != ''">
			and s.a_access_code = #{p.a_access_code}
		</if>
		<if test="p.z_access_code != null and p.z_access_code != ''">
			and s.z_access_code = #{p.z_access_code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and s.spec_id = #{p.spec_id}
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================