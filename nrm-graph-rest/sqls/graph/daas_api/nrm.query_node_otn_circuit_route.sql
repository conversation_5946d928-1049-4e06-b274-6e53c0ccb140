// nrm.query_node_otn_circuit_route.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
		select snc_id as id,aliasname,userlabel,a_port_id,z_port_id,a_ptp_id,z_ptp_id,a_ne_id,z_ne_id,direction,layer_rate,data_domain_id,is_cross_domain,cross_obj_id,the_state,a_ems_id,z_ems_id,data_source,vendor_obj_name,is_split,protect_snc_id,snc_order,a_obj_type,a_snc_id,z_obj_type,z_snc_id,delay,snc_state,parent_snc_id,frequency,frequency_width,snc_type,channel_type,is_end_to_end_valid,static_protection_level,insert_time,update_time,is_ovpn,ovpn_id,a_board_id,z_board_id,a_shelf_id,z_shelf_id,res_id,res_domain_id,a_data_domain_id,z_data_domain_id,a_region_id,z_region_id,a_site_id,z_site_id,update_id,receive_time,is_ason_snc,is_vc4_servertrail,signal_route_type,trace_id
		from ct_otn_schema.t_snc l
		where 1=1
		<if test="p.ids != null">
			and l.snc_id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}
			</foreach>
		</if>
		
		<if test="p.codes != null">
			and l.userlabel in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>

		
		<if test="p.code != null and p.code != ''">
			and ( l.userlabel = #{p.code} or l.aliasname = #{p.code})
		</if>

		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================