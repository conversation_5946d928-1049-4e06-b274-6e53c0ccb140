// nrm.query_node_link.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
        select l.id,l.name,l.code,l.spec_id,l.a_physic_device_id,l.z_physic_device_id,l.a_port_id,l.z_port_id,le.service_type_id,dic.desc_china service_type_name,s.res_type spec_name, 
		ad.name a_name,zd.name z_name,ap.code a_port_code,zp.code z_port_code,l.nm_code,ap.spec_id as a_port_spec_id,zp.spec_id as z_port_spec_id from cm_link l
		left join pe_link le on l.id=le.link_id
		left join pm_pub_res_type s on s.res_type_id=l.spec_id
		left join pm_pub_restriction dic on dic.serial_no=le.service_type_id
		left join cm_device ad on ad.id=l.a_physic_device_id
		left join cm_device zd on zd.id=l.z_physic_device_id
		left join cm_port ap on l.a_port_id=ap.id
		left join cm_port zp on l.z_port_id=zp.id
		where 1=1 
		<if test="p.ids != null">
			and l.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.code != null and p.code != ''">
			and l.code = #{p.code}
		</if>
		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize}, #{currentPage});
return data;

// ===================================================================================