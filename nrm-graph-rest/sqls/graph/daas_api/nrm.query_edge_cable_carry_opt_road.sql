// nrm.query_edge_cable_segment_carry_opt_road

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_edge 
as (
select lc.id,c.id a_id,c.spec_id a_spec_id,l.id b_id,l.spec_id b_spec_id from cm_link l
inner join cr_link_link road2route on l.id=road2route.upper_link_id
inner join cm_link r on road2route.lower_link_id=r.id
inner join cr_link_link route2jx on r.id=route2jx.upper_link_id
inner join cm_link jx on route2jx.lower_link_id=jx.id
inner join cr_link_cable lc on jx.id=lc.link_id
inner join cm_cable f on lc.cable_id=f.id
inner join cm_cable c on f.parent_id=c.id
)
select * from t_edge where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================