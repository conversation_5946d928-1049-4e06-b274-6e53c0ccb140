// nrm.query_node_access_device.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;

// 100264 用户机房
var queryFun = @@mybatis(p)<%
    <select>
        select d.id,d.name,d.code,d.spec_id,d.device_type_id,d.model_id,d.manufactor_id,n.code as loid,
               f.id room_id,f.code room_code,f.name room_name,f.facility_type_id room_type_id from cm_device d
		left join rr_number_entity n2e on n2e.entity_id=d.id and n2e.entity_spec_id=d.spec_id
		left join v_rm_number n on n2e.number_id=n.id and n.spec_id=2020200001
        left join cm_facility f on d.facility_id=f.id
		where 1=1 and d.spec_id not in (1028200001) and (d.facility_id is null or f.facility_type_id =100264)
		<if test="p.ids != null">
			and d.id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}::numeric
			</foreach>
		</if>
		<if test="p.name != null and p.name != ''">
			and d.name = #{p.name}
		</if>
		<if test="p.code != null and p.code != ''">
			and d.code = #{p.code}
		</if>
		<if test="p.spec_id != null and p.spec_id != ''">
			and d.spec_id = #{p.spec_id}
		</if>
		<if test="p.loid != null and p.loid != ''">
			and n.code = #{p.loid}
		</if>
		<if test="p.loids != null and p.loids != ''">
			and n.code in
			<foreach collection="p.loids" item="loid" open="(" close=")" separator=",">
				#{loid}
			</foreach>
		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================