// nrm.query_edge_device_link_device

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
with t_edge 
as (
select l.id,ad.id a_id,ad.spec_id a_spec_id,bd.id b_id,bd.spec_id b_spec_id from cm_device ad
inner join cm_link l on ad.id=l.a_physic_device_id
inner join cm_device bd on bd.id=l.z_physic_device_id
where ad.id &lt;&gt; bd.id
union all
select l.id,ad.id a_id,ad.spec_id a_spec_id,bd.id b_id,bd.spec_id b_spec_id from cm_device ad
inner join cm_link l on ad.id=l.z_physic_device_id
inner join cm_device bd on bd.id=l.a_physic_device_id
where ad.id &lt;&gt; bd.id
)
		select distinct * from t_edge
		where 1=1
		<if test="p.a == null and p.b == null">
			and 1=2
		</if>
		<if test="p.a != null">
			and (a_id,a_spec_id) in
			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		<if test="p.b != null">
			and (b_id,b_spec_id) in
			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
				(#{previous.id}::numeric, #{previous.spec_id}::numeric)
			</foreach>
		</if>
		
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================