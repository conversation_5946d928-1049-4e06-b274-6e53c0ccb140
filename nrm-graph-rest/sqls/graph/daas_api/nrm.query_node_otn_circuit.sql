// nrm.query_node_otn_circuit.sql



hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

import 'net.hasor.dataql.fx.basic.CollectionUdfSource' as collect;


var queryFun = @@mybatis(p)<%
    <select>
		select fdfr_id as id,aliasname,userlabel,
		a_port_id,z_port_id,a_ptp_id,z_ptp_id,a_ne_id,z_ne_id,
		direction,layer_rate,data_domain_id,is_cross_domain,
		fdfr_state,fdfr_type,service_type,vendor_obj_name,insert_time,update_time,
		a_board_id,z_board_id,a_data_domain_id,z_data_domain_id,receive_time,
		is_flexible,admin_state,fd_id,trace_id,cir,cbs,pir,pbs,is_end_to_end_valid,
		update_id,carrying_type
		from ct_otn_schema.t_fdfr l
		where 1=1
		<if test="p.ids != null">
			and l.snc_id in
			<foreach collection="p.ids" item="id" open="(" close=")" separator=",">
				#{id}
			</foreach>
		</if>
		
		<if test="p.codes != null">
			and l.aliasname in
			<foreach collection="p.codes" item="code" open="(" close=")" separator=",">
				#{code}
			</foreach>
		</if>
		
		<if test="p.code != null and p.code != ''">
			and l.aliasname = #{p.code}
		</if>

		<if test="p.is_empty">
			and 1 = 2 
		</if>
    </select>
%>;

var setParam = (p) -> {
    var newMap = collect.newMap(p);
	run newMap.put('is_empty',collect.isEmpty(p));
    return newMap.data()
};

var p = setParam(${param})


var pageQuery = queryFun(p);
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================