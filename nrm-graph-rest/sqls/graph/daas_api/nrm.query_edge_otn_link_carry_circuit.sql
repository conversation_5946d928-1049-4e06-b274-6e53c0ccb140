// nrm.query_edge_otn_link_carry_circuit

hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;


var queryFun = @@mybatis(p)<%
    <select>
		with  t_edge as (
        	select
        		ll.route_id id,ll.route_obj_id a_id,ll.fdfr_id b_id,
        		ll.fdfr_id upper_link_id,ll.route_obj_id lower_link_id,
        		seg_no,grp_no,order_no,direction,a_ne_id,a_ptp_id,a_port_id,
        		z_ne_id,z_ptp_id,z_port_id,data_domain_id,route_type,
        		connect_direct,trace_id,route_obj_state,
        		cast(1 as int) layer
        	from ct_otn_schema.t_fdfr_route ll
        )
        select * from t_edge where 1=1
        <if test="p.a == null and p.b == null">
        			and 1=2
        		</if>
        		<if test="p.a != null">
        			and (a_id) in
        			<foreach collection="p.a" item="previous" open="(" close=")" separator=",">
        				(#{previous.id})
        			</foreach>
        		</if>
        		<if test="p.b != null">
        			and (b_id) in
        			<foreach collection="p.b" item="previous" open="(" close=")" separator=",">
        				(#{previous.id})
        			</foreach>
        		</if>
    </select>
%>;

var pageQuery = queryFun(${param});
run pageQuery.setPageInfo({
    "pageSize"    : #{pageSize}, // 页大小
    "currentPage" : #{currentPage}   // 第3页
});
var data=pageQuery.data();
var pageInfo = mockPageInfo(#{pageSize},#{currentPage});
return data;

// ===================================================================================