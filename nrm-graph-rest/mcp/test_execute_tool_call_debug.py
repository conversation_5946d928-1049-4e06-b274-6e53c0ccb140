import asyncio
import json
import os
import time
from dotenv import load_dotenv
import sys
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 加载环境变量
load_dotenv()

# 创建stdio服务器参数
server_params = StdioServerParameters(
    command="python",  # 可执行文件
    args=["mcp/mcp_idc_sql_server_debug.py"],  # 命令行参数
    env=os.environ  # 传递当前环境变量
)

# 调试日志文件
DEBUG_LOG_FILE = "mcp_client_debug.log"

def log_debug(message):
    """写入调试日志"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    with open(DEBUG_LOG_FILE, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")

async def execute_tool_call(tool_call: dict, session) -> str:
    """
    执行工具调用
    
    Args:
        tool_call: 工具调用参数，包含name和input
        session: MCP会话
        
    Returns:
        工具执行结果
    """
    log_debug(f"开始执行工具调用: {tool_call}")
    
    if "error" in tool_call:
        log_debug(f"工具调用错误: {tool_call['error']}")
        return tool_call["error"]
    
    if not tool_call.get('input'):
        tool_call["input"] = {}
    
    if not all(k in tool_call for k in ("name", "input")):
        log_debug("工具调用缺少必要字段'name'或'input'")
        return "工具调用缺少必要字段'name'或'input'"
    
    try:
        log_debug(f"调用工具: {tool_call['name']}")
        log_debug(f"参数: {json.dumps(tool_call['input'], ensure_ascii=False)}")
        
        # 设置超时时间
        timeout = 30  # 30秒超时
        log_debug(f"设置超时时间: {timeout}秒")
        
        # 创建一个任务
        task = asyncio.create_task(session.call_tool(
            name=tool_call["name"],
            arguments=tool_call["input"]
        ))
        
        # 等待任务完成或超时
        try:
            log_debug("等待工具执行完成...")
            result = await asyncio.wait_for(task, timeout=timeout)
            log_debug(f"工具执行完成: {result}")
            return str(result)
        except asyncio.TimeoutError:
            log_debug(f"工具执行超时 ({timeout}秒)")
            return f"工具执行超时 ({timeout}秒)，请检查SQL语句或稍后重试"
        
    except Exception as e:
        import traceback
        log_debug(f"工具执行失败: {str(e)}")
        log_debug(traceback.format_exc())
        return f"工具执行失败: {str(e)}"

async def format_and_print_results(results_str):
    """格式化并打印查询结果"""
    print("\n查询结果:")
    print("-" * 50)
    
    try:
        # 尝试解析结果字符串为JSON
        results = json.loads(results_str)
        
        if not results:
            print("无结果")
            return
        
        if isinstance(results, list) and len(results) > 0 and isinstance(results[0], dict):
            if "error" in results[0]:
                print(f"错误: {results[0]['error']}")
                return
            
            # 尝试以表格形式打印结果
            try:
                # 获取所有列名
                columns = list(results[0].keys())
                
                # 计算每列的最大宽度
                col_widths = {}
                for col in columns:
                    col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in results))
                
                # 打印表头
                header = " | ".join(f"{col:{col_widths[col]}}" for col in columns)
                print(header)
                print("-" * len(header))
                
                # 打印数据行
                for row in results:
                    row_str = " | ".join(f"{str(row.get(col, '')):{col_widths[col]}}" for col in columns)
                    print(row_str)
                
                print(f"\n共 {len(results)} 条记录")
            except Exception as e:
                # 如果表格格式化失败，直接打印JSON
                print(f"表格格式化失败: {str(e)}")
                print(json.dumps(results, ensure_ascii=False, indent=2))
        else:
            # 如果不是预期的格式，直接打印
            print(json.dumps(results, ensure_ascii=False, indent=2))
    except json.JSONDecodeError:
        # 如果不是JSON格式，直接打印原始字符串
        print(results_str)
    
    print("-" * 50)

async def test_sql_query(session, sql):
    """测试SQL查询工具调用"""
    log_debug(f"测试SQL查询: {sql}")
    
    tool_call = {
        "name": "sql_query",
        "input": {
            "sql": sql
        }
    }
    
    result = await execute_tool_call(tool_call, session)
    log_debug(f"SQL查询结果: {result}")
    return result

async def test_get_table_schema(session, table_name):
    """测试获取表结构工具调用"""
    log_debug(f"测试获取表结构: {table_name}")
    
    tool_call = {
        "name": "get_table_schema",
        "input": {
            "table_name": table_name
        }
    }
    
    result = await execute_tool_call(tool_call, session)
    log_debug(f"表结构结果: {result}")
    return result

async def test_connection(session):
    """测试连接"""
    log_debug("测试连接")
    
    tool_call = {
        "name": "test_connection",
        "input": {}
    }
    
    result = await execute_tool_call(tool_call, session)
    log_debug(f"连接测试结果: {result}")
    return result

async def main():
    """主函数，测试execute_tool_call函数"""
    # 清空日志文件
    with open(DEBUG_LOG_FILE, "w", encoding="utf-8") as f:
        f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 启动测试客户端\n")
    
    print("测试execute_tool_call函数 (调试版)")
    print("=" * 50)
    
    log_debug("开始连接到MCP服务器")
    print(f"[1/4] 连接到MCP服务器...")
    async with stdio_client(server_params) as (read, write):
        log_debug("连接成功，创建ClientSession")
        print(f"[2/4] 创建ClientSession...")
        async with ClientSession(read, write) as session:
            log_debug("初始化会话")
            print(f"[3/4] 初始化会话...")
            await session.initialize()
            log_debug("初始化完成")
            print(f"[4/4] 初始化完成")
            
            # 获取工具列表
            log_debug("获取工具列表")
            list_tool_response = await session.list_tools()
            tools_data = list_tool_response.model_dump()
            tools = json.dumps(tools_data['tools'], ensure_ascii=False, indent=2)
            log_debug(f"工具列表: {tools}")
            print(f"\n可用工具列表:\n{tools}")
            
            # 测试连接
            print("\n\n测试0: 测试连接")
            log_debug("执行连接测试")
            result = await test_connection(session)
            await format_and_print_results(result)
            
            # 交互式测试
            while True:
                print("\n请选择要测试的工具 (输入数字或'exit'退出):")
                print("1. sql_query - 执行SQL查询")
                print("2. get_table_schema - 获取表结构")
                print("3. test_connection - 测试连接")
                choice = input("> ")
                
                if choice.lower() == 'exit':
                    print("测试结束")
                    break
                
                if choice == '1':
                    print("\n请输入SQL查询语句:")
                    sql = input("> ")
                    log_debug(f"用户输入SQL: {sql}")
                    result = await test_sql_query(session, sql)
                    await format_and_print_results(result)
                elif choice == '2':
                    print("\n请输入表名:")
                    table_name = input("> ")
                    log_debug(f"用户输入表名: {table_name}")
                    result = await test_get_table_schema(session, table_name)
                    await format_and_print_results(result)
                elif choice == '3':
                    log_debug("用户选择测试连接")
                    result = await test_connection(session)
                    await format_and_print_results(result)
                else:
                    print("无效的选择，请重试")

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
