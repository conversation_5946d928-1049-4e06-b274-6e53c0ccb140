import json
from dotenv import load_dotenv

import os
import asyncio

from openai import OpenAI
from openai import AsyncOpenAI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

load_dotenv()

# Create server parameters for stdio connection
server_params = StdioServerParameters(
    command="python", # Executable
    args=["mcp/mcp_demo_server.py"], # Optional command line arguments
    env=None # Optional environment variables
)


async def run():
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            # 获取工具列表
            list_tool_response = await session.list_tools()
            tools_data = list_tool_response.model_dump()
            tools = json.dumps(tools_data['tools'], ensure_ascii=False)

            # 构造系统提示
            system_prompt = fr"""你是一个智能助手，可以使用以下工具：
            ```json
            {tools}
            ```
            请按REACT模式执行：
            [思考] 分析问题
            [行动] 调用工具（JSON数组格式，用markdown代码标志包围），工具名称key是name，工具参数key是input
            [观察] 工具结果
            [答案] 最终结论
            请注意：如果需要调用工具时，返回[思考]和[行动]，不要给出[观察]和[答案]，等待用户输入观察结果。当不需要调用工具时,返回[思考]和[答案]，请不要修改输入参数，当调用工具的入参无法返回数据或错误结果，请如实给出结果
            """

            print(system_prompt)

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": "张三、李四、吴昌分数之和多少"}
            ]

            max_iterations = 6
            final_answer = None

            for _ in range(max_iterations):
                # 查询DeepSeek
                api_result = await query_deepseek(messages)
                if not api_result['success']:
                    print(f"API错误: {api_result.get('error')}")
                    break

                response_text = api_result['response']
                print(f"\n模型完整响应: {response_text}")

                # 解析工具调用
                if '[行动]' in response_text:
                    tool_calls = extract_tool_call(response_text)
                    print(f"解析到工具调用: {tool_calls}")
                    for tool_call in tool_calls:
                        # 执行工具
                        tool_result = await execute_tool_call(tool_call, session)
                        print(f"工具执行结果: {tool_result}")

                        # 更新消息历史
                        messages.extend([
                            {"role": "assistant", "content": response_text},
                            {"role": "user", "content": f"[观察] {tool_result}"}
                        ])
                else:
                    final_answer = response_text.split('[答案]')[-1].strip()
                    break

            if final_answer:
                print(f"\n最终答案: {final_answer}")
            else:
                print("\n未找到明确答案")



def extract_tool_call(response: str) -> list:
    """提取工具调用参数，支持多行JSON块"""
    import re
    json_pattern = r"[行动].*```json(.*)```"
    match = re.search(json_pattern, response, re.DOTALL)
    if match:
        try:
            return json.loads(match.group(1))
        except json.JSONDecodeError:
            return {"error": "无效的JSON格式"}
    return {"error": "未找到JSON参数块"}

async def execute_tool_call(tool_call: json, session) -> str:
    if "error" in tool_call:
        return tool_call["error"]
    if (not tool_call.get('input')):tool_call["input"] = {}
    if not all(k in tool_call for k in ("name", "input")):
        return "工具调用缺少必要字段'name'或'input'"
    try:
        result = await session.call_tool(
            name=tool_call["name"],
            arguments=tool_call["input"]
        )
        return str(result)
    except Exception as e:
        return f"工具执行失败: {str(e)}"

client = AsyncOpenAI(
   api_key=os.getenv("DEEPSEEK_API_KEY"),
   base_url=os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1")
)

async def query_deepseek(messages, model="deepseek-chat", temperature=0, max_tokens=1024):
    """异步调用DeepSeek流式API"""

    try:
        stream = await client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=True
        )
        full_content = ""
        print("DeepSeek响应流开始：")
        async for chunk in stream:
            content_chunk = chunk.choices[0].delta.content or ""
            full_content += content_chunk
            print(content_chunk, end="", flush=True)
        print("\n\n流处理已完成")
        return {
            "success": True,
            "response": full_content,
            "is_complete": True
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"API请求错误: {str(e)}",
            "is_complete": True
        }



if __name__ == "__main__":
    import asyncio
    asyncio.run(run())