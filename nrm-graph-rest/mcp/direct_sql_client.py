import asyncio
import json
import os
from dotenv import load_dotenv
import sys
import importlib.util

# 加载环境变量
load_dotenv()

def import_module_from_file(module_name, file_path):
    """从文件路径导入模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

# 导入mcp_idc_sql_server.py模块
server_module_path = os.path.join(os.path.dirname(__file__), "mcp_idc_sql_server.py")
server_module = import_module_from_file("mcp_idc_sql_server", server_module_path)

# 从导入的模块中获取sql_query函数
sql_query = server_module.sql_query

async def format_and_print_results(results):
    """格式化并打印查询结果"""
    print("\n查询结果:")
    print("-" * 50)
    
    if not results:
        print("无结果")
        return
    
    if isinstance(results, list) and len(results) > 0 and isinstance(results[0], dict):
        if "error" in results[0]:
            print(f"错误: {results[0]['error']}")
            return
        
        # 尝试以表格形式打印结果
        try:
            # 获取所有列名
            columns = list(results[0].keys())
            
            # 计算每列的最大宽度
            col_widths = {}
            for col in columns:
                col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in results))
            
            # 打印表头
            header = " | ".join(f"{col:{col_widths[col]}}" for col in columns)
            print(header)
            print("-" * len(header))
            
            # 打印数据行
            for row in results:
                row_str = " | ".join(f"{str(row.get(col, '')):{col_widths[col]}}" for col in columns)
                print(row_str)
            
            print(f"\n共 {len(results)} 条记录")
        except Exception as e:
            # 如果表格格式化失败，直接打印JSON
            print(f"表格格式化失败: {str(e)}")
            print(json.dumps(results, ensure_ascii=False, indent=2))
    else:
        # 如果不是预期的格式，直接打印
        print(results)
    
    print("-" * 50)

async def main():
    """主函数，直接调用mcp_idc_sql_server.py中的sql_query函数"""
    print("SQL查询客户端 - 直接调用mcp_idc_sql_server.py")
    print(f"API URL: {server_module.API_URL}")
    print("=" * 50)
    
    while True:
        # 获取用户输入的SQL查询
        print("\n请输入SQL查询语句 (输入'exit'退出):")
        sql_input = input("> ")
        
        # 检查是否退出
        if sql_input.lower() == 'exit':
            print("程序已退出")
            break
        
        # 执行查询
        try:
            print(f"执行SQL查询: {sql_input}")
            results = await sql_query(sql_input)
            await format_and_print_results(results)
        except Exception as e:
            import traceback
            print(f"执行查询时发生错误: {str(e)}")
            traceback.print_exc()

async def run_query(sql):
    """运行单个查询并返回结果"""
    try:
        return await sql_query(sql)
    except Exception as e:
        return [{"error": f"执行查询时发生错误: {str(e)}"}]

async def get_table_schema(table_name):
    """获取表结构信息"""
    schema_sql = f"""
    SELECT 
        column_name as "列名", 
        data_type as "数据类型", 
        is_nullable as "是否可空",
        column_default as "默认值"
    FROM 
        information_schema.columns 
    WHERE 
        table_name = '{table_name}'
    ORDER BY 
        ordinal_position;
    """
    return await run_query(schema_sql)

async def get_abnormal_traffic(limit=10):
    """获取异常流量数据"""
    query = f"""
    SELECT 
        dev_code AS 设备编码,
        port_name AS 端口名称,
        MIN(shijian) AS 流量最早时间,
        MAX(shijian) AS 流量最晚时间,
        (MAX(shijian::timestamp) - MIN(shijian::timestamp)) AS 持续时间,
        ROUND(MAX((in_val + out_val)/1000)::numeric,2)||'kbps' AS 最大流速,
        ROUND(MIN((in_val + out_val)/1000)::numeric,2)||'kbps' AS 最小流速
    FROM port_abnormal_traffic 
    WHERE abnormal_type = '有资源异常'
    GROUP BY dev_code, port_name
    HAVING MAX(in_val + out_val) > 0
    ORDER BY (MAX(in_val + out_val)) DESC
    LIMIT {limit}
    """
    return await run_query(query)

async def interactive_mode():
    """交互式模式"""
    await main()

async def demo_mode():
    """演示模式，自动执行一些预定义的查询"""
    print("SQL查询客户端 - 演示模式")
    print(f"API URL: {server_module.API_URL}")
    print("=" * 50)
    
    # 演示1: 获取port_abnormal_traffic表结构
    print("\n演示1: 获取port_abnormal_traffic表结构")
    results = await get_table_schema('port_abnormal_traffic')
    await format_and_print_results(results)
    
    # 演示2: 获取异常流量数据
    print("\n演示2: 获取异常流量数据")
    results = await get_abnormal_traffic(5)
    await format_and_print_results(results)
    
    print("\n演示完成")

if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--demo':
        # 演示模式
        asyncio.run(demo_mode())
    else:
        # 交互式模式
        asyncio.run(interactive_mode())
