from mcp.server.fastmcp import FastMCP
from typing import List, Dict

# 创建MCP服务器
mcp = FastMCP("student_score")

# 学生数据
students = {
    "张三": 100,
    "李四": 90, 
    "王五": 80,
    "周五": 75
}

# 工具1: 查询所有学生姓名
@mcp.tool()
def get_all_students() -> List[str]:
    """获取所有学生姓名列表"""
    return list(students.keys())

# 工具2: 根据姓名查询分数
@mcp.tool()
def get_score_by_name(name: str) -> Dict[str, int]:
    """根据学生姓名查询分数"""
    if name in students:
        return {"name": name, "score": students[name]}
    else:
        return {"error": "学生不存在"}

if __name__ == "__main__":
    # 启动服务器
    mcp.run(transport='stdio')
    # mcp.run(transport='sse', server_port=9001)
    pass