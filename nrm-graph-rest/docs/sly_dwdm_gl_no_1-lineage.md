graph TD
    %% 源表定义
    SLY_DEVICE_GL_INFO[sly_device_gl_info<br/>设备光路信息表]
    CM_LINK[${o3_res_schema}.cm_link<br/>链路表]
    CR_LINK_LINK[${o3_res_schema}.cr_link_link<br/>链路关系表]
    CV_LINK[${o3_res_schema}.cv_link<br/>链路属性表]

    %% 中间表
    SLY_DWDM_GL_NO_1[sly_dwdm_gl_no_1<br/>波分光路表1]

    %% 目标依赖表
    SLY_DWDM_GL_NO[sly_dwdm_gl_no<br/>波分光路表]
    SLY_DWDM_GL_NO_ROOM[sly_dwdm_gl_no_room<br/>机房信息表]
    SLY_DWDM_PROTECT[sly_dwdm_protect<br/>保护信息表]

    %% 关联条件说明
    subgraph "关联条件"
        LINK_COND1[gl.spec_id = 1131200002<br/>以太网链路]
        LINK_COND2[t2.upper_link_spec_id = 1132100021<br/>以太网链路]
        LINK_COND3[t4.lower_link_spec_id = 1132300020<br/>client链路]
        LINK_COND4[pkey='P_OPT_ROUTE_MODEL'<br/>and value='100172']
    end

    %% 数据流向
    SLY_DEVICE_GL_INFO --> |主表|SLY_DWDM_GL_NO_1
    CM_LINK --> |gl.code = t1.gl_code|SLY_DWDM_GL_NO_1
    CR_LINK_LINK --> |多层链路关联|SLY_DWDM_GL_NO_1
    CV_LINK --> |链路属性过滤|SLY_DWDM_GL_NO_1

    SLY_DWDM_GL_NO_1 --> |进一步处理|SLY_DWDM_GL_NO
    SLY_DWDM_GL_NO --> |分析机房信息|SLY_DWDM_GL_NO_ROOM
    SLY_DWDM_GL_NO_ROOM --> |分析保护信息|SLY_DWDM_PROTECT

    %% 处理逻辑说明
    subgraph "处理说明"
        PROCESS1[1. 关联以太网链路]
        PROCESS2[2. 关联client链路]
        PROCESS3[3. 获取波分信息]
        PROCESS4[4. 创建索引优化查询]
    end

    %% 样式定义
    classDef source fill:#e1f3d8,stroke:#82c91e
    classDef intermediate fill:#fff3bf,stroke:#fab005
    classDef target fill:#d3f9d8,stroke:#40c057
    classDef condition fill:#f8f9fa,stroke:#adb5bd
    classDef process fill:#e7f5ff,stroke:#339af0

    %% 应用样式
    class SLY_DEVICE_GL_INFO,CM_LINK,CR_LINK_LINK,CV_LINK source
    class SLY_DWDM_GL_NO_1 intermediate
    class SLY_DWDM_GL_NO,SLY_DWDM_GL_NO_ROOM,SLY_DWDM_PROTECT target
    class LINK_COND1,LINK_COND2,LINK_COND3,LINK_COND4 condition
    class PROCESS1,PROCESS2,PROCESS3,PROCESS4 process