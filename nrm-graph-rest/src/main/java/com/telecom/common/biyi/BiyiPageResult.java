package com.telecom.common.biyi;

import java.io.Serializable;
import java.util.List;

public class BiyiPageResult <T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<T> data;
    private long recordsTotal;
    private long recordsFiltered;

    public BiyiPageResult() {
    }

    public BiyiPageResult(List<T> data, long recordsTotal, long recordsFiltered) {
        this.data = data;
        this.recordsFiltered = recordsFiltered;
        this.recordsTotal = recordsTotal;
    }

    public List<T> getData() {
        return this.data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public long getRecordsTotal() {
        return this.recordsTotal;
    }

    public void setRecordsTotal(long recordsTotal) {
        this.recordsTotal = recordsTotal;
    }

    public long getRecordsFiltered() {
        return this.recordsFiltered;
    }

    public void setRecordsFiltered(long recordsFiltered) {
        this.recordsFiltered = recordsFiltered;
    }
}
