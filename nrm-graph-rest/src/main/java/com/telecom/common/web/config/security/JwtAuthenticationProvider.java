package com.telecom.common.web.config.security;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;


@Service(value = "jwtAuthenticationProvider")
public class JwtAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    private static final Logger LOG = LoggerFactory.getLogger(JwtAuthenticationProvider.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public boolean supports(Class<?> authentication) {
        return (JwtAuthenticationToken.class.isAssignableFrom(authentication));
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
    }

    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication)
            throws AuthenticationException {
        JwtAuthenticationToken jwtAuthenticationToken = (JwtAuthenticationToken) authentication;
        String token = jwtAuthenticationToken.getToken();
        try {
            JwtUser parsedUser = jwtUtil.verifyJWT(token);


            if (parsedUser == null) {
                throw new JwtTokenMalformedException("JWT token is not valid");
            }

            LOG.debug("login:" + parsedUser.getUsername() + "," + parsedUser.getAuthorities());


            List<GrantedAuthority> authorityList = AuthorityUtils.commaSeparatedStringToAuthorityList(parsedUser.getAuthorities());

            for (GrantedAuthority auth : authorityList) {
                LOG.debug("拥有权限" + auth.getAuthority());
            }

            SecurityContext.setJwtUser(parsedUser);

            return new AuthenticatedUser(parsedUser.getId(), parsedUser.getUsername(), token, authorityList);
        } catch (AuthenticationException ex) {
            LOG.error(ex.getMessage(),ex);
            throw ex;

        }
    }

}
