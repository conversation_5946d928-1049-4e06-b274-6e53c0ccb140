package com.telecom.common.web.config.security;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;



public class JwtAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
	
	private static final Logger LOG = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
	
	
	@Autowired(required=true)
	JwtUtil jwtUtil;
	
    public JwtAuthenticationFilter() {
        super("/**");
    }

    @Override
    protected boolean requiresAuthentication(HttpServletRequest request, HttpServletResponse response) {
        return true;
    }

	@Override
	public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
			throws AuthenticationException, IOException, ServletException {
		
		LOG.debug(request.getRequestURI().toString());
		
		String header = request.getHeader("Authorization");
		
		String authToken = "";

        if (header == null || !header.startsWith("Bearer ")) {
        	authToken=jwtUtil.createJWT(createUnkownUser());
			// return null;
        }else{

        	authToken = header.substring(7);
        }
        
        JwtAuthenticationToken authRequest = new JwtAuthenticationToken(authToken,new JwtUser());

        return getAuthenticationManager().authenticate(authRequest);
	}
	
	@Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain, Authentication authResult)
            throws IOException, ServletException {
        super.successfulAuthentication(request, response, chain, authResult);

        // As this authentication is in HTTP header, after success we need to continue the request normally
        // and return the response as if the resource was not secured at all
        chain.doFilter(request, response);
    }
	
	JwtUser createUnkownUser(){
		JwtUser user = new JwtUser();
		user.setId("UNKOWN");
		user.setUsername("UNKOWN");
		//user.setRole("USER");
		return user;
	}

}