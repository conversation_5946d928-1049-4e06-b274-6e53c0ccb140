package com.telecom.common.web.config.security;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

public class JwtAuthenticationToken extends UsernamePasswordAuthenticationToken{
	/**
	 *
	 */
	private static final long serialVersionUID = 4531917991603525250L;
	private String token;
	private JwtUser user;

    public JwtAuthenticationToken(String token,JwtUser user) {
        super(null, null);
        this.token = token;
        this.user = user;

    }

    public String getToken() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {

        return user;
    }

}
