package com.telecom.common.util;

import org.apache.commons.lang3.StringUtils;
//import sun.misc.BASE64Encoder;
import java.util.Base64;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

public class ImageUtil {
    public static String getImageBase64String(File imageFile) {
        if (imageFile == null || !imageFile.exists()) {
            return "";
        }
        InputStream is = null;
        byte[] data = null;
        try {
            is = new FileInputStream(imageFile);
            data = new byte[is.available()];
            is.read(data);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // BASE64Encoder encoder = new BASE64Encoder();
        // return encoder.encode(data);
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(data);
    }
}
