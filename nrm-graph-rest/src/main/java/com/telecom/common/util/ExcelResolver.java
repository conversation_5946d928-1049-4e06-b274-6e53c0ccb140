package com.telecom.common.util;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.db.handler.StringHandler;
import com.alibaba.fastjson.JSON;

import net.sf.json.JSONArray;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

public class ExcelResolver {
    public static final Logger log = LoggerFactory.getLogger(ExcelResolver.class);

    /**
     * 判断文件是否符合exce的格式
     *
     * @param multipartFile 文件
     * @return
     */
    public static List<Map> excelResolver(MultipartFile multipartFile) {
        Workbook workbook = null;
        try {
            InputStream in = multipartFile.getInputStream();
            String fileName = multipartFile.getOriginalFilename();
            //根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith("xls")) {
                //2003
                workbook = new HSSFWorkbook(in);
            } else if (fileName.endsWith("xlsx")) {
                //2007 及2007以上
                workbook = new XSSFWorkbook(in);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return contentResolver(workbook);
    }


    /**
     * 解析文件内容
     *
     * @param wb 文件对象
     * @return
     */
    private static List<Map> contentResolver(Workbook wb) {
        if (wb == null || wb.getNumberOfSheets() < 1) {
            return new ArrayList();
        }
        int sheetNum = wb.getNumberOfSheets();
        List<Map> list = new ArrayList<>();
        for (int i = 0; i < sheetNum; i++) {
            Map<String, Object> sheetMap = new HashMap<>();
            Sheet sheet = wb.getSheetAt(i);
            String sheetName = sheet.getSheetName();
            //解析title名称
            Row hssfRow = sheet.getRow(0);
            Map<String, String> titleMap = dataResolver(hssfRow);

            List<Map<String, String>> dataMap = dataResolver(sheet, titleMap);

            // sheetMap.put("sheetName",sheetName);
            //sheetMap.put("contentTile",titleMap);
            sheetMap.put("data", dataMap);
            list.add(sheetMap);
        }
        return list;
    }

    /**
     * 解析行数据
     *
     * @param hssfRow 行
     * @return
     */
    private static Map<String, String> dataResolver(Row hssfRow) {
        int firstCellNum = hssfRow.getFirstCellNum();
        int lasCellNum = hssfRow.getLastCellNum();
        Map<String, String> map = new HashMap<>();

        for (int i = firstCellNum; i < lasCellNum; i++) {
            Cell cell = hssfRow.getCell(i);
            map.put("line_" + i, cell.getStringCellValue().trim());
        }
        return map;
    }

    private static Map<String, String> dataResolver(Row hssfRow, Map titlemap) {
        int firstCellNum = hssfRow.getFirstCellNum();
        int lasCellNum = hssfRow.getLastCellNum();
        Map<String, String> map = new HashMap<>();
        for (int i = firstCellNum; i < lasCellNum; i++) {
            Cell cell = hssfRow.getCell(i);
            String s = "line_" + i;
            String cellValue = ObjectUtil.isNotEmpty(cell)?cell.getStringCellValue():"";
            map.put(Optional.ofNullable(titlemap.get(s)).orElse("").toString(), cellValue.trim());
        }
        return map;
    }

    /**
     * 解析每一个sheet页数据
     *
     * @param sheet sheet对象
     * @return
     */

    private static List<Map<String, String>> dataResolver(Sheet sheet, Map map) {
        List<Map<String, String>> dataList = new ArrayList<>();
        //获得当前sheet的开始行
        int firstRowNum = sheet.getFirstRowNum();
        //获得当前sheet的结束行
        int lastRowNum = sheet.getLastRowNum();
        for (int row = firstRowNum + 1; row <= lastRowNum; row++) {
            Row hssfRow = sheet.getRow(row);
            Map<String, String> rowMap = dataResolver(hssfRow, map);
            dataList.add(rowMap);
        }
        return dataList;
    }



}
