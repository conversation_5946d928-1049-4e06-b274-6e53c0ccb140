package com.telecom.common.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil {
	private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

	// 现在
	// public static Timestamp now = new Timestamp(System.currentTimeMillis());

	// 今年年份
	public static String thisYear() {
		return String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
	}

	// 今年一月
	public static String thisYearJanuary() {
		return String.valueOf(Calendar.getInstance().get(Calendar.YEAR)) + "-01";
	}

	// 今年十二月
	public static String thisYearDecember() {
		return String.valueOf(Calendar.getInstance().get(Calendar.YEAR)) + "-12";
	}

	// 当前月的第一天
	public static String thisMonthFirstDay() {
		return format.format(getThisMonthFirstDay());
	}

	// 当前月的最后一天
	public static String thisMonthLastDay() {
		return format.format(getThisMonthLastDay());
	}

	// 昨天
	public static String yesterday() {
		return format.format(getAppointedDay(-1));
	}

	// 一周前
	public static String oneWeekAgoDay() {
		return format.format(getAppointedDay(-7));
	}

	// 今天零点零分零秒的毫秒数
	public static Timestamp todayZeroTime() {
		return new Timestamp(getTodayZeroTime());
	}

	// 今天23点59分59秒的毫秒数
	public static Timestamp todayLastTime() {
		return new Timestamp(getTodayLastTime());
	}

	public static Timestamp now() {
		return new Timestamp(System.currentTimeMillis());
	}

	// 获取当前月的第一天
	public static Date getThisMonthFirstDay() {
		Calendar today = Calendar.getInstance();
		today.add(Calendar.MONTH, 0);
		today.set(Calendar.DAY_OF_MONTH, 1);
		return today.getTime();
	}

	// 获取当前月的最后一天
	public static Date getThisMonthLastDay() {
		Calendar today = Calendar.getInstance();
		today.add(Calendar.MONTH, 1);
		today.set(Calendar.DAY_OF_MONTH, 0);
		return today.getTime();
	}

	public static Date getAppointedDay(int num) {
		Calendar today = Calendar.getInstance();
		today.add(Calendar.DATE, num);
		return today.getTime();
	}

	// 今天零点零分零秒的毫秒数
	public static long getTodayZeroTime() {
		long current = System.currentTimeMillis();// 当前时间毫秒数
		return current / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();
	}

	// 今天23点59分59秒的毫秒数
	public static long getTodayLastTime() {
		return getTodayZeroTime() + 24 * 60 * 60 * 1000 - 1;
	}

}
