package com.telecom.common.s3;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class S3Config {
    @Bean
    @ConfigurationProperties(prefix = "s3")
    public S3Properties s3Properties() {
        return new S3Properties();
    }
    @Bean
    public AmazonS3 s3Connection() {
        S3Properties s3Properties = s3Properties();
        AWSCredentials credentials = new BasicAWSCredentials(s3Properties.accessKey, s3Properties.secretKey);
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTP);
        AmazonS3 conn = new AmazonS3Client(credentials, clientConfig);
        conn.setEndpoint(s3Properties.endpoint);
        return conn;
    }
}
