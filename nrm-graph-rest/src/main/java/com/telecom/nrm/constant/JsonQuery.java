package com.telecom.nrm.constant;

import com.alibaba.fastjson.JSONObject;

public class JsonQuery {

    public static JSONObject fourLogic;

    public static JSONObject pon;

    public static JSONObject ipran;

    public static JSONObject newCity;

    public static JSONObject ipm;

    public static JSONObject alarm;

    // 构造四个接口的入参json
    static {

        String fourLogic = "{\n" +
                "\"related_area_code\":\"'YZ'\",\n" +
                "\"spec_code\":\"'SJ','WX','DH'\",\n" +
                "    \"internal\":\"'24 hour'\",\n" +
                "    \"userLabel\":\"高邮\",\n" +
                "    \"summary1\":\"网元断链告警(198099803)\",\n" +
                "\"summary2\":\"温度\",\n" +
                "\"summary3\":\"该地址不能ping通\",\n" +
                "\"summary4\":\"BTS掉站\"\n" +
                "}";
        JsonQuery.fourLogic = JSONObject.parseObject(fourLogic);


        String pon = "{\n" +
                "    \"start\":\"2024/06/20 14:32:02\", \n" +
                "    \"end\":\"2024/06/20 15:02:02\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"pon_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JsonQuery.pon = JSONObject.parseObject(pon);


        String ipran = "{\n" +
                "    \"start\":\"2024/06/20 14:33:07\", \n" +
                "    \"end\":\"2024/06/20 15:03:07\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"ipran_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JsonQuery.ipran = JSONObject.parseObject(ipran);


        String newCity = "{\n" +
                "    \"start\":\"2024/06/20 14:33:49\", \n" +
                "    \"end\":\"2024/06/20 15:03:49\",   \n" +
                "    \"queries\":[{   \n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"ipf_p_crc_inputerror\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"*************\"\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JsonQuery.newCity = JSONObject.parseObject(newCity);

        String npmStr = "{\n" +
                "    \"start\":\"2024/07/02 11:02:50\", \n" +
                "    \"end\":\"2024/07/02 13:32:50\",  \n" +
                "    \"queries\":[{\n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"P_IfFluxIn\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"***************\"\n" +
                "\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JsonQuery.ipm = JSONObject.parseObject(npmStr);


        String ipranStr = "{\n" +
                "    \"start\":\"2024/07/02 11:02:50\", \n" +
                "    \"end\":\"2024/07/02 13:32:50\",  \n" +
                "    \"queries\":[{\n" +
                "        \"aggregator\":\"none\",    \n" +
                "        \"metric\":\"ipran_p_flux_fluxin\",\n" +
                "        \"tags\":{\n" +
                "         \"MgmtIp\":\"***************\"\n" +
                "\n" +
                "        }\n" +
                "    }]\n" +
                "}";
        JsonQuery.ipran = JSONObject.parseObject(ipranStr);


        String alarm = "{\n" +
                "    \"additional_fields\":\",contexte,close_time,nm_code,eqp_name,user_label\",\n" +
                "    \"field_name\":\"summary\",\n" +
                "    \"access_line\":\"南京盐城ETN6056\",\n" +
                "    \"nm_code\":\"JS_YBZX_CS\"\n" +
                "}";

        JsonQuery.alarm = JSONObject.parseObject(alarm);


    }


}
