package com.telecom.nrm.constant;

/**
 * <AUTHOR>
 */
public interface Constant {


    class GraphApiList {
        /**
         * PON接入网络topo查询
         */
        public static final String QUERY_PON_TOPO = "query_pon_topo";
    }

    class ResType{

        /**
         * 光纤光路
         */
        public static final Long FIBER_OPTICAL= 1132400006L;

        /**
         * MS-OTN单波段
         */
        public static final Long MS_OTN_SINGLE_BAND = 1132192006L;

        /**
         * DWDM光通道
         */
        public static final Long DWDM_OPTICAL_CHANNEL = 1132200002L;

        /**
         * SDH复用段
         */
        public static final Long SDH_MULTI_SEG = 1132100008L;

        /**
         * DWDM复用段/光复用段
         */
        public static final Long DWDM_MULTI_SEG = 1132100010L;

        /**
         * 光链路路由
         */
        public static final Long OPTICAL_LINK_ROUTE = 1131200002L;

        /**
         * 局向光纤
         */
        public static final Long OFFICE_FIBER  = 1131100003L;


        /**
         * 软跳
         */
        public static final Long SOFT_PATCHEING  = 1132100001L;

        /**
         * 硬跳
         */
        public static final Long HARD_PATCHEING  = 1131100001L;
    }
}
