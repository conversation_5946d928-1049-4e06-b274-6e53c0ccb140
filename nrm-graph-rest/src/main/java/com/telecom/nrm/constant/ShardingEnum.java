package com.telecom.nrm.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

public enum ShardingEnum {

/*    put("321122930000000000000006", "ds_bc_o3_nj");
    put("321122930000000000000008", "ds_bc_o3_sz");
    put("321122930000000000000024", "ds_bc_o3_nt");
    put("321122930000000000000021", "ds_bc_o3_yz");
    put("321122930000000000000063", "ds_bc_o3_lyg");
    put("321122930000000000000069", "ds_bc_o3_cz");
    put("321122930000000000000013", "ds_bc_o3_wx");
    put("321122930000000000000073", "ds_bc_o3_sq");
    put("321122930000000000000037", "ds_bc_o3_zj");
    put("321122930000000000000017", "ds_bc_o3_yc");
    put("321122930000000000000079", "ds_bc_o3_tz");
    put("321122930000000000000056", "ds_bc_o3_ha");
    put("321122930000000000000046", "ds_bc_o3_xz");*/


    /** 南京 */
    NJ("321122930000000000000006", "25", "ds_res_nj", "ds_bc_nj", "ds_bc_o3_nj","8320100","南京"),

    /** 镇江 */
    ZJ("321122930000000000000037", "11", "ds_res_zj", "ds_bc_zj", "ds_bc_o3_zj","8321100","镇江"),

    /** 常州 */
    CZ("321122930000000000000069", "19", "ds_res_cz", "ds_bc_cz","ds_bc_o3_cz", "8320400" ,"常州"),

    /** 无锡 */
    WX("321122930000000000000013", "10", "ds_res_wx", "ds_bc_wx", "ds_bc_o3_wx","8320200","无锡"),

    /** 苏州 */
    SZ("321122930000000000000008", "12", "ds_res_sz", "ds_bc_sz", "ds_bc_o3_sz","8320500", "苏州"),

    /** 南通 */
    NT("321122930000000000000024", "13", "ds_res_nt", "ds_bc_nt", "ds_bc_o3_nt","8320600", "南通"),

    /** 泰州 */
    TZ("321122930000000000000079", "23", "ds_res_tz", "ds_bc_tz","ds_bc_o3_tz", "8321200", "泰州"),

    /** 扬州 */
    YZ("321122930000000000000021", "14", "ds_res_yz", "ds_bc_yz", "ds_bc_o3_yz","8321000", "扬州"),

    /** 徐州 */
    XZ("321122930000000000000046", "16", "ds_res_xz", "ds_bc_xz", "ds_bc_o3_xz","8320300", "徐州"),

    /** 淮安 */
    HA("321122930000000000000056", "17", "ds_res_ha", "ds_bc_ha", "ds_bc_o3_ha","8320800", "淮安"),

    /** 宿迁 */
    SQ("321122930000000000000073", "27", "ds_res_sq", "ds_bc_sq", "ds_bc_o3_sq","8321300", "宿迁"),

    /** 连云港 */
    LYG("321122930000000000000063", "18", "ds_res_lyg", "ds_bc_lyg", "ds_bc_o3_lyg","8320700", "连云港"),

    /** 盐城 */
    YC("321122930000000000000017", "15", "ds_res_yc", "ds_bc_yc", "ds_bc_o3_yc","8320900" , "盐城"),

    SW("-1", "-1", "-1", "-1", "-1","-1" , "省外");


    private String o3;

    private String o2;

    private String o3ShardingCode;

    private String o2ShardingCode;

    //新一代BC库的shardingCode
    private String bcShardingCode;

    private String ppmCode;

    private String regionName;


    ShardingEnum(String o3, String o2, String o3ShardingCode, String o2ShardingCode ,String bcShardingCode, String ppmCode,String regionName) {
        this.o3 = o3;
        this.o2 = o2;
        this.o3ShardingCode = o3ShardingCode;
        this.o2ShardingCode = o2ShardingCode;
        this.ppmCode = ppmCode;
        this.regionName = regionName;
        this.bcShardingCode = bcShardingCode;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getRegionName() {
        return regionName;
    }

    public String getO3() {
        return o3;
    }

    public void setO3(String o3) {
        this.o3 = o3;
    }

    public String getO2() {
        return o2;
    }

    public void setO2(String o2) {
        this.o2 = o2;
    }


    public String getO3ShardingCode() {
        return o3ShardingCode;
    }

    public void setO3ShardingCode(String o3ShardingCode) {
        this.o3ShardingCode = o3ShardingCode;
    }

    public String getO2ShardingCode() {
        return o2ShardingCode;
    }

    public void setO2ShardingCode(String o2ShardingCode) {
        this.o2ShardingCode = o2ShardingCode;
    }

    public String getPpmCode() {
        return ppmCode;
    }

    public String getBcShardingCode() {
        return bcShardingCode;
    }

    public void setBcShardingCode(String bcShardingCode) {
        this.bcShardingCode = bcShardingCode;
    }

    public void setPpmCode(String ppmCode) {
        this.ppmCode = ppmCode;
    }

    public static String getShardCodeByO3(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO3().equals(id)) {
                return shardingEnum.getO3ShardingCode();
            }
        }
        return null;
    }

    public static String getShardCodeByO2(String id, boolean isOnline) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO2().equals(id)) {
                if (isOnline) {
                    return shardingEnum.getO3ShardingCode();
                }
                return shardingEnum.getO2ShardingCode();
            }
        }
        return null;
    }

    public static String getO2ByO3(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO3().equals(id)) {
                return shardingEnum.getO2();
            }
        }
        return null;
    }

    public static String getO3ByO2(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO2().equals(id)) {
                return shardingEnum.getO3();
            }
        }
        return null;
    }

    public static String getO3ByPpmCode(String ppmCode) {
        if (StringUtils.isBlank(ppmCode)) {
            return null;
        }

        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getPpmCode().equals(ppmCode)) {
                return shardingEnum.getO3();
            }
        }
        return null;
    }

    public static String getRegionNameByO3(String id) {
        String result = null;
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO3().equals(id)) {
                result = shardingEnum.getRegionName();
                break;
            }
        }
        return result;
    }
    public static ShardingEnum getShardingEnumByO3(String id) {
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getO3().equals(id)) {
                return shardingEnum;
            }
        }
        return null;
    }
    public static ShardingEnum getShardingEnumByBc(String code) {
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getBcShardingCode().equals(code)) {
                return shardingEnum;
            }
        }
        return null;
    }
    public static ShardingEnum getShardingEnumByPmCode(String code) {
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getPpmCode().equals(code)) {
                return shardingEnum;
            }
        }
        return null;
    }
    public static ShardingEnum analysisShardingEnumByText(String text) {
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (text.indexOf(shardingEnum.getRegionName()) >= 0) {
                return shardingEnum;
            }
        }
        return null;
    }
    public static ShardingEnum getShardingEnumByRegionName(String regionName) {
        for (ShardingEnum shardingEnum : ShardingEnum.values()) {
            if (shardingEnum.getRegionName().equals(regionName)) {
                return shardingEnum;
            }
        }
        return null;
    }

    /**
     * 地市缩写获取枚举  如 nj  获取NJ
     * @param cityAbb
     * @return
     */
    public static ShardingEnum getShardingEnumByCityAbb(String cityAbb ){
        for (ShardingEnum value : ShardingEnum.values()) {
            if (value.toString().toLowerCase(Locale.ROOT).equals(cityAbb)){
                return value;
            }
        }
        return null;

    }


}
