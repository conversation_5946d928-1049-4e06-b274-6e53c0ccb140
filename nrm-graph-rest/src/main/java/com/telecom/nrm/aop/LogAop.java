package com.telecom.nrm.aop;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.nrm.service.ElasticsearchLogService;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

@Aspect
@Component
@Slf4j
public class LogAop {

    @Autowired
    ElasticsearchLogService elasticsearchLogService;


    @Pointcut(value = "@annotation(com.telecom.nrm.aop.LogAnnotation)")
    public void queryLog() {
    }


    @Around("queryLog()")
    public Object queryLog(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        //获取切入点所在的方法
        Method method = signature.getMethod();
        Object[] args = proceedingJoinPoint.getArgs();
        String argsJson = null;
        if (args != null && args.length > 0) {
            // 过滤掉HttpServletRequest等不能序列化的对象
            Object[] filteredArgs = filterSerializableArgs(args);
            if (filteredArgs.length > 0) {
                try {
                    argsJson = JSONObject.toJSONString(filteredArgs);
                } catch (Exception e) {
                    argsJson = "参数序列化失败: " + e.getMessage();
                }
            } else {
                argsJson = "无可序列化参数";
            }
        }
        // 获取请求URL
        if (ObjectUtil.isEmpty(RequestContextHolder.getRequestAttributes())) {
            return proceedingJoinPoint.proceed();
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String requestUrl = request.getRequestURL().toString();
        Map<String, String> headers = getHeaders(request);
        LogAnnotation annotation = method.getAnnotation(LogAnnotation.class);
        String interfaceName = annotation.interfaceName();

        String userName = SecurityContext.getJwtUser().getUsername();
        // 执行原方法
        Object result = null;


        long duration = 0L;
        try {
            long beginTimestamp = System.currentTimeMillis();
            result = proceedingJoinPoint.proceed();
            long endTimestamp = System.currentTimeMillis();
            duration = endTimestamp - beginTimestamp;
        } finally {
            JSONObject logParam = new JSONObject();
            logParam.put("intf_code", interfaceName);
            logParam.put("request_body", argsJson);
            String responseBody = result == null ? null : JSONObject.toJSONString(result);
            logParam.put("response_body", responseBody == null ? "" : responseBody
                    .substring(0, Math.min(responseBody.length(), 5500)));
            logParam.put("request_header", headers.isEmpty() ? "" : headers.toString());
            if (ObjectUtil.isNotEmpty(userName)) {
                logParam.put("user_name", userName);
            }
            if (ObjectUtil.isNotEmpty(duration)) {
                logParam.put("duration", duration);
            }
            try {
                elasticsearchLogService.saveInterfaceLog(logParam);
            } catch (Exception e) {
                log.error("调用接口日志保存失败", e);
            }

        }

        return result;
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headers = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        return headers;
    }

    /**
     * 过滤掉不能序列化的参数，如HttpServletRequest、HttpServletResponse等
     */
    private Object[] filterSerializableArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return new Object[0];
        }

        List<Object> filteredArgs = new ArrayList<>();
        for (Object arg : args) {
            if (arg == null) {
                filteredArgs.add(null);
            } else if (isSerializable(arg)) {
                filteredArgs.add(arg);
            } else {
                // 对于不能序列化的对象，记录其类型
                filteredArgs.add("不可序列化对象: " + arg.getClass().getSimpleName());
            }
        }

        return filteredArgs.toArray();
    }

    /**
     * 判断对象是否可以安全序列化
     */
    private boolean isSerializable(Object obj) {
        if (obj == null) {
            return true;
        }

        // 排除Servlet相关对象
        if (obj instanceof javax.servlet.ServletRequest ||
            obj instanceof javax.servlet.ServletResponse ||
            obj instanceof javax.servlet.http.HttpSession) {
            return false;
        }

        // 排除Spring相关对象
        if (obj.getClass().getName().startsWith("org.springframework.web.") ||
            obj.getClass().getName().startsWith("org.springframework.security.")) {
            return false;
        }

        // 基本类型和常用对象类型
        if (obj instanceof String ||
            obj instanceof Number ||
            obj instanceof Boolean ||
            obj instanceof java.util.Date ||
            obj instanceof java.time.LocalDateTime ||
            obj instanceof java.time.LocalDate ||
            obj instanceof com.alibaba.fastjson.JSONObject ||
            obj instanceof com.alibaba.fastjson.JSONArray) {
            return true;
        }

        // 集合类型需要检查元素
        if (obj instanceof java.util.Collection ||
            obj instanceof java.util.Map ||
            obj.getClass().isArray()) {
            return true; // 简化处理，允许集合类型
        }

        // 其他自定义对象，尝试序列化
        return true;
    }

}
