package com.telecom.nrm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@EnableAsync
@Configuration
public class ThreadPoolConfig {

    @Bean
    public ExecutorService graphRestPool() {
        return new ThreadPoolExecutor(16, 32, 3, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
                new CustomizableThreadFactory("graph-rest-thread"), new ThreadPoolExecutor.AbortPolicy());
    }
}
