package com.telecom.nrm.config;

import com.telecom.nrm.agent.PortChatController;
import com.telecom.nrm.controller.IdcChatController;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig  implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new IdcChatController(), "/ws-idc").setAllowedOrigins("*");
        registry.addHandler(new PortChatController(), "/ws-idc-port").setAllowedOrigins("*");
    }

}
