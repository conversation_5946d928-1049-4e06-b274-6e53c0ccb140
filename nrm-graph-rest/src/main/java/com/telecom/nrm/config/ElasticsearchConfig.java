package com.telecom.nrm.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.springframework.context.ApplicationListener;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.core.env.Environment;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.List;
import java.util.Map;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class ElasticsearchConfig implements ApplicationListener<ApplicationReadyEvent> {

    private final ElasticsearchProperties elasticsearchProperties;

    @Bean(destroyMethod = "close")
    public RestHighLevelClient restHighLevelClient() {
        HttpHost[] httpHosts = elasticsearchProperties.getHosts().stream()
                .map(h -> new HttpHost(
                        (String)h.get("host"),
                        ((Number)h.get("port")).intValue(),
                        "http"))
                .toArray(HttpHost[]::new);
        
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(
                    elasticsearchProperties.getUsername(),
                    elasticsearchProperties.getPassword()));

        RestClientBuilder builder = RestClient.builder(httpHosts)
                .setPathPrefix("/elastic-search")
                .setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        
        return new RestHighLevelClient(builder);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("Elasticsearch config loaded - hosts: {}, username: {}",
            elasticsearchProperties.getHosts(),
            elasticsearchProperties.getUsername());
        
        Environment env = event.getApplicationContext().getEnvironment();
        log.info("All elasticsearch.* properties: {}",
            env.getProperty("elasticsearch.hosts"));
    }
}