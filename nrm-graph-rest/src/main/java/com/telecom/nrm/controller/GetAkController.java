package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.glacier.model.StatusCode;
import com.telecom.da.client.DaRequest;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.GetAkDao;
import io.netty.handler.codec.http.HttpContentEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Mono;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;


@RestController
@RequestMapping("/api/getAk")
@Slf4j
public class GetAkController {


    @Autowired
    GetAkDao getAkDao;

    private WebClient webClient = WebClient.builder()
            .baseUrl("http://************:8000/serviceAgent/rest/jtgis/gisIntf/account/getAkToken")
            .build();


    @PostMapping("")
    @LogAnnotation( interfaceName="元凤地图绘制 getAK 接口-地图绘制getak")

    public JSONObject doAPI() throws NoSuchAlgorithmException {


        JSONObject emptyJson = new JSONObject();
        JSONObject getakresult = getAkDao.query_tb_ak(emptyJson,"ds_graph_js");

        JSONArray akresult = getakresult.getJSONArray("result");

        System.out.println("akresult"+akresult);

        if(akresult.size() == 1 ){
            return akresult.getJSONObject(0);
        }
        else{

        String url= "http://************:8000/serviceAgent/rest/gisIntf/account/getAkToken";
        String xappid="30249cce6de52a0052b98686cbadf3d9";
        String xappkey="bccc8e5ffdc50f1af4c186c6aedf5df0";
        String requestBody= getAkrequest();
            Mono<JSONObject> result = webClient.post().
                    uri(url)
                    .header("X-APP-ID",xappid)
                    .header("X-APP-KEY",xappkey)
                    .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                    .body(BodyInserters.fromValue(requestBody))  //JSON字符串数据
                    .retrieve() // 获取响应体
                    .bodyToMono(JSONObject.class);
            JSONObject obj= result.block();
            log.info("doAPI return "+ obj);

        getAkDao.insert_into_tb_ak(obj,"ds_graph_js");

        return obj;
        }
    }


    public  String getAkrequest() throws NoSuchAlgorithmException {
        Map<String,Object> result = new HashMap(3);
        String password = "%cPHkY4zr6MqTZ4rL7sR";  //能力账户用户密码
        String md5Password = encryptPassword(password);
        String sysName = "JS_ZYZX";  //能力账号用户名称
        String dates = dateFormat();
        StringBuilder builder = new StringBuilder();
        builder.append(sysName);
        builder.append(":");
        builder.append(md5Password);
        builder.append(":");
        builder.append(dates);
        String sign = encryptPassword(builder.toString());
        result.put("key",sysName);
        result.put("t",dates);
        result.put("sign",sign);
        return JSONObject.toJSONString(result) ;

    }
    public static String encryptPassword(String password)
            throws NoSuchAlgorithmException {
        String returnString = null;
        byte[] digestc = null;
        java.security.MessageDigest algc = java.security.MessageDigest
                .getInstance("MD5");
        algc.update(password.getBytes());
        digestc = algc.digest();
        returnString = byte2hex(digestc);
        return returnString;
    }

    private static String byte2hex(byte[] b) { // 二行制转字符串
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs.toUpperCase();
    }
    private static String dateFormat(){
        long time1 = System.currentTimeMillis();
        return String.valueOf(time1);
    }


    @PostMapping("getpoiinfo")
    @LogAnnotation( interfaceName="元凤地图绘制 getpoiinfo 接口-集团gis地图getpoiinfo")
    public ResponseEntity<JSONObject> getpoiinfo(@RequestBody JSONObject request)  {



        String ak = request.getString("ak");
        String city = request.getString("city");

        String cityName = getCityName(city);
        String rows = request.getString("rows");
        String city_limit = request.getString("city_limit");
        String keyWord = request.getString("keyWord");




//        String url= "http://************:8000/serviceAgent/rest/gisvitalservice/queryPlaceByRegion";
        String xappid="30249cce6de52a0052b98686cbadf3d9";
        String xappkey="bccc8e5ffdc50f1af4c186c6aedf5df0";

        WebClient webClient = WebClient.create();

        Mono<String> response = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .scheme("http")
                        .host("************")
                        .port(8000)
                        .path("/serviceAgent/rest/jtgis/gisvitalservice/queryPlaceByRegion")
                        .queryParam("ak", ak) // 添加参数
                        .queryParam("city", cityName)
                        .queryParam("rows", rows)
                        .queryParam("city_limit", city_limit)
                        .queryParam("keyWord", keyWord)
                        .build())
                .header("X-APP-ID", xappid) // 添加header
                .header("X-APP-KEY", xappkey) // 添加自定义header
                .retrieve() // 发起请求并获取响应
                .bodyToMono(String.class); // 将响应体转换为Mono<String>





// 使用 subscribe 来处理响应

        JSONObject result = new JSONObject();




        String obj= response.block();
        log.info("poi信息返回 "+ obj);
        JSONObject jsonObject = JSONObject.parseObject(obj);


        result.put("result",jsonObject);


        return ResponseEntity.ok(jsonObject);
    }



    public static String getCityName(String city) {
        switch (city.toLowerCase()) { // 转换为小写以确保匹配不受大小写影响
            case "nj":
                return "南京"; // 南京
            case "zj":
                return "镇江"; // 镇江
            case "wx":
                return "无锡"; // 无锡
            case "sz":
                return "苏州"; // 苏州
            case "nt":
                return "南通"; // 南通
            case "yz":
                return "扬州"; // 扬州
            case "yc":
                return "盐城"; // 盐城
            case "xz":
                return "徐州"; // 徐州
            case "ha":
                return "淮安"; // 淮安
            case "lyg":
                return "连云港"; // 连云港
            case "cz":
                return "常州"; // 常州
            case "tz":
                return "泰州"; // 泰州
            case "sq":
                return "宿迁"; // 宿迁
            default:
                return "南京"; // 如果找不到匹配项，则返回Unknown或其他默认值
        }
    }

}
