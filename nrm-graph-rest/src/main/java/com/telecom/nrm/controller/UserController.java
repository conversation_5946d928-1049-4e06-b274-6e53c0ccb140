package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.dao.UserDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/userinfo")
@Slf4j
public class UserController {



    @Autowired
    UserDao userDao;

    @PostMapping("query_rm_area")
    public JSONObject query_rm_area(@RequestBody JSONObject request) {


        System.out.println("request"+request);

        JSONObject result = userDao.query_rm_area(request,"ds_graph_js");
        System.out.println(result);

        return result;

    }

    @GetMapping("")
    public BiyiPageResult<JSONObject> queryCutOverProject(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        System.out.println("param"+param);
        PageResponse<JSONObject> queryUsers = userDao.userPageQuery(param, pageable.getSize(), pageable.getPage(),NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(queryUsers.getData(), queryUsers.getPageInfo().getTotalCount() , queryUsers.getPageInfo().getTotalCount());
    }





}
