package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.DualRouteStatisticsService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.dto.ExportResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.telecom.nrm.aop.LogAnnotation;

import java.util.*;

/**
 * 双路由光路检测运营统计控制器
 * 
 * <AUTHOR> Generated
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/api/dual-route-statistics")
public class DualRouteStatisticsController {

    private static final Logger log = LoggerFactory.getLogger(DualRouteStatisticsController.class);

    @Autowired
    private DualRouteStatisticsService dualRouteStatisticsService;

    @Autowired
    private DocumentExportService documentExportService;

    /**
     * 获取双路由月度运营统计数据
     *
     * @param request 查询请求
     * @return 月度统计数据
     */
    @PostMapping("/monthly")
    @LogAnnotation(interfaceName = "双路由月度运营统计查询")
    public ResponseEntity<JSONObject> getMonthlyStatistics(@RequestBody JSONObject request) {
        try {
            log.info("双路由月度统计查询请求，参数: {}", request);

            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);

            JSONObject result = dualRouteStatisticsService.getMonthlyStatistics(request);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("双路由月度统计查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由历史趋势分析数据
     *
     * @param request 查询请求
     * @return 趋势分析数据
     */
    @PostMapping("/trend")
    @LogAnnotation(interfaceName = "双路由历史趋势分析查询")
    public ResponseEntity<JSONObject> getTrendAnalysis(@RequestBody JSONObject request) {
        try {
            log.info("双路由趋势分析查询请求，参数: {}", request);

            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);

            JSONObject result = dualRouteStatisticsService.getTrendAnalysis(request);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("双路由趋势分析查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }





    /**
     * 获取双路由变化统计
     *
     * @param request 查询请求
     * @return 变化统计数据
     */
    @PostMapping("/change-statistics")
    @LogAnnotation(interfaceName = "双路由变化统计查询")
    public ResponseEntity<JSONObject> getChangeStatistics(@RequestBody JSONObject request) {
        try {
            log.info("双路由变化统计查询请求，参数: {}", request);

            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);

            JSONObject result = dualRouteStatisticsService.getChangeStatistics(request);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("双路由变化统计查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由风险对象列表
     * 
     * @param params 查询参数
     * @return 风险对象数据
     */
    @GetMapping("/risk-objects")
    @LogAnnotation(interfaceName = "双路由风险对象查询")
    public ResponseEntity<JSONObject> getRiskObjects(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("双路由风险对象查询请求，参数: {}", params);
            
            JSONObject param = new JSONObject();
            if (params != null) {
                param.putAll(params);
            }
            
            // 提取分库代码
            String shardingCode = extractShardingCode(param);
            param.put("shardingCode", shardingCode);
            
            JSONObject result = dualRouteStatisticsService.getRiskObjects(param);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("双路由风险对象查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由运营KPI汇总数据
     * 
     * @param params 查询参数
     * @return KPI汇总数据
     */
    @GetMapping("/kpi")
    @LogAnnotation(interfaceName = "双路由KPI汇总查询")
    public ResponseEntity<JSONObject> getKpiSummary(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("双路由KPI汇总查询请求，参数: {}", params);
            
            JSONObject param = new JSONObject();
            if (params != null) {
                param.putAll(params);
            }
            
            // 提取分库代码
            String shardingCode = extractShardingCode(param);
            param.put("shardingCode", shardingCode);
            
            JSONObject result = dualRouteStatisticsService.getKpiSummary(param);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("双路由KPI汇总查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由地区对比分析数据
     * 
     * @param request 查询请求
     * @return 地区对比分析数据
     */
    @PostMapping("/area-comparison")
    @LogAnnotation(interfaceName = "双路由地区对比分析查询")
    public ResponseEntity<JSONObject> getAreaComparison(@RequestBody JSONObject request) {
        try {
            log.info("双路由地区对比分析查询请求，参数: {}", request);
            
            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);
            
            JSONObject result = dualRouteStatisticsService.getAreaComparison(request);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("双路由地区对比分析查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由问题类型分析数据
     * 
     * @param request 查询请求
     * @return 问题类型分析数据
     */
    @PostMapping("/problem-analysis")
    @LogAnnotation(interfaceName = "双路由问题类型分析查询")
    public ResponseEntity<JSONObject> getProblemAnalysis(@RequestBody JSONObject request) {
        try {
            log.info("双路由问题类型分析查询请求，参数: {}", request);
            
            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);
            
            JSONObject result = dualRouteStatisticsService.getProblemAnalysis(request);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("双路由问题类型分析查询失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取双路由运营报表导出数据
     */
    @PostMapping("/export")
    @LogAnnotation(interfaceName = "双路由运营报表导出")
    public ResponseEntity<ExportResponseDTO> getExportData(@RequestBody JSONObject request) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [双路由导出] 接收到双路由运营报表导出请求");
            log.info("📋 [双路由导出] 请求参数: {}", request);

            // 参数验证
            if (request == null || request.isEmpty()) {
                log.error("❌ [双路由导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);
            log.info("📍 [双路由导出] 分库代码: {}", shardingCode);

            // 1. 查询双路由统计数据
            log.info("🔍 [双路由导出] 步骤1: 开始查询双路由统计数据");
            long queryStartTime = System.currentTimeMillis();
            JSONObject result = dualRouteStatisticsService.getExportData(request);
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [双路由导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            if (result == null || !result.getBoolean("success")) {
                String errorMsg = result != null ? result.getString("message") : "查询结果为空";
                log.warn("⚠️ [双路由导出] 查询失败: {}", errorMsg);
                return ResponseEntity.ok(ExportResponseDTO.failure("查询双路由统计数据失败: " + errorMsg));
            }

            // 2. 转换数据格式
            log.info("🔄 [双路由导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertDualRouteStatisticsData(result);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [双路由导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [双路由导出] 转换后数据量: {} 条记录", exportData.size());

            // 3. 定义列
            log.info("📝 [双路由导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "统计月份:month",
                "地区:areaName",
                "保护组总数:totalGroups",
                "正常保护组:normalGroups",
                "风险保护组:riskGroups",
                "高风险保护组:highRiskGroups",
                "中风险保护组:mediumRiskGroups",
                "低风险保护组:lowRiskGroups",
                "已整改数量:remediatedCount",
                "待整改数量:pendingCount",
                "整改率:remediationRate",
                "风险率:riskRate",
                "统计时间:statisticsTime"
            );
            log.info("📋 [双路由导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [双路由导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO exportResult = documentExportService.exportToDocumentSecurity(
                exportData, columns, "双路由运营报表", "双路由运营报表导出",
                "双路由统计", "/api/dual-route-statistics/export", "双路由运营报表数据导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [双路由导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (exportResult.isSuccess()) {
                exportResult.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [双路由导出] 导出成功完成!");
                log.info("📁 [双路由导出] 文件名: {}", exportResult.getFileName());
                log.info("📊 [双路由导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [双路由导出] 总耗时: {} ms", totalTime);
                log.info("📈 [双路由导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [双路由导出] 导出失败: {}", exportResult.getMessage());
            }

            return ResponseEntity.ok(exportResult);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [双路由导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [双路由导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    /**
     * 生成双路由运营统计报告
     *
     * @param request 查询请求
     * @return 运营统计报告
     */
    @PostMapping("/report")
    @LogAnnotation(interfaceName = "双路由运营统计报告生成")
    public ResponseEntity<JSONObject> generateReport(@RequestBody JSONObject request) {
        try {
            log.info("双路由运营统计报告生成请求，参数: {}", request);

            // 提取分库代码
            String shardingCode = extractShardingCode(request);
            request.put("shardingCode", shardingCode);

            JSONObject result = dualRouteStatisticsService.generateOperationalReport(request);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("双路由运营统计报告生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "报告生成失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取业务指标类型说明
     *
     * @return 业务指标类型说明
     */
    @GetMapping("/metric-types")
    @LogAnnotation(interfaceName = "双路由业务指标类型查询")
    public ResponseEntity<JSONObject> getMetricTypes() {
        try {
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "查询成功");

            JSONObject metricTypes = new JSONObject();
            metricTypes.put("remediation", dualRouteStatisticsService.getMetricTypeDescription("remediation"));
            metricTypes.put("confirmation", dualRouteStatisticsService.getMetricTypeDescription("confirmation"));
            metricTypes.put("improvement", dualRouteStatisticsService.getMetricTypeDescription("improvement"));
            metricTypes.put("deterioration", dualRouteStatisticsService.getMetricTypeDescription("deterioration"));

            result.put("data", metricTypes);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取业务指标类型说明失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 提取分库代码
     *
     * @param param 参数对象
     * @return 分库代码
     */
    private String extractShardingCode(JSONObject param) {
        // 从请求参数中提取分库代码
        String shardingCode = param.getString("shardingCode");

        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            // 如果没有直接提供分库代码，尝试从地区代码推导
            String areaCode = param.getString("areaCode");
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                // 根据江苏地市代码生成分库代码
                shardingCode = "ds_bc_o3_" + areaCode.toLowerCase();
            }

            // 如果仍然没有分库代码，使用默认值
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = NRMConstants.SHARDING_CODE;
            }
        }

        return shardingCode;
    }

    /**
     * 转换双路由统计数据格式
     */
    private List<Map<String, Object>> convertDualRouteStatisticsData(JSONObject result) {
        log.info("🔄 [数据转换] 开始转换双路由统计数据格式");
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            JSONObject resultData = result.getJSONObject("data");
            if (resultData == null) {
                log.warn("⚠️ [数据转换] 结果数据为空");
                return data;
            }

            // 处理月度统计数据
            JSONArray monthlyStats = resultData.getJSONArray("monthlyStats");
            if (monthlyStats != null && !monthlyStats.isEmpty()) {
                log.info("📊 [数据转换] 处理月度统计数据，数量: {}", monthlyStats.size());

                for (int i = 0; i < monthlyStats.size(); i++) {
                    try {
                        JSONObject item = monthlyStats.getJSONObject(i);

                        Map<String, Object> row = new HashMap<>();
                        row.put("month", item.getString("month") != null ? item.getString("month") : "");
                        row.put("areaName", item.getString("areaName") != null ? item.getString("areaName") : "");
                        row.put("totalGroups", item.getInteger("totalGroups") != null ? item.getInteger("totalGroups") : 0);
                        row.put("normalGroups", item.getInteger("normalGroups") != null ? item.getInteger("normalGroups") : 0);
                        row.put("riskGroups", item.getInteger("riskGroups") != null ? item.getInteger("riskGroups") : 0);
                        row.put("highRiskGroups", item.getInteger("highRiskGroups") != null ? item.getInteger("highRiskGroups") : 0);
                        row.put("mediumRiskGroups", item.getInteger("mediumRiskGroups") != null ? item.getInteger("mediumRiskGroups") : 0);
                        row.put("lowRiskGroups", item.getInteger("lowRiskGroups") != null ? item.getInteger("lowRiskGroups") : 0);
                        row.put("remediatedCount", item.getInteger("remediatedCount") != null ? item.getInteger("remediatedCount") : 0);
                        row.put("pendingCount", item.getInteger("pendingCount") != null ? item.getInteger("pendingCount") : 0);

                        // 计算比率
                        Double remediationRate = item.getDouble("remediationRate");
                        row.put("remediationRate", remediationRate != null ? String.format("%.2f%%", remediationRate) : "0.00%");

                        Double riskRate = item.getDouble("riskRate");
                        row.put("riskRate", riskRate != null ? String.format("%.2f%%", riskRate) : "0.00%");

                        row.put("statisticsTime", item.getString("statisticsTime") != null ? item.getString("statisticsTime") : "");

                        data.add(row);

                    } catch (Exception e) {
                        log.error("❌ [数据转换] 转换第 {} 条月度统计记录时出错: {}", i + 1, e.getMessage());
                    }
                }
            }

            // 处理趋势分析数据
            JSONArray trendData = resultData.getJSONArray("trendData");
            if (trendData != null && !trendData.isEmpty()) {
                log.info("📊 [数据转换] 处理趋势分析数据，数量: {}", trendData.size());

                for (int i = 0; i < trendData.size(); i++) {
                    try {
                        JSONObject item = trendData.getJSONObject(i);

                        Map<String, Object> row = new HashMap<>();
                        row.put("month", item.getString("period") != null ? item.getString("period") : "");
                        row.put("areaName", item.getString("areaName") != null ? item.getString("areaName") : "");
                        row.put("totalGroups", item.getInteger("totalCount") != null ? item.getInteger("totalCount") : 0);
                        row.put("normalGroups", item.getInteger("normalCount") != null ? item.getInteger("normalCount") : 0);
                        row.put("riskGroups", item.getInteger("riskCount") != null ? item.getInteger("riskCount") : 0);
                        row.put("highRiskGroups", item.getInteger("highRisk") != null ? item.getInteger("highRisk") : 0);
                        row.put("mediumRiskGroups", item.getInteger("mediumRisk") != null ? item.getInteger("mediumRisk") : 0);
                        row.put("lowRiskGroups", item.getInteger("lowRisk") != null ? item.getInteger("lowRisk") : 0);
                        row.put("remediatedCount", item.getInteger("remediated") != null ? item.getInteger("remediated") : 0);
                        row.put("pendingCount", item.getInteger("pending") != null ? item.getInteger("pending") : 0);

                        // 计算比率
                        Double remediationRate = item.getDouble("remediationRate");
                        row.put("remediationRate", remediationRate != null ? String.format("%.2f%%", remediationRate) : "0.00%");

                        Double riskRate = item.getDouble("riskRate");
                        row.put("riskRate", riskRate != null ? String.format("%.2f%%", riskRate) : "0.00%");

                        row.put("statisticsTime", item.getString("updateTime") != null ? item.getString("updateTime") : "");

                        data.add(row);

                    } catch (Exception e) {
                        log.error("❌ [数据转换] 转换第 {} 条趋势分析记录时出错: {}", i + 1, e.getMessage());
                    }
                }
            }

            // 处理汇总数据
            JSONObject summary = resultData.getJSONObject("summary");
            if (summary != null) {
                log.info("📊 [数据转换] 处理汇总数据");

                try {
                    Map<String, Object> row = new HashMap<>();
                    row.put("month", "汇总");
                    row.put("areaName", "全部地区");
                    row.put("totalGroups", summary.getInteger("totalGroups") != null ? summary.getInteger("totalGroups") : 0);
                    row.put("normalGroups", summary.getInteger("normalGroups") != null ? summary.getInteger("normalGroups") : 0);
                    row.put("riskGroups", summary.getInteger("riskGroups") != null ? summary.getInteger("riskGroups") : 0);
                    row.put("highRiskGroups", summary.getInteger("highRiskGroups") != null ? summary.getInteger("highRiskGroups") : 0);
                    row.put("mediumRiskGroups", summary.getInteger("mediumRiskGroups") != null ? summary.getInteger("mediumRiskGroups") : 0);
                    row.put("lowRiskGroups", summary.getInteger("lowRiskGroups") != null ? summary.getInteger("lowRiskGroups") : 0);
                    row.put("remediatedCount", summary.getInteger("remediatedCount") != null ? summary.getInteger("remediatedCount") : 0);
                    row.put("pendingCount", summary.getInteger("pendingCount") != null ? summary.getInteger("pendingCount") : 0);

                    // 计算比率
                    Double remediationRate = summary.getDouble("remediationRate");
                    row.put("remediationRate", remediationRate != null ? String.format("%.2f%%", remediationRate) : "0.00%");

                    Double riskRate = summary.getDouble("riskRate");
                    row.put("riskRate", riskRate != null ? String.format("%.2f%%", riskRate) : "0.00%");

                    row.put("statisticsTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

                    data.add(row);

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换汇总数据时出错: {}", e.getMessage());
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功转换: {} 条记录", data.size());

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

}
