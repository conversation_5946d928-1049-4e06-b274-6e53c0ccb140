package com.telecom.nrm.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.biyi.ResponseFactory;
import com.telecom.common.biyi.Result;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.constant.ShardingEnum;
import com.telecom.nrm.dao.OltInfoDao;
import com.telecom.nrm.dao.OptGroupDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.OltService;
import com.telecom.nrm.service.OptGroupService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/api/olt")
@Slf4j
public class OLTController {

    @Autowired
    private OltInfoDao oltInfoDao;

    @Autowired
    OltService oltService;

    @Autowired
    OptGroupService optGroupService;

    @Autowired
    OptGroupDao optGroupDao;

    @GetMapping("queryByPage")
    @LogAnnotation(interfaceName ="OLT上联管理-OLT查询")
    public BiyiPageResult<JSONObject> queryByPage(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        String areaCode=jsonObject.getString("areaCode");
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

        PageResponse<JSONObject> pageResponse = oltInfoDao.queryResOltInfo(jsonObject, pageable.getSize(), pageable.getPage(), shardingCode);
        JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
        oltService.addAlarmInfo(pageResponse.getData(),ShardingEnum.getShardingEnumByCityAbb(areaCode.toLowerCase(Locale.ROOT)).getRegionName());
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount(), pageResponse.getPageInfo().getTotalCount());
    }

    @GetMapping("queryLinkAndDevices")
    @LogAnnotation(interfaceName ="OLT上联管理-链路拓扑查询")
    public Result queryLinkAndDevices(@RequestParam(required = false) Map example) {
        Map<String, Object> resultMap = new HashMap<>();
        String url ="http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/opt-group-api/risk-analyze-previous";
        try {
            JSONObject analyzeQuery =new JSONObject();
//            analyzeQuery.put("name", MapUtils.getString(example, "deviceName"));
            JSONObject param = new JSONObject();
            param.put("name", MapUtils.getString(example, "deviceName"));
            param.put("source_type_id", 10);
            param.put("page", 1);
            param.put("size", 10);
            param.put("ds", MapUtils.getString(example, "area"));
            param.put("object_tyoe","optGroup");
            String id = "-1";
            String ds = ShardingEnum.getShardingEnumByRegionName(MapUtils.getString(example, "area")).getBcShardingCode();
            PageResponse<JSONObject> optGroupPage = optGroupDao.pm_opt_road_group_result_query(param, 10, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(MapUtils.getString(example, "area")));
            if (ObjectUtil.isNotNull(optGroupPage) && CollectionUtils.isNotEmpty(optGroupPage.getData())) {
                id = optGroupPage.getData().get(0).getString("id");
            }


            analyzeQuery.put("id", id);
            analyzeQuery.put("ds", MapUtils.getString(example, "area"));
            analyzeQuery.put("cached", true);
            JSONObject riskAnalyzePrevious = optGroupService.risk_analyze_previous(analyzeQuery);
           // JSONObject riskAnalyzePrevious = get();
            resultMap.put("risk",Node.join(riskAnalyzePrevious));
            JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
            String areaCode = jsonObject.getString("area");
            BigDecimal oltId = jsonObject.getBigDecimal("oltId");
            ShardingEnum shardingEnum = ShardingEnum.getShardingEnumByRegionName(areaCode);
            String shardingCode = shardingEnum.getBcShardingCode();
            List<JSONObject> links = oltInfoDao.queryResOltRelaLink(oltId, shardingCode);
            JSONObjectUtil.convertBigNumberToString(links);
            resultMap.put("links", links);
            if (CollectionUtils.isNotEmpty(links)) {
                Set<BigDecimal> deviceIds = links.stream()
                        .flatMap(e -> Stream.of(e.getBigDecimal("z_device_id"), e.getBigDecimal("a_device_id")))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                List<JSONObject> devices = oltInfoDao.queryResOltByIds(deviceIds, shardingCode);
                JSONObjectUtil.convertBigNumberToString(devices);
                resultMap.put("devices", devices);
            }
            return ResponseFactory.successResponse(resultMap);
        } catch (Exception e) {
            log.error("",e);
            return ResponseFactory.failResponse("接口异常" + e.getMessage());
        }



    }

/*    public static JSONObject  get() throws IOException {

        String url ="http://nrm.oss.telecomjs.com:39049/graph-rest-api/api/opt-group-api/risk-analyze-previous";
        JSONObject analyzeQuery =new JSONObject();
        analyzeQuery.put("name", "营房中兴OLT-5(公安监控专用)");
        analyzeQuery.put("ds", "南京");
        analyzeQuery.put("cached", false);
        String result = HttpRequestsUtils.sendPost(url,null,analyzeQuery);
        System.out.println(result);
        return  JSONObject.parseObject(result);

    }*/

    @Data
    public static class Node {
        private String dangerType;
        private String dangerCount;
        private String sameRouteLength;
        private String riskLevel = "中等风险";

        public static List<Node> join(JSONObject object) {
            if (null == object) return ListUtil.empty();
            List<Node> nodes = new ArrayList<>();
            if (object.containsKey("count_sameCables") && object.getInteger("count_sameCables") > 0) {
                Node node = new Node();
                node.setDangerType("同光缆");
                node.setDangerCount(object.getInteger("count_sameCables").toString());
                nodes.add(node);
            }
            if (object.containsKey("count_samePipeSegments") && object.getInteger("count_samePipeSegments") > 0) {
                Node node = new Node();
                node.setDangerType("同管道");
                JSONArray samePipeSegments = object.getJSONArray("samePipeSegments");
                if (CollectionUtils.isNotEmpty(samePipeSegments)) {
                    long sum = 0;
                    for (int i = 0; i < samePipeSegments.size(); i++) {
                        if (samePipeSegments.getJSONObject(i).containsKey("length") && samePipeSegments.getJSONObject(i).getString("length") !=null ) {
                            sum += samePipeSegments.getJSONObject(i).getLong("length");

                        }
                    }


                    node.setSameRouteLength(String.valueOf(sum));
                }
                node.setDangerCount(object.getInteger("count_samePipeSegments").toString());
                nodes.add(node);
            }
            return nodes;

        }
    }

    @GetMapping("/nt")
    public String nt(@RequestParam(required = false) Map example) {
        List<JSONObject> list = oltService.selectAllOltBySharding("ds_bc_o3_nt");
        int i = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject jsonObject : list) {
                String name = jsonObject.getString("name");
                String code = jsonObject.getString("code");
                BigDecimal id = jsonObject.getBigDecimal("id");
                System.out.println("====>" + (i++) + ":" + code);
                oltService.createOptGroupAndElement(code, name, "ds_bc_o3_nt", id);
            }
        }
        return "123";
    }


}
