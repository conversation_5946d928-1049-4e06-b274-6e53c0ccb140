package com.telecom.nrm.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustViewDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.graph.GraphScene;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/custview")
@Slf4j
public class CustViewController {

    @Autowired
    RegionDao regionDao;
    @Autowired
    CustViewDao custViewDao;
    @GetMapping("")
    public BiyiPageResult<JSONObject> getCustViewList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        // log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        PageResponse<JSONObject> pageResponse =  custViewDao.queryCustview(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        log.info("pageResponse");
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            for(int i =0 ;i<pageResponse.getData().size();i++){
                if(pageResponse.getData().get(i).getString("region").equals("江苏省公司")){
                    pageResponse.getData().get(i).put("region", "江苏省");
                }
            }
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }


    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();


        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){

            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))||Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100699"))){
                    Region2List.get(i).setName("江苏省");
                }
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        return ResponseEntity.ok().body(meta);
    }


    @RequestMapping("/save")
    public JSONObject save(@RequestBody JSONObject jsonObject) {
        if (ObjectUtil.isEmpty(jsonObject.getString("id"))) {
            jsonObject.put("create_time", DateTime.now());
            jsonObject.put("modify_time", DateTime.now());
        }else{
            jsonObject.put("modify_time",DateTime.now());
        }
        Integer result_1 =  custViewDao.saveCustviewsave(jsonObject ,NRMConstants.SHARDING_GRAPH_DB);
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }



    @DeleteMapping("/{id}")
    public JSONObject deletebyid(@PathVariable String id) {
        Integer result_1 =  custViewDao.deleteCustview(id ,NRMConstants.SHARDING_GRAPH_DB);
        JSONObject result = new JSONObject();
        if(result_1 ==1){
            result.put("result","sueccess");
        }else{
            result.put("result", "error");
        }
        return result;
    }

    @GetMapping("/{id}")
    public ResponseEntity<JSONObject> get(@PathVariable Long id) {
        log.debug("REST request to get entity : {}", id);
        JSONObject custView = custViewDao.getById(id,NRMConstants.SHARDING_GRAPH_DB);
        return ResponseEntity.ok()
                .body(custView);
    }


}
