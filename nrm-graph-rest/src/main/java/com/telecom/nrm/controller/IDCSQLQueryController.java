package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.IdcAuditDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/idc-query")
@Slf4j
public class IDCSQLQueryController {

    @Autowired
    private IdcAuditDao idcAuditDao;


    @RequestMapping("/sql_query")
    public JSONObject sql_query(@RequestBody  JSONObject requestBody) {

        try {
            String sql = requestBody.getString("sql");
            if (StringUtils.isEmpty(sql)) {
                return new JSONObject();
            }
            JSONObject result = new JSONObject();
            JSONObject param = new JSONObject();
            param.put("pbSql", sql);
            JSONArray data = idcAuditDao.idcDynamicQuery(param, "ds_idc_js").getJSONArray("data");
            result.put("data", data);
            return result;
        } catch (Exception e) {
            log.error("SQL query error", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("message", "SQL query execution failed: " + e.getMessage());

            // Capture full stack trace
            StringBuffer stackTrace = new StringBuffer();
            StackTraceElement[] stackElements = e.getStackTrace();
            stackTrace.append(e.toString()).append("\n");
            for (StackTraceElement element : stackElements) {
                stackTrace.append("\tat ").append(element.toString()).append("\n");
            }

            // Add cause if exists
            Throwable cause = e.getCause();
            while (cause != null) {
                stackTrace.append("Caused by: ").append(cause.toString()).append("\n");
                for (StackTraceElement element : cause.getStackTrace()) {
                    stackTrace.append("\tat ").append(element.toString()).append("\n");
                }
                cause = cause.getCause();
            }

            errorResult.put("error", stackTrace.toString());
            return errorResult;
        }
    }
}
