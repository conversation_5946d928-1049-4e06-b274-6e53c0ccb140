package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CircuitAnalysisDao;
import com.telecom.nrm.dao.DeviceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.HunderRiverService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/hunderRiver")

public class HunderRiverController {


    @Autowired
    HunderRiverService hunderRiverService;

    @Autowired
    DeviceDao deviceDao;

    @Autowired
    CircuitAnalysisDao circuitAnalysisDao;

    @GetMapping("/zdOrderQuery")
    public JSONObject zdOrderQuery(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        return circuitAnalysisDao.custFaultQuery(jsonObject,NRMConstants.SHARDING_GRAPH_DB);
    }

    @GetMapping("/qualityDifferenceQuery")
    @LogAnnotation( interfaceName="质量详细信息")
    public PageResponse<JSONObject> qualityDifferenceQuery(@RequestParam(required = false) Map example) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        return deviceDao.qualityDifferenceQuery(jsonObject, NRMConstants.SHARDING_CODE);

    }

    @GetMapping("/queryAlarmByAccessNumber")
    public JSONObject queryAlarmByAccessNumber(@RequestParam(required = false) Map example) throws IOException {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        return hunderRiverService.queryAlarmByAccessNumber(jsonObject);

    }


}
