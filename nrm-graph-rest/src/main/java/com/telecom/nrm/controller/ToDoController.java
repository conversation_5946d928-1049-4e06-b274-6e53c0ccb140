package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.ToDoDao;
import com.telecom.nrm.domain.Link;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;


@RestController
@RequestMapping("/api/todo")
@Slf4j
public class ToDoController {
    private String CLIENT_SECRET = "3e558bc6-c133-4207-b7ec-f99ff4d88d08";
    private String CLIENT_ID="ct_oss_nrm";
    private String SSO_URL="https://dl.telecomjs.com";
    private String REDIRECT_URI="http://nrm.oss.telecomjs.com:39049";
    private WebClient webClient = WebClient.builder().baseUrl(SSO_URL).build();

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ToDoDao toDoDao;

    @PostMapping("/toDoForPortal")
    public String getToDoOrders(@RequestParam(required = false) Map example) {
        JSONObject resp = new JSONObject();
        resp.put("success", "true");
        resp.put("inDoing", new JSONObject());
        resp.put("haveDone", new JSONObject());
        resp.put("toRead", new JSONObject());
        resp.put("inReading", new JSONObject());
        JSONObject toDo = new JSONObject();
        toDo.put("moreUrl", "http://nrm.oss.telecomjs.com:39049/#/nrm/todo/list");
        toDo.put("data", new ArrayList<JSONObject>());
        JSONObject params = (JSONObject) JSON.toJSON(example);
        String query = params.getString("query");
        String callback  =params.getString("callback");
        JSONObject childTokenObject = geChildAccessTokenBySid(query);
        String childAccessToken = childTokenObject.getString("access_token");
        JSONObject childAccessTokenParam = new JSONObject();
        childAccessTokenParam.put("redirect_uri", REDIRECT_URI);
        childAccessTokenParam.put("access_token", childAccessToken);
        JSONObject codeObject = getCodeByChildAccessToken(childAccessTokenParam);
        String code = codeObject.getString("code");
        JSONObject ssoParam = new JSONObject();
        ssoParam.put("code", code);
        ssoParam.put("redirect_uri", REDIRECT_URI);
        JSONObject tokenObject = getSsoAccessToken(ssoParam);
        String accessToken= tokenObject.getString("access_token");
        JSONObject ssoUser = getSsoUser(accessToken);
        String phone = ssoUser.getString("phone");
        JSONObject userIDByUserPhone = toDoDao.getUserIDByUserPhone(phone, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtil.isEmpty(userIDByUserPhone)) {
            resp.put("userName", "");
            toDo.put("count", 0);
            resp.put("toDo", toDo);
            String respCallBack = callback +"("+resp.toString()+")";
            return respCallBack;
        }
        String userID = userIDByUserPhone.getString("user_id");
        String userName = userIDByUserPhone.getString("name");
        resp.put("userName", userName);
        String orders = toDoDao.getOrdersByUserID(userID, NRMConstants.SHARDING_GRAPH_DB);
        toDo.put("count", orders);
        resp.put("toDo", toDo);
        String respCallBack = callback +"("+ resp +")";
        return respCallBack;
    }

    @GetMapping("")
    public BiyiPageResult<JSONObject> getToDoLists(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JwtUser jwtUser = SecurityContext.getJwtUser();
        String userId = jwtUser.getId();
        if (ObjectUtil.isEmpty(userId)){
            return new BiyiPageResult();
        }
        JSONObject params = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> toDoPageResponse = toDoDao.queryToDoByUserId(userId, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(toDoPageResponse.getData(), toDoPageResponse.getPageInfo().getTotalCount(), toDoPageResponse.getPageInfo().getTotalCount());
    }

    @PutMapping("/update/{id}")
    public ResponseEntity getToDoLists(@PathVariable(name="id") BigDecimal id) {
//        JwtUser jwtUser = SecurityContext.getJwtUser();
//        String userId = jwtUser.getId();
//        Integer updateRes = toDoDao.updateStatusByUserID(userId, NRMConstants.SHARDING_GRAPH_DB);
        String finishDate = new Date().toString();
        JSONObject params = new JSONObject();
        params.put("id", id);
        params.put("finish_date", finishDate);
        try {
            Integer updateRes = toDoDao.updateStatusByUserID(params, NRMConstants.SHARDING_GRAPH_DB);
            return ResponseEntity.created(new URI("/api/todo"))
                    .body(updateRes);
        }catch (Exception ex) {
            log.error(ex.getMessage(),ex);
            return ResponseEntity.status(500).body(ex.getMessage().split("\n")[0]);
        }
    }

    JSONObject getSsoAccessToken(JSONObject params) {
        String redirect_uri = params.getString("redirect_uri");
        String code = params.getString("code");
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        // 设置请求参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("client_id", CLIENT_ID);
        postParameters.add("client_secret", CLIENT_SECRET);
        postParameters.add("grant_type", "authorization_code");
        postParameters.add("code", code);
        postParameters.add("redirect_uri", redirect_uri);
        Mono<JSONObject> resp = webClient.post().
                uri("/SingleSignOn/accessToken")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)  //JSON数据类型
                .syncBody(postParameters)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体
        JSONObject result= resp.block();
//        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);
//        JSONObject result = restTemplate.postForObject(SSO_URL+"/SingleSignOn/accessToken",httpEntity,JSONObject.class);
        return result;
    }

    JSONObject getSsoUser(String ssoToken) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        // 设置请求参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("access_token", ssoToken);
        Mono<JSONObject> resp = webClient.post().
                uri("/SingleSignOn/user/info")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)  //JSON数据类型
                .syncBody(postParameters)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体
        JSONObject result= resp.block();
//        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);
//        JSONObject result = restTemplate.postForObject(SSO_URL+"/SingleSignOn/user/info",httpEntity,JSONObject.class);
        return result;
    }

    JSONObject geChildAccessTokenBySid(String oauth2_user_id) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        // 设置请求参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("client_id", CLIENT_ID);
        postParameters.add("client_secret", CLIENT_SECRET);
        postParameters.add("grant_type", "sid_check");
        postParameters.add("sid", oauth2_user_id);
        Mono<JSONObject> resp = webClient.post().
                uri("/APPSingleSignOn/getTokenBySid")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)  //JSON数据类型
                .syncBody(postParameters)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体
        JSONObject result= resp.block();
//        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);
//        JSONObject result = restTemplate.postForObject(SSO_URL+"/APPSingleSignOn/getTokenBySid",httpEntity,JSONObject.class);
        return result;
    }

    JSONObject getCodeByChildAccessToken(JSONObject params) {
        String redirect_uri = params.getString("redirect_uri");
        String access_token = params.getString("access_token");
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        // 设置请求参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("client_id", CLIENT_ID);
        postParameters.add("client_secret", CLIENT_SECRET);
        postParameters.add("access_token", access_token);
        postParameters.add("redirect_uri", redirect_uri);
        postParameters.add("response_type", "code");
        Mono<JSONObject> resp = webClient.post().
                uri("/SingleSignOn/authorizeChild")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)  //JSON数据类型
                .syncBody(postParameters)  //JSON字符串数据
                .retrieve()
                .bodyToMono(JSONObject.class); // 获取响应体
        JSONObject result= resp.block();
//        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);
//        JSONObject result = restTemplate.postForObject(SSO_URL+"/SingleSignOn/authorizeChild",httpEntity,JSONObject.class);
        return result;
    }
}
