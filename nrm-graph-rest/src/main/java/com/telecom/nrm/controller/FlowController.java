package com.telecom.nrm.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.FlowDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.FlowService;
import com.telecom.nrm.utils.HttpClientUtil;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/flow")
@Slf4j
public class FlowController {

    @Autowired
    FlowService flowService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    FlowDao flowDao;

    @PostMapping("/create")
    public JSONObject flow_create(@RequestBody JSONObject jsonObject) throws IOException {
        String flow_create_url = "http://*************:39110/open/flow/create";
        JSONObject result = flowService.doPost(flow_create_url, jsonObject);
        if(result.getString("success").equals("true")){
            JSONObject save_flow = new JSONObject();
            save_flow.put("instance_id",result.getString("result"));
            save_flow.put("flow_code", jsonObject.getString("flowCode"));
            save_flow.put("create_op", jsonObject.getJSONObject("variables").getString("startUser"));
            flowService.pm_flow_instance_insert(save_flow);
        }
        return result;
    }

    @PostMapping("/update_flow")
    public JSONObject update_flow(@RequestBody JSONObject jsonObject) throws IOException {
        JSONObject save_flow = new JSONObject();
        save_flow.put("instance_id",jsonObject.getString("flow_id"));
        save_flow.put("title", jsonObject.getString("title"));
        save_flow.put("reason", jsonObject.getString("reason"));
        save_flow.put("process_definition_id",jsonObject.getString("process_definition_id"));
        save_flow.put("process_name", jsonObject.getString("process_name"));
        save_flow.put("state", "流程中");
        JSONObject save_flow_result = flowService.pm_flow_instance_insert(save_flow);
        return save_flow_result;
    }

    @PostMapping("/myTodo")
    public JSONObject flow_myTodo(@RequestBody JSONObject jsonObject) throws IOException {
        return flowService.flow_myTodoService(jsonObject);
    }

    @PostMapping("/commit")
    public JSONObject flow_commit(@RequestBody JSONObject jsonObject) throws IOException {
        String flow_commit_url = "http://*************:39110/open/flow/commit";
        JSONObject result = flowService.doPost(flow_commit_url, jsonObject);
        if(ObjectUtil.isNotNull(result.getString("success")) && result.getString("success").equals("true")){
            JSONObject save_flow = flowService.flow_param(jsonObject);
            save_flow.put("result", jsonObject.getString("flowMap"));
            save_flow.put("task_type", "前进下一环节");
            JSONObject save_task_result = flowService.pm_task_instance_insert(save_flow);
            if(save_task_result.getString("result").equals("1")){
                flowService.pm_rela_flow_task_insert(save_flow);
            }
            if(ObjectUtil.isNotNull(result.getJSONObject("result")) && ObjectUtil.isNotNull(result.getJSONObject("result").getString("finish")) && result.getJSONObject("result").getString("finish").equals("Y")){
                JSONObject query_flow = new JSONObject();
                query_flow.put("instance_id",jsonObject.getString("flow_id"));
                JSONObject flow_instance = flowService.pm_flow_instance_query(query_flow).getJSONObject("result");
                if(ObjectUtil.isNotNull(flow_instance.getString("flow_code")) && flow_instance.getString("flow_code").equals("white_apply")){
                    List<JSONObject> res_list = getResByFlow(query_flow);
                    for(JSONObject res : res_list){
                        JSONObject current_res_save = new JSONObject();
                        current_res_save.put("pipleline_code", res.getString("code"));
                        current_res_save.put("type", res.getString("type"));
                        custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(current_res_save, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(res.getString("area")));
                    }
                }
            }
        }
        return result;
    }

    @PostMapping("/transfer")
    public JSONObject flow_transfer(@RequestBody JSONObject jsonObject) throws IOException {
        String flow_transfer_url = "http://*************:39110/open/flow/transfer";
        JSONObject result = flowService.doPost(flow_transfer_url, jsonObject);
        if(result.getString("success").equals("true")) {
            JSONObject save_flow = flowService.flow_param(jsonObject);
            save_flow.put("task_type", "转交");
            JSONObject save_task_result = flowService.pm_task_instance_insert(save_flow);
            if (save_task_result.getString("result").equals("1")) {
                flowService.pm_rela_flow_task_insert(save_flow);
            }
        }
        return result;
    }

    @PostMapping("/stop/{processInstanceId}")
    public JSONObject flow_stop(@PathVariable(name="processInstanceId") String processInstanceId, @RequestBody JSONObject jsonObject) throws IOException {
        String flow_transfer_url = "http://*************:39110/open/flow/end/"+processInstanceId;
        JSONObject result = flowService.doPost(flow_transfer_url, null);
        if(result.getString("success").equals("true")) {
            JSONObject update_flow = new JSONObject();
            update_flow.put("state", "终止");
            update_flow.put("deal_op", jsonObject.getString("userCode"));
            update_flow.put("instance_id", jsonObject.getString("flow_id"));
            JSONObject update_flow_result = flowService.pm_flow_instance_update(update_flow);
        }
        return result;
    }

    @PostMapping("/queryFlow/{processInstanceId}")
    public JSONObject flow_query(@PathVariable(name="processInstanceId") String processInstanceId) throws IOException {
        JSONObject query_flow = new JSONObject();
        query_flow.put("instance_id",processInstanceId);
        JSONObject result = flowService.pm_flow_instance_query(query_flow).getJSONObject("result");
        result.put("resource", getResByFlow(query_flow));
        return result;
    }

    @PostMapping("/queryTask/{taskId}")
    public JSONObject task_query(@PathVariable(name="taskId") String taskId) throws IOException {
        JSONObject query_flow = new JSONObject();
        query_flow.put("task_id",taskId);
        JSONObject result = flowDao.pm_task_instance_query(query_flow,NRMConstants.SHARDING_GRAPH_DB).getJSONObject("result");
        return result;
    }

    @PostMapping("/queryFlowTask/{processInstanceId}")
    public List<JSONObject> flow_task_query(@PathVariable(name="processInstanceId") String processInstanceId) throws IOException {
        JSONObject query_flow = new JSONObject();
        query_flow.put("instance_id",processInstanceId);
        //List<JSONObject> result = flowDao.pm_flow_instance_task_query(query_flow,NRMConstants.SHARDING_GRAPH_DB).getJSONArray("result");
        List<JSONObject> result = flowService.flow_task_query(query_flow);
        return result;
    }

    public List<JSONObject> getResByFlow(JSONObject jsonObject){
        JSONObject resource = flowService.pm_flow_resource_query(jsonObject);
        List<JSONObject> result = new ArrayList<>();
        if(resource.getString("result").startsWith("[")){
            result= resource.getJSONArray("result");
        }else{
            if(!ObjectUtil.isEmpty(resource.getJSONObject("result"))){
                JSONArray current_resource = new JSONArray();
                current_resource.add(resource.getJSONObject("result"));
                result = current_resource;
            }
        }
        return result;
    }

    @PostMapping("/delete_res")
    public Integer deleteResource(@RequestBody JSONObject jsonObject) throws IOException {
        Integer result = flowService.pm_resource_process_delete(jsonObject);
        return result;
    }

    @PostMapping("/res_save/{processInstanceId}")
    public void upload_res(@PathVariable(name="processInstanceId") String processInstanceId,@RequestParam("file") MultipartFile file) throws IOException {
        JSONObject res_flow = new JSONObject();
        res_flow.put("instance_id",processInstanceId);
        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("area",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==1 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("code",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==2 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("type",sheet.getRow(i).getCell(j).getStringCellValue());
                    }
                    JSONObject save_result = flowService.pm_resource_process_insert(current_data);
                    if(save_result.getString("result").equals("1")){
                        res_flow.put("code",current_data.getString("code"));
                        flowService.pm_rela_flow_resource_insert(res_flow);
                    }
                }
            }
        }
    }

    @PostMapping("/res_save_opt/{processInstanceId}")
    public void res_save_opt(@PathVariable(name="processInstanceId") String processInstanceId,@RequestBody List<JSONObject> jsonObject) throws IOException {
        JSONObject res_flow = new JSONObject();
        res_flow.put("instance_id",processInstanceId);
        for(JSONObject res : jsonObject){
            res.put("area", res.getString("areaName"));
            res.put("rela_id", res.getString("id"));
            res.put("type", "optGroup");
            res.remove("id");
            JSONObject save_result = flowService.pm_resource_process_insert(res);
            if(save_result.getString("result").equals("1")){
                res_flow.put("code",res.getString("code"));
                flowService.pm_rela_flow_resource_insert(res_flow);
            }
        }
    }
}
