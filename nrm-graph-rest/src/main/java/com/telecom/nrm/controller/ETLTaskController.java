package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.etl.ETLTask;
import com.telecom.nrm.domain.etl.ETLTaskStatus;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphResponse;
import com.telecom.nrm.service.ETLTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/etl-task")
@Slf4j
public class ETLTaskController {



    @Autowired
    ETLTaskService etlTaskService;

    @PostMapping("/submit")
    public ResponseEntity<ETLTaskStatus> submit(@RequestBody ETLTask etlTask) {
        ETLTaskStatus etlTaskStatus = etlTaskService.submitTask(etlTask);
        return ResponseEntity.ok(etlTaskStatus);
    }

    @PostMapping("/status")
    public ResponseEntity<ETLTaskStatus> status(@RequestBody ETLTask etlTask) {
        ETLTaskStatus etlTaskStatus = etlTaskService.status(etlTask);
        return ResponseEntity.ok(etlTaskStatus);
    }
}
