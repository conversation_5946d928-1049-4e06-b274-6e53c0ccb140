package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.annotation.DaAPI;
import com.telecom.da.client.annotation.DaParam;
import com.telecom.da.client.annotation.DaShardingCode;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.dao.InfluenceAnalysisDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import com.telecom.nrm.dto.ExportResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import org.springframework.http.MediaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;

import org.apache.poi.ss.usermodel.*;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/accs_nbr_no")
@Slf4j
public class GroupFaultRapidPositioningController {


    @Autowired
    GroupFaultRapidPositioningDao groupFaultRapidPositioningDao;

    @Value("${zd.url}")
    String zd_url;

    @Autowired
    PONService ponService;

    @Autowired
    ObsLocationService obsLocationService;

    @Autowired
    QueryCfsEffectService queryCfsEffectService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @Autowired
    DocumentExportService documentExportService;
    @Autowired
    InfluenceAnalysisDao influenceAnalysisDao;

    ExecutorService executor = Executors.newFixedThreadPool(4);


    private WebClient webClient = WebClient.builder()
            .baseUrl("https://cnioc.telecomjs.com:18080/serv/fault-pre/attemper/job/manualApi")
            .build();


//
//    private String zd_url = "http://nrm.oss.telecomjs.com:39049/zdintf-prod/ida/services/AsigService";


    @PostMapping("groupfaultlog")
    public int groupfaultlog(@RequestBody JSONObject request) {


        log.info("request" + request);
        int loginsert = groupFaultRapidPositioningDao.tb_groupfault_log_insert(request, "ds_graph_js");

        return loginsert;

    }


    @PostMapping("groupfaultv2")
    @LogAnnotation( interfaceName="障碍定位获取管道坐标")
    public ResponseEntity<JSONObject> pageQueryFromOltToBaseSect(@RequestBody JSONObject request) {


//
//        //生成群障查询记录
//        @DaAPI(apiCode = "tb_groupfault_log_insert", version = "V20240410113746988", returnTypes = {Integer.class})
//        public Integer tb_groupfault_log_insert(@DaParam JSONObject param, @DaShardingCode String shardingCode);
//tb_groupfault_log_insert


        System.out.println("@@@@@" + request);

        JSONObject param = new JSONObject();

        if (ObjectUtil.isNotEmpty(request.getString("accs_nbr_nos"))) {
            param.put("accs_nbr_nos", request.getJSONArray("accs_nbr_nos"));

        }
        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 10000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;
        String sharding_code = "ds_odso_" + request.getString("city");

        PageResponse<JSONObject> response = groupFaultRapidPositioningDao.pageQueryFromOltToBaseSect(param, pageSize, currentPage, sharding_code);
        List<JSONObject> data = response.getData();

        JSONObject result = new JSONObject();
        result.put("bse_sect_list", data);


        return ResponseEntity.ok(result);
    }


    @PostMapping("bseeqptobsesect")
    @LogAnnotation( interfaceName="障碍定位从设备得到管道")
    public ResponseEntity<JSONObject> pageQueryBseEqptoBseSect(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();
        JSONObject result = new JSONObject();

        if (ObjectUtil.isNotEmpty(request.getString("bse_eqp_ids"))) {
            JSONArray bse_eqp_ids = request.getJSONArray("bse_eqp_ids");
            if (bse_eqp_ids.size() > 0) {

                param.put("bse_eqp_ids", bse_eqp_ids);

                int pageSize;
                int currentPage;
                //int pageSize = request.getInteger("pageSize");
                pageSize = 10000;
                //int currentPage = request.getInteger("currentPage");
                currentPage = 1;
                String sharding_code = "ds_odso_" + request.getString("city");

                PageResponse<JSONObject> response = groupFaultRapidPositioningDao.pageQueryBseEqptoBseSect(param, pageSize, currentPage, sharding_code);
                List<JSONObject> data = response.getData();

                result.put("bse_sect_list", data);


            }


        }


        return ResponseEntity.ok(result);
    }


    @PostMapping("groupfault")
    @LogAnnotation( interfaceName="障碍定groupfault接口")
    public ResponseEntity<JSONObject> pageQueryOltObd(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();

        if (ObjectUtil.isNotEmpty(request.getString("accs_nbr_nos"))) {
//            param.put("accs_nbr_nos", Arrays.asList(request.getString("accs_nbr_nos")));
            param.put("accs_nbr_nos", request.getJSONArray("accs_nbr_nos"));

        }

        log.info("检查查询的接入号清单是啥" + param.getString("accs_nbr_nos"));
        int pageSize;
        int currentPage;
        //int pageSize = request.getInteger("pageSize");
        pageSize = 1000;
        //int currentPage = request.getInteger("currentPage");
        currentPage = 1;
        String sharding_code = "ds_odso_" + request.getString("city");

        PageResponse<JSONObject> response = groupFaultRapidPositioningDao.pageQueryOltObd(param, pageSize, currentPage, sharding_code);
        List<JSONObject> data = response.getData();

        Map<String, List<JSONObject>> oltMap = data.stream()
                .collect(Collectors.groupingBy(d -> d.getString("olt_eqp_id")));
        JSONObject result = new JSONObject();
        List oltList;
        if (ObjectUtil.isNotEmpty(oltMap)) {
            oltList = new ArrayList();
            for (String key : oltMap.keySet()) {
                JSONObject olt = new JSONObject();
                //父表格
                olt.put("olt_id", key);
                olt.put("accs_nbr_nos", oltMap.get(key).stream()
                        .map(jsonObject -> jsonObject.getString("accs_nbr_no"))
                        .collect(Collectors.toList()));
                olt.put("olt_no", oltMap.get(key).get(0).getString("olt_no"));
                olt.put("olt_eqp_id", oltMap.get(key).get(0).getString("olt_eqp_id"));
                olt.put("olt_name", oltMap.get(key).get(0).getString("olt_name"));
                olt.put("olt_ip", oltMap.get(key).get(0).getString("olt_ip"));
                olt.put("olt_instl_addr_desc", oltMap.get(key).get(0).getString("olt_instl_addr_desc"));
                olt.put("olt_room_id", oltMap.get(key).get(0).getString("olt_room_id"));
                olt.put("posx", oltMap.get(key).get(0).getString("olt_posx"));
                olt.put("posy", oltMap.get(key).get(0).getString("olt_posy"));
                olt.put("room_name", oltMap.get(key).get(0).getString("room_name"));
                olt.put("station_name", oltMap.get(key).get(0).getString("station_name"));
                olt.put("pon_code", oltMap.get(key).get(0).getString("pon_code"));
                olt.put("card_name", oltMap.get(key).get(0).getString("card_name"));
                olt.put("equipment_spec_id", 1028200001);
                olt.put("count_olt_accs_nbr_no", oltMap.get(key).size());
                //子表格
                olt.put("olt_subtable", oltMap.get(key));
//                //这个olt对应的接入号的清单，留着以后导出可能用到
//                olt.put("access_no_list", oltMap.get(key).stream()
//                        .map(jsonObject -> jsonObject.getString("accs_nbr_no"))
//                        .collect(Collectors.toList())  );
                oltList.add(olt);
            }

            result.put("oltList", oltList);

        }

        Map<String, List<JSONObject>> frst_obd_Map = data.stream()
                .collect(Collectors.groupingBy(d -> d.getString("frst_obd_phy_eqp_id")));


        List frstobdList;
        if (ObjectUtil.isNotEmpty(frst_obd_Map)) {
            frstobdList = new ArrayList();
            for (String key : frst_obd_Map.keySet()) {
                JSONObject frstobd = new JSONObject();
                //父表格
                frstobd.put("obd_id", key);
                frstobd.put("access_no_list", frst_obd_Map.get(key).stream()
                        .map(jsonObject -> jsonObject.getString("accs_nbr_no"))
                        .collect(Collectors.toList()));
                frstobd.put("frst_obd_phy_port_id", frst_obd_Map.get(key).get(0).getString("frst_obd_phy_port_id"));
                frstobd.put("frst_obd_name", frst_obd_Map.get(key).get(0).getString("frst_obd_name"));
                frstobd.put("frst_obd_no", frst_obd_Map.get(key).get(0).getString("frst_obd_no"));
                frstobd.put("frst_obd_phy_port_no", frst_obd_Map.get(key).get(0).getString("frst_obd_phy_port_no"));
                frstobd.put("count_frstobd_accs_nbr_no", frst_obd_Map.get(key).size());
                frstobd.put("posx", frst_obd_Map.get(key).get(0).getString("frst_obd_posx"));
                frstobd.put("posy", frst_obd_Map.get(key).get(0).getString("frst_obd_posy"));
                frstobd.put("equipment_spec_id", 1020200006);

                //子表格
                frstobd.put("frstobd_subtable", frst_obd_Map.get(key));
                //
                frstobdList.add(frstobd);
            }
            result.put("frstobdList", frstobdList);
        }


        Map<String, List<JSONObject>> scnd_obd_Map = data.stream().filter(d -> ObjectUtil.isNotEmpty(d.getString("scnd_obd_phy_eqp_id")))
                .collect(Collectors.groupingBy(d -> d.getString("scnd_obd_phy_eqp_id")));
        List scndobdList;
        if (ObjectUtil.isNotEmpty(scnd_obd_Map)) {
            scndobdList = new ArrayList();
            for (String key : scnd_obd_Map.keySet()) {
                JSONObject scndobd = new JSONObject();
                //父表格
                scndobd.put("obd_id", key);
                scndobd.put("access_no_list", scnd_obd_Map.get(key).stream()
                        .map(jsonObject -> jsonObject.getString("accs_nbr_no"))
                        .collect(Collectors.toList()));
                scndobd.put("scnd_obd_phy_port_id", scnd_obd_Map.get(key).get(0).getString("scnd_obd_phy_port_id"));
                scndobd.put("scnd_obd_name", scnd_obd_Map.get(key).get(0).getString("scnd_obd_name"));
                scndobd.put("scnd_obd_no", scnd_obd_Map.get(key).get(0).getString("scnd_obd_no"));
                scndobd.put("scnd_obd_phy_port_no", scnd_obd_Map.get(key).get(0).getString("scnd_obd_phy_port_no"));
                scndobd.put("posx", scnd_obd_Map.get(key).get(0).getString("scnd_obd_posx"));
                scndobd.put("posy", scnd_obd_Map.get(key).get(0).getString("scnd_obd_posy"));
                scndobd.put("count_scndobd_accs_nbr_no", scnd_obd_Map.get(key).size());
                scndobd.put("equipment_spec_id", 1020200006);

                //子表格
                scndobd.put("scndobd_subtable", scnd_obd_Map.get(key));
                //
                scndobdList.add(scndobd);
            }
            result.put("scndobdList", scndobdList);
        }


        return ResponseEntity.ok(result);


    }


    @PostMapping("obs_location")
    @LogAnnotation( interfaceName="障碍定obs_location接口")
    public JSONObject queryObstaclepoints(@RequestBody JSONObject request) throws URISyntaxException {
//       String sharingCode = "ds_bc_o3_";
        JSONArray accessCodes = request.getJSONArray("access_code");
        String city = request.getString("city");
        Boolean isGroup = request.getBoolean("isGroup");
        String sharingCode = "ds_bc_o3_" + city.toLowerCase();
        if (ObjectUtil.isEmpty(accessCodes)) {
            return new JSONObject();
        }
        JSONObject jsonObject = obsLocationService.queryObstaclepoints(JSONObjectUtil.jsonStringArrayToList(accessCodes), sharingCode, isGroup, city);
        return jsonObject;
    }

    @PostMapping("obs_location_v2")
    @LogAnnotation( interfaceName="障碍定obs_locationv2接口")
    public JSONObject queryObstaclepointsV2(@RequestBody JSONObject request) throws URISyntaxException {
//       String sharingCode = "ds_bc_o3_";
        JSONArray accessCodes = request.getJSONArray("access_code");
        String city = request.getString("city");
        Boolean isGroup = request.getBoolean("isGroup");
        String sharingCode = "ds_bc_o3_" + city.toLowerCase();
        if (ObjectUtil.isEmpty(accessCodes)) {
            return new JSONObject();
        }
        JSONObject jsonObject = obsLocationService.queryObstaclepointsV2(JSONObjectUtil.jsonStringArrayToList(accessCodes), sharingCode, isGroup);


        return jsonObject;
    }


    @PostMapping("pon-resource-tree")
    @LogAnnotation( interfaceName="障碍定位pon-resource-tree接口")
    public ResponseEntity<JSONObject> getPONResourceTree(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();

        if (ObjectUtil.isNotEmpty(request.getString("accs_nbr_nos"))) {
            param.put("accessCodes", request.getString("accs_nbr_nos"));
        }
        if (ObjectUtil.isNotEmpty(request.getString("city"))) {
            param.put("areaCode", request.getString("city"));
        }

        JSONObject result = ponService.getResourceTree(param);


        return ResponseEntity.ok(result);
    }

    @PostMapping("pon-cable-segment-tree")
    @LogAnnotation( interfaceName="障碍定位pon-cable-segment-tree接口")
    public ResponseEntity<JSONObject> getPONCableSegmentTree(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();

        if (ObjectUtil.isNotEmpty(request.getString("accs_nbr_nos"))) {
            param.put("accessCodes", request.getJSONArray("accs_nbr_nos"));
        }
        if (ObjectUtil.isNotEmpty(request.getString("city"))) {
            param.put("areaCode", request.getString("city"));
        }

        JSONObject result = ponService.getCableSegmentTree(param);


        return ResponseEntity.ok(result);
    }

    @PostMapping("getcblandfacility")
    @LogAnnotation( interfaceName="障碍定位getcblandfacility接口")
    public JSONObject getcblandfacility(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();
        if (ObjectUtil.isNotEmpty(request.getString("phy_port_ids"))) {
            param.put("phy_port_ids", Arrays.asList(request.getString("phy_port_ids").split(",")));
        }
        String sharding_code = "ds_odso_" + request.getString("city");
        JSONObject response = groupFaultRapidPositioningDao.getSectByPhyPort(param, sharding_code);


        return response;

    }

    @PostMapping("getaccesscode")
    @LogAnnotation( interfaceName="障碍定位getaccesscode接口-通过光缆段获得全部接入号")
    public JSONArray getAllAccCodesByFiberCableSegments(@RequestBody JSONObject request) {
        JSONObject param = new JSONObject();
        int currentPage = 1;
        int pageSize = 100;
        ArrayList<String> segments = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(request.getString("segments"))) {
            log.info("segments");
            List<String> tem = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("segments"));
            if (ObjectUtil.isEmpty(tem)) {
                return new JSONArray();
            }
            segments.addAll(tem);
        }
        if (ObjectUtil.isNotEmpty(request.getString("current_page"))) {
            currentPage = Integer.parseInt(request.getString("current_page"));
        }
        if (ObjectUtil.isNotEmpty(request.getString("page_size"))) {
            pageSize = Integer.parseInt(request.getString("page_size"));
        }
        String sharding_code = "ds_odso_" + request.getString("city");
        PageResponse<JSONObject> response = new PageResponse<>();
        JSONArray res = new JSONArray();
        for (int i = 0; i < segments.size(); i++) {
            Page pageInfo = new Page();
            pageInfo.setCurrentPage(currentPage);
            pageInfo.setPageSize(pageSize);
            param.put("segment", segments.get(i));
            response = groupFaultRapidPositioningDao.getAllAccCodesByFiberCableSegments(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), sharding_code);
            JSONArray accs = new JSONArray();
            JSONObject curAcc = new JSONObject();
            pageInfo = response.getPageInfo();
            for (int k = currentPage; k <= response.getPageInfo().getTotalPage(); k++) {
                pageInfo.setCurrentPage(k);
                response.setPageInfo(pageInfo);
                PageResponse<JSONObject> nextRes = groupFaultRapidPositioningDao.getAllAccCodesByFiberCableSegments(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), sharding_code);
                for (int j = 0; j < nextRes.getData().size(); j++) {
                    JSONObject jsonObject = nextRes.getData().get(j);
                    if (j == 0 && ObjectUtil.isEmpty(curAcc.getString("cbl_sect_id"))) {
                        curAcc.put("cbl_sect_id", jsonObject.getString("cbl_sect_id"));
                        curAcc.put("cbl_sect_no", jsonObject.getString("cbl_sect_no"));
                        curAcc.put("cbl_sect_name", jsonObject.getString("cbl_sect_name"));
                    }
                    accs.add(jsonObject.getString("accs_nbr_no"));
                }
            }
            curAcc.put("accs_nbr_no", accs);
            curAcc.put("accs_nbr_no_count", accs.size());
            res.add(curAcc);
        }
        return res;
    }


    @PostMapping("downloadaccesscode")
    @LogAnnotation( interfaceName="障碍定位downloadaccesscode接口-下载接入号清单")

    public void download(@RequestBody JSONObject request, HttpServletResponse response) throws IOException {
        log.info("request", request);

        JSONArray res = getAllAccCodesByFiberCableSegments(request);

        // 填充数据
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头      ['cbl_sect_id', 'cbl_sect_name', 'cbl_sect_no','accs_nbr_no','accs_nbr_no_count']
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("光缆段ID");
        header.createCell(2).setCellValue("光缆段名称");
        header.createCell(3).setCellValue("光缆段编号");
        header.createCell(4).setCellValue("接入号清单");
        header.createCell(5).setCellValue("接入号个数");

        log.info("result" + res);


        // 插入数据
        int rowIndex = 1;
        for (int i = 0; i < res.size(); i++) {

            List<String> accs_nbr_noList = res.getJSONObject(i).getObject("accs_nbr_no", List.class);
            log.info("accs_nbr_noList" + accs_nbr_noList);
            log.info("accs_nbr_noList" + res.getJSONObject(i));


            for (String accNumber : accs_nbr_noList) {
                // 在这里处理每个accNumber
                log.info("accNumber" + accNumber);

                XSSFRow row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(request.getString("city"));
                row.createCell(1).setCellValue(res.getJSONObject(i).getString("cbl_sect_id"));
                row.createCell(2).setCellValue(res.getJSONObject(i).getString("cbl_sect_name"));
                row.createCell(3).setCellValue(res.getJSONObject(i).getString("cbl_sect_no"));
                row.createCell(4).setCellValue(accNumber);
                row.createCell(5).setCellValue(res.getJSONObject(i).getString("accs_nbr_no_count"));
            }
        }

        // 设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }


    @PostMapping("pipe_effect")
    @LogAnnotation( interfaceName="障碍定位 pipe_effect 接口-管道影响分析")

    public ResponseEntity<JSONObject> pipeEffect(@RequestBody JSONObject request) {
        String city = request.getString("city");
        List<String> codes = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("codes"));

        JSONObject response = queryCfsEffectService.findPipeEffect(codes, city);

        return ResponseEntity.ok(response);
    }

    @PostMapping("cbl_sect_effect")
    public ResponseEntity<JSONObject> findCblSectEffect(@RequestBody JSONObject request) {
        String city = request.getString("city");
        List<String> codes = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("codes"));

        JSONObject response = queryCfsEffectService.findCblSectEffect(codes, city);

        return ResponseEntity.ok(response);
    }



    @PostMapping("download_cbl_sect_effect")
    @LogAnnotation( interfaceName="障碍定位 download_cbl_sect_effect 接口-下载光缆影响接入号")
    public ResponseEntity<ExportResponseDTO> download_cbl_sect_effect(@RequestBody JSONObject request) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [光缆段导出] 接收到光缆段影响接入号清单导出请求");
            log.info("📋 [光缆段导出] 请求参数: {}", request);

            // 参数验证
            if (request == null || request.isEmpty()) {
                log.error("❌ [光缆段导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            String city = request.getString("city");
            JSONArray codes = request.getJSONArray("codes");
            log.info("📍 [光缆段导出] 查询地市: {}", city);
            log.info("📊 [光缆段导出] 光缆段编码数量: {}", codes != null ? codes.size() : 0);

            // 1. 查询光缆段影响数据
            log.info("🔍 [光缆段导出] 步骤1: 开始查询光缆段影响数据");
            long queryStartTime = System.currentTimeMillis();
            ResponseEntity<JSONObject> res = findCblSectEffect(request);
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [光缆段导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            JSONObject responseData = res.getBody();
            if (responseData == null) {
                log.error("❌ [光缆段导出] 查询返回数据为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("查询数据失败，返回结果为空"));
            }

            log.info("📊 [光缆段导出] 查询响应数据大小: {} 字符", responseData.toString().length());

            // 2. 转换数据格式
            log.info("🔄 [光缆段导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertCableSectionEffectData(responseData, city);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [光缆段导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [光缆段导出] 转换后数据量: {} 条记录", exportData.size());

            if (exportData.isEmpty()) {
                log.warn("⚠️ [光缆段导出] 转换后数据为空，可能没有影响的接入号");
                return ResponseEntity.ok(ExportResponseDTO.failure("没有查询到影响的接入号数据"));
            }

            // 3. 定义列
            log.info("📝 [光缆段导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "地市:city",
                "光缆段编码:cable_segment_code",
                "光路编码:route_code",
                "纤芯号:line_no",
                "接入号:access_code",
                "客户名称:customer_name",
                "客户等级:customer_level",
                "影响业务类型:service_type",
                "IP地址:ip_addr",
                "PON口编码:pon_code",
                "客户经理:customer_manager",
                "客户经理电话:customer_manager_phone",
                "客户视图名称:scene_name"
            );
            log.info("📋 [光缆段导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [光缆段导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO result = documentExportService.exportToDocumentSecurity(
                exportData, columns, "光缆段影响接入号清单", "光缆段影响接入号清单",
                "网络分析", "/api/accs_nbr_no/download_cbl_sect_effect", "光缆段影响接入号清单导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [光缆段导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (result.isSuccess()) {
                result.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [光缆段导出] 导出成功完成!");
                log.info("📁 [光缆段导出] 文件名: {}", result.getFileName());
                log.info("📊 [光缆段导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [光缆段导出] 总耗时: {} ms", totalTime);
                log.info("📈 [光缆段导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [光缆段导出] 导出失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [光缆段导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [光缆段导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    /**
     * 转换光缆段影响数据格式
     */
    private List<Map<String, Object>> convertCableSectionEffectData(JSONObject responseData, String city) {
        log.info("🔄 [数据转换] 开始转换光缆段影响数据格式");
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            JSONArray cableSegmentList = responseData.getJSONArray("cblSegmentList");
            log.info("📊 [数据转换] 光缆段列表数量: {}", cableSegmentList != null ? cableSegmentList.size() : 0);

            if (ObjectUtil.isNotEmpty(cableSegmentList)) {
                int totalCfsCount = 0;
                int processedCableSegments = 0;

                for (int j = 0; j < cableSegmentList.size(); j++) {
                    try {
                        JSONObject cableSegment = cableSegmentList.getJSONObject(j);
                        String cableSegmentCode = cableSegment.getString("code");
                        JSONArray cfsList = cableSegment.getJSONArray("cfs_list");

                        log.debug("🔍 [数据转换] 处理光缆段 {}: {}, CFS数量: {}",
                            (j + 1), cableSegmentCode, cfsList != null ? cfsList.size() : 0);

                        if (ObjectUtil.isNotEmpty(cfsList)) {
                            for (int l = 0; l < cfsList.size(); l++) {
                                try {
                                    JSONObject cfs = cfsList.getJSONObject(l);

                                    Map<String, Object> row = new HashMap<>();
                                    row.put("city", city != null ? city : "");
                                    row.put("cable_segment_code", cableSegmentCode != null ? cableSegmentCode : "");
                                    row.put("route_code", cfs.getString("route_code") != null ? cfs.getString("route_code") : "");
                                    row.put("line_no", cfs.getString("line_no") != null ? cfs.getString("line_no") : "");
                                    row.put("access_code", cfs.getString("access_code") != null ? cfs.getString("access_code") : "");
                                    row.put("customer_name", cfs.getString("cust_name") != null ? cfs.getString("cust_name") : "");
                                    row.put("customer_level", cfs.getString("customer_level_name") != null ? cfs.getString("customer_level_name") : "");
                                    row.put("service_type", cfs.getString("service_spec_name") != null ? cfs.getString("service_spec_name") : "");
                                    row.put("ip_addr", cfs.getString("ip_addr") != null ? cfs.getString("ip_addr") : "");
                                    row.put("pon_code", cfs.getString("pon_code") != null ? cfs.getString("pon_code") : "");
                                    row.put("customer_manager", cfs.getString("khjl_name") != null ? cfs.getString("khjl_name") : "");
                                    row.put("customer_manager_phone", cfs.getString("khjl_phone") != null ? cfs.getString("khjl_phone") : "");
                                    row.put("scene_name", cfs.getString("scene_name") != null ? cfs.getString("scene_name") : "");

                                    data.add(row);
                                    totalCfsCount++;

                                } catch (Exception e) {
                                    log.error("❌ [数据转换] 转换CFS记录时出错，光缆段: {}, CFS索引: {}, 错误: {}",
                                        cableSegmentCode, l, e.getMessage());
                                }
                            }
                        } else {
                            log.debug("⚠️ [数据转换] 光缆段 {} 没有CFS数据", cableSegmentCode);
                        }

                        processedCableSegments++;

                        // 每处理10个光缆段输出一次进度
                        if (processedCableSegments % 10 == 0) {
                            log.info("📊 [数据转换] 已处理 {} / {} 个光缆段，累计CFS记录: {}",
                                processedCableSegments, cableSegmentList.size(), totalCfsCount);
                        }

                    } catch (Exception e) {
                        log.error("❌ [数据转换] 转换光缆段时出错，索引: {}, 错误: {}", j, e.getMessage());
                    }
                }

                log.info("✅ [数据转换] 转换完成");
                log.info("📊 [数据转换] 处理光缆段数量: {}", processedCableSegments);
                log.info("📊 [数据转换] 总CFS记录数: {}", totalCfsCount);

            } else {
                log.warn("⚠️ [数据转换] 光缆段列表为空");
            }

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }


    @PostMapping("download_pipe_effect")
    @LogAnnotation( interfaceName="障碍定位 download_pipe_effect 接口-下载管道影响分析")

    public void download_pipe_effect(@RequestBody JSONObject request, HttpServletResponse response) throws IOException {
        ResponseEntity<JSONObject> res = pipeEffect(request);

        String city = request.getString("city");

        // 填充数据
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("影响用户清单");

        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("支撑段编码");
        header.createCell(2).setCellValue("光缆段编码");
        header.createCell(3).setCellValue("光路编码");
        header.createCell(4).setCellValue("纤芯号");
        header.createCell(5).setCellValue("接入号");
        header.createCell(6).setCellValue("客户名称");
        header.createCell(7).setCellValue("客户等级");
        header.createCell(8).setCellValue("影响业务类型");
        header.createCell(9).setCellValue("ip地址");
        header.createCell(10).setCellValue("pon口编码");
        header.createCell(11).setCellValue("客户经理");
        header.createCell(12).setCellValue("客户经理电话");
        header.createCell(13).setCellValue("客户视图名称");




        int rowIndex = 1;

        JSONObject jsonObject = new JSONObject(res.getBody());
        JSONArray pipeSegmentList = jsonObject.getJSONArray("pipeSegmentList");

        for (int i = 0; i < pipeSegmentList.size(); i++) {
            JSONObject pipeSegment = pipeSegmentList.getJSONObject(i);
            String pipeSegmentCode = pipeSegment.getString("code");
            JSONArray cableSegmentList = pipeSegment.getJSONArray("cable_segment_list");
            if (ObjectUtil.isNotEmpty(cableSegmentList)) {

                for (int j = 0; j < cableSegmentList.size(); j++) {
                    JSONObject cableSegment = cableSegmentList.getJSONObject(j);
                    String cableSegmentCode = cableSegment.getString("code");
                    JSONArray cfsList = cableSegment.getJSONArray("cfs_list");
                    if (ObjectUtil.isNotEmpty(cfsList)) {
                        for (int l = 0; l < cfsList.size(); l++) {
                            JSONObject cfs = cfsList.getJSONObject(l);

                            System.out.println("cfs"+cfs);
                            String line_no = cfs.getString("line_no");
                            String cfsName = cfs.getString("service_name");
                            String cfsSpec = cfs.getString("service_spec_name");
                            String cfsAccsCode = cfs.getString("access_code");
                            String optRoadCode = cfs.getString("route_code");


                            String cust_name = cfs.getString("cust_name");
                            String customer_level_name  = cfs.getString("customer_level_name");


//                            header.createCell(0).setCellValue("地市");
//                            header.createCell(1).setCellValue("支撑段编码");
//                            header.createCell(2).setCellValue("光缆段编码");
//                            header.createCell(3).setCellValue("光路编码");
//                            header.createCell(4).setCellValue("纤芯号");
//                            header.createCell(5).setCellValue("接入号");
//                            header.createCell(7).setCellValue("影响业务名称");
//                            header.createCell(8).setCellValue("政企客户名称");
//                            header.createCell(9).setCellValue("客户等级");
//                            header.createCell(10).setCellValue("影响业务类型");
//                            header.createCell(11).setCellValue("ip地址");
//                            header.createCell(12).setCellValue("pon口编码");
//                            header.createCell(13).setCellValue("客户经理");
//                            header.createCell(14).setCellValue("客户经理电话");
//                            header.createCell(15).setCellValue("是否要客");

                            XSSFRow row = sheet.createRow(rowIndex++);
                            row.createCell(0).setCellValue(city);
                            row.createCell(1).setCellValue(pipeSegmentCode);
                            row.createCell(2).setCellValue(cableSegmentCode);
                            row.createCell(3).setCellValue(optRoadCode);
                            row.createCell(4).setCellValue(line_no);
                            row.createCell(5).setCellValue(cfsAccsCode);
                            row.createCell(6).setCellValue(cust_name);
                            row.createCell(7).setCellValue(customer_level_name);
                            row.createCell(8).setCellValue(cfsSpec);
                            row.createCell(9).setCellValue(cfs.getString("ip_addr"));
                            row.createCell(10).setCellValue(cfs.getString("pon_code"));
                            row.createCell(11).setCellValue(cfs.getString("khjl_name"));
                            row.createCell(12).setCellValue(cfs.getString("khjl_phone"));
                            row.createCell(13).setCellValue(cfs.getString("scene_name"));
                        }
                    }

                }
            }
        }

        // 设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");
        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

//
//    public JSONArray checkpon1234 (@RequestBody JSONObject request) {
//        JSONObject param = new JSONObject();
//        int currentPage = 1;
//        int pageSize = 1000;
//        ArrayList<String> segments = new ArrayList<>();
//        if(ObjectUtil.isNotEmpty(request.getString("segments")))
//        {
//            log.info("segments");
//            List<String> tem = request.getJSONArray("segments");
//            if (ObjectUtil.isEmpty(tem)) {
//                return new JSONArray();
//            }
//            segments.addAll(tem);
//        }
//        if(ObjectUtil.isNotEmpty(request.getString("current_page")))
//        {
//            currentPage = Integer.parseInt(request.getString("current_page"));
//        }
//        if(ObjectUtil.isNotEmpty(request.getString("page_size")))
//        {
//            pageSize = Integer.parseInt(request.getString("page_size"));
//        }
//        String sharding_code = "ds_odso_"+ request.getString("city");
//        String shardingCode = "ds_bc_o3_"+ request.getString("city");
//
//
//        PageResponse<JSONObject> response = new PageResponse<>();
//        JSONArray res = new JSONArray();
//        for (int i = 0; i < segments.size(); i++) {
//            Page pageInfo = new Page();
//            pageInfo.setCurrentPage(currentPage);
//            pageInfo.setPageSize(pageSize);
//            param.put("segment", segments.get(i));
//            response = groupFaultRapidPositioningDao.getAllAccCodesByFiberCableSegments(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), sharding_code);
//            JSONArray accs = new JSONArray();
//            JSONObject curAcc = new JSONObject();
//            pageInfo = response.getPageInfo();
//            for (int k = currentPage; k <= response.getPageInfo().getTotalPage(); k++) {
//                pageInfo.setCurrentPage(k);
//                response.setPageInfo(pageInfo);
//                PageResponse<JSONObject> nextRes = groupFaultRapidPositioningDao.getAllAccCodesByFiberCableSegments(param, pageInfo.getPageSize(), pageInfo.getCurrentPage(), sharding_code);
//                for (int j = 0; j < nextRes.getData().size(); j++) {
//                    JSONObject jsonObject = nextRes.getData().get(j);
//                    if (j == 0 && ObjectUtil.isEmpty(curAcc.getString("cbl_sect_id"))) {
//                        curAcc.put("cbl_sect_id", jsonObject.getString("cbl_sect_id"));
//                        curAcc.put("cbl_sect_no", jsonObject.getString("cbl_sect_no"));
//                        curAcc.put("cbl_sect_name", jsonObject.getString("cbl_sect_name"));
//                    }
//                    accs.add(jsonObject.getString("accs_nbr_no"));
//                }
//            }
//            curAcc.put("accs_nbr_no", accs);
//            curAcc.put("accs_nbr_no_count", accs.size());
//            res.add(curAcc);
//
//            //通过accs获取到全部的pon口
//           // query_cfs_2_pon
//
//
//            //调用pon口检测
//
//            GraphRequestBuilder builder = new GraphRequestBuilder();
//
//            GraphRequest graphRequest = builder.setApiId("query_cfs_2_pon").setShardingCode(shardingCode)
//                    .appendWhereBodyItem(new GraphWhereBodyItem("cfs","access_code_s",accs)).build();
//            Graph graph = graphApiService.doApi(graphRequest);
//            JSONObject data = graphService.getData(graph);
//
//
//
//        }
//        //下面开始调用检测
//        return res;
//    }


    //查找对应的网管IP
    @PostMapping("querynetip")
    @LogAnnotation( interfaceName="障碍定位 querynetip 接口-查询对应的网管IP")

    public JSONObject pageQueryPONManageIP(@RequestBody JSONObject request) {
        JSONObject result = new JSONObject();
        JSONArray poninfo = request.getJSONArray("poninfo");

        System.out.println("@@@@@@@@@request" + request);
        JSONObject ipparam = new JSONObject();
        List<String> ipList = new ArrayList<>();

        for (int i = 0; i < poninfo.size(); i++) {
            JSONObject ponInfoObject = poninfo.getJSONObject(i);
            String ip = ponInfoObject.getString("ip");
            ipList.add(ip);
        }

        ipparam.put("loopback_ips", ipList);
        String city = request.getString("city");
        System.out.println("@@@@@@@@@ipparam" + ipparam);


        PageResponse<JSONObject> response = groupFaultRapidPositioningDao.pageQueryPONManageIP(ipparam, 1000, 1, "ds_odso_"+city);
        JSONObjectUtil.convertBigNumberToString(response.getData());


        List resultArray = response.getData();



        System.out.println("resultArray" + resultArray.toString());

        result.put("ipmap", resultArray);

        return result;
    }


    //传的入参是网管IP的时候，直接查询IPOSS接口，不需要进行IP转换，也不需要关联产品
    @PostMapping("checkponbynetip")
    @LogAnnotation( interfaceName="障碍定位 checkponbynetip 接口-通过网管接口，进行pon口检测")

    public JSONObject checkponbynetip(@RequestBody JSONObject request) {

        List<JSONObject> ipmap = JSONObjectUtil.jsonObjectArrayToList(request.getJSONArray("ipmap"));
        JSONObject result = new JSONObject();

        ArrayList<FutureTask<JSONObject>> FutureTaskList = new ArrayList<FutureTask<JSONObject>>();
        if (ObjectUtil.isNotEmpty(ipmap)) {

            JSONArray resultArray = new JSONArray();

            log.info("transferedPoninfo" + ipmap);

            //FutureTask
            Map<FutureTask, Integer> ponfutureTaskMap = new HashMap<>();
            for (int i = 0; i < ipmap.size(); i++) {
                JSONObject currentPoninfo = ipmap.get(i);
                String ip_address = currentPoninfo.getString("loopback_ip_2nd");
                String pon_code = currentPoninfo.getString("pon_code");


                if (ObjectUtil.isNotEmpty(pon_code)) {
                    String[] pon_code_string = pon_code.split("/");

                    if (ObjectUtil.isNotEmpty(pon_code_string) && pon_code_string.length <= 3) {
                        try {
                            String frame = "";
                            String slot = "";
                            String port = "";
                            if (pon_code_string.length == 3) {
                                frame = pon_code_string[0].replaceFirst("^0+(?!$)", "");
                                slot = pon_code_string[1].replaceFirst("^0+(?!$)", "");
                                port = pon_code_string[2].replaceFirst("^0+(?!$)", "");
                            } else if (pon_code_string.length == 2) {
                                frame = "1";
                                slot = pon_code_string[0].replaceFirst("^0+(?!$)", "");
                                port = pon_code_string[1].replaceFirst("^0+(?!$)", "");
                            }
                            JSONObject param = new JSONObject();
                            param.put("frame", frame);
                            param.put("slot", slot);
                            param.put("port", port);
                            param.put("devip", ip_address);
                            param.put("serial", "testxxxxxx");

                            FutureTask<JSONObject> futureTask = new FutureTask<>(new Callable<JSONObject>() {
                                @Override
                                public JSONObject call() throws Exception {
                                    Mono<JSONObject> ponreturn = webClient.post().
                                            uri("/PON_CHECK")
                                            .contentType(MediaType.APPLICATION_JSON)
                                            .syncBody(param)
                                            .retrieve()
                                            .bodyToMono(JSONObject.class);
                                    JSONObject obj = ponreturn.block();
                                    return obj;
                                }
                            });
                            executor.submit(futureTask);
                            FutureTaskList.add(futureTask);
                            ponfutureTaskMap.put(futureTask, 1);
                        } catch (Exception ex) {
                            log.error("解析端口时发生错误：" + ex.getMessage());
                        }


                    }
                }
            }


            for (int i = 0; i < FutureTaskList.size(); i++) {
                FutureTask<JSONObject> futureTask = FutureTaskList.get(i);

                try {
                    JSONObject obj = futureTask.get();

                    log.info("iposs返回结果：" + obj);

                    //如果这个pon下面的猫的个数为0，那么就重试

                    if (ObjectUtil.isEmpty(obj.getJSONObject("result")) || ObjectUtil.isEmpty(obj.getJSONObject("result").getJSONArray("LOID")) || obj.getJSONObject("result").getJSONArray("LOID").size() == 0) {

                        int count = ponfutureTaskMap.get(futureTask);
                        if (count <= 3) {
                            Thread.sleep(1000);
                            executor.submit(futureTask);
                            FutureTaskList.add(futureTask);
                            count++;
                            ponfutureTaskMap.put(futureTask, count);
                            continue;
                        } else {
                            obj.put("msg", "调用失败");

                        }

                    } else {
                        resultArray.add(obj);

                    }

                } catch (InterruptedException e) {
                    // 处理中断异常
                    e.printStackTrace();
                } catch (ExecutionException e) {
                    // 处理计算过程中的异常
                    e.printStackTrace();
                }


            }


            result.put("checkpon", resultArray);
            result.put("ipmap", ipmap);

            result.put("message", "success");


        } else {
            result.put("message", "fail");


        }


        return result;


    }


    //执行pon口检测
    @PostMapping("checkpon")
    @LogAnnotation( interfaceName="障碍定位 checkpon 接口-执行pon口检测")

    public JSONObject checkpon(@RequestBody JSONObject request) {

        JSONObject result = new JSONObject();

        JSONArray poninfo = request.getJSONArray("poninfo");
        String city = request.getString("city");
        List<String> ipList = new ArrayList<>();

        for (int i = 0; i < poninfo.size(); i++) {
            JSONObject ponInfoObject = poninfo.getJSONObject(i);
            String ip = ponInfoObject.getString("ip");
            ipList.add(ip);
        }

        JSONObject ipparam = new JSONObject();
        ipparam.put("loopback_ips", ipList);
        PageResponse<JSONObject> ipResponse = groupFaultRapidPositioningDao.pageQueryPONManageIP(ipparam, 1000, 1,"ds_odso_"+city);
        List<JSONObject> ipResult = ipResponse.getData();
        log.info("ipresult {}" , ipResult);

        // 构造ip映射关系
        Map<String, String> ipmap = new HashMap<>();
        for (int i = 0; i < ipResult.size(); i++) {
            // 获取当前的JSONObject对象
            JSONObject jsonObject = ipResult.get(i);
            // 从JSONObject中提取loopback_ip_2nd字段的值
            String loopbackIp2nd = jsonObject.getString("loopback_ip_2nd");
            String loopbackIp = jsonObject.getString("loopback_ip");
            ipmap.put(loopbackIp, loopbackIp2nd);
        }
        log.info("ipmap {}" , ipmap);
        if (ObjectUtil.isEmpty(ipResult)) {
            result.put("message", "fail");
            return result;
        }


        PageResponse<JSONObject> manufactorResponse = groupFaultRapidPositioningDao.pageQueryPONManuFactor(ipparam, 1000, 1, "ds_odso_" + city);
        List<JSONObject> manufactorResult = manufactorResponse.getData();
        log.info("manufactorResult {}" , ipResult);
        JSONObject manufactorMap = new JSONObject();
        for (JSONObject manufactor: manufactorResult) {
            String loopbackIp = manufactor.getString("ip");
            String manufactorName = manufactor.getString("manufactor_name");
            manufactorMap.put(loopbackIp, manufactorName);
        }




        JSONArray resultArray = new JSONArray();


        // 创建一个JSONArray来存储结果
        JSONArray transferedPoninfo = new JSONArray();
        // 遍历poninfo JSONArray
        for (int i = 0; i < poninfo.size(); i++) {
            JSONObject currentObject = poninfo.getJSONObject(i);
            String currentIp = currentObject.getString("ip");

            // 检查当前IP是否存在于ipmap中
            if (ipmap.containsKey(currentIp)) {
                // 如果存在，将当前JSONObject添加到结果JSONArray中
                currentObject.put("nm_ip", ipmap.get(currentIp));
                transferedPoninfo.add(currentObject);
            }
        }

        log.info("transferedPoninfo" + transferedPoninfo);

        ArrayList<FutureTask<JSONObject>> FutureTaskList = new ArrayList<FutureTask<JSONObject>>();
        //FutureTask
        Map<FutureTask, Integer> ponfutureTaskMap = new HashMap<>(); // 每个FutureTask
        for (int i = 0; i < transferedPoninfo.size(); i++) {
            JSONObject currentPoninfo = transferedPoninfo.getJSONObject(i);
            String res_ip_address = currentPoninfo.getString("ip");
            String ip_address = currentPoninfo.getString("nm_ip");
            String pon_code = currentPoninfo.getString("pon_code");
            String manufactorName = manufactorMap.getString(res_ip_address);


            if (ObjectUtil.isNotEmpty(pon_code)) {
                String[] pon_code_string = pon_code.split("/");

                if (ObjectUtil.isNotEmpty(pon_code_string) && pon_code_string.length <= 3) {
                    try {
                        JSONObject param = new JSONObject();
                        String frame = "";
                        String slot = "";
                        String port = "";
                        String realPort = ""; // 设备真实的端口编码
                        if (pon_code_string.length == 3) {
                            frame = pon_code_string[0].replaceFirst("^0+(?!$)", "");
                            slot = pon_code_string[1].replaceFirst("^0+(?!$)", "");
                            port = pon_code_string[2].replaceFirst("^0+(?!$)", "");
                        } else if (pon_code_string.length == 2) {
                            // frame = "1";
                            slot = pon_code_string[0].replaceFirst("^0+(?!$)", "");
                            port = pon_code_string[1].replaceFirst("^0+(?!$)", "");
                        }

                        log.info("manufactor name {}", manufactorName);
                        if (city.equals("zj") && ObjectUtil.isNotEmpty(manufactorName) && manufactorName.contains("华为")) {
                            // todo 针对镇江要做特殊处理,对所有的端口号-1
                            log.info("针对镇江华为设备将端口号-1");
                            realPort = (Integer.parseInt(port) - 1) + "";
                        } else {
                            realPort = port;
                        }

                        param.put("frame", frame);
                        param.put("slot", slot);
                        param.put("port", realPort);
                        param.put("devip", ip_address);
                        param.put("serial", "testxxxxxx");


                        param.put("res_dev_ip", res_ip_address);
                        param.put("res_port_code", pon_code);
                        param.put("res_port", port);
                        param.put("res_slot", slot);
                        param.put("res_frame", frame);


                        FutureTask<JSONObject> futureTask = new FutureTask<>(new Callable<JSONObject>() {
                            @Override
                            public JSONObject call() throws Exception {
                                // 执行耗时的计算任务

                                Mono<JSONObject> ponreturn = webClient.post().
                                        uri("/PON_CHECK")
                                        .header("app-key","98C6C0906EFCA2A1F564433D1A1638B6")
                                        .header("staffCode","IPOSS")
                                        .contentType(MediaType.APPLICATION_JSON)  //JSON数据类型
                                        .syncBody(param)  //JSON字符串数据
                                        .retrieve()
                                        .bodyToMono(JSONObject.class); // 获取响应体

                                JSONObject obj = ponreturn.block();

                                if (ObjectUtil.equal(obj.getString("msg"), "调用成功")) {
                                    JSONObject resultObject = obj.getJSONObject("result");
                                    resultObject.put("res_dev_ip", param.getString("res_dev_ip"));
                                    resultObject.put("res_port_code", param.getString("res_port_code"));
                                    resultObject.put("res_port", param.getString("res_port"));
                                    resultObject.put("res_slot", param.getString("res_slot"));
                                    resultObject.put("res_frame", param.getString("res_frame"));
                                }


                                return obj;
                            }
                        });

                        executor.submit(futureTask);

                        FutureTaskList.add(futureTask);

                        ponfutureTaskMap.put(futureTask, 1);
                    } catch (Exception ex) {
                        log.error("解析端口时发生错误：" + ex.getMessage());
                    }


                }
            }
        }


        for (int i = 0; i < FutureTaskList.size(); i++) {
            FutureTask<JSONObject> futureTask = FutureTaskList.get(i);

            try {
                JSONObject obj = futureTask.get();

                log.info("iposs返回结果：" + obj);

                //如果这个pon下面的猫的个数为0，那么就重试

                if (ObjectUtil.isEmpty(obj.getJSONObject("result")) || ObjectUtil.isEmpty(obj.getJSONObject("result").getJSONArray("LOID")) || obj.getJSONObject("result").getJSONArray("LOID").size() == 0) {

                    int count = ponfutureTaskMap.get(futureTask);
                    if (count <= 1) {
                        Thread.sleep(1000);
                        executor.submit(futureTask);
                        FutureTaskList.add(futureTask);
                        count++;
                        ponfutureTaskMap.put(futureTask, count);
                        continue;
                    } else {
                        obj.put("msg", "调用失败");
                    }

                } else {

                    // todo 处理针对
                    resultArray.add(obj);

                }

            } catch (InterruptedException e) {
                // 处理中断异常
                log.warn(e.getMessage(), e);
            } catch (ExecutionException e) {
                // 处理计算过程中的异常
                log.warn(e.getMessage(), e);
            }


        }


        result.put("checkpon", resultArray);
        result.put("ipmap", ipmap);

        result.put("message", "success");


        return result;
    }

    //
//
    @PostMapping("getProListByLoid")
    @LogAnnotation( interfaceName="障碍定位 getProListByLoid 接口-根据loid获取接入号清单")
    public JSONArray queryCFStoPon(@RequestBody JSONObject request) {
        JSONObject res = ponService.queryLOID2CFS(request);
        return res.getJSONArray("pro_info_list");
    }


    /**
     * @description:
     * 这里先检测pon在资源下面的资源树信息，然后检测pon接口的在线情况，如果检测成功，就把两者相互结合，并输出结果
     * */
    @PostMapping("multipleponcheck")
    @LogAnnotation( interfaceName="障碍定位 multipleponcheck 接口-多个pon口一起检测")

    public JSONObject multiplePonCheck(@RequestBody JSONObject request) {

        JSONObject result = new JSONObject();

        log.info("@@@@@@@@@@request, {}", request);
        //首先关联到接入设备，也就是猫
        String areaCode = request.getString("city");
        String shardingCode = "ds_bc_o3_" + areaCode.toLowerCase();
        JSONArray poninfo = request.getJSONArray("poninfo");
        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("pon_2_access_device").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("pon_port", "poninfo", poninfo)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
//        graphService.buildTree(data, "PON端口", "接入设备", "access_device");
//        graphService.buildTree(data, "接入设备", "cfs", "cfs");
//        List<JSONObject> pontree = graphService.filterNodeByLabel(data, "PON端口");

        graphService.buildTree(data, "PON端口", "接入设备", "access_device");
        graphService.buildTree(data, "PON端口", "设备", "device");
        graphService.buildTree(data, "接入设备", "cfs", "cfs");
        graphService.buildTree(data, "光路", "设备", "device");

        List<JSONObject> pontree = graphService.filterNodeByLabel(data, "PON端口");
        List<JSONObject> opt_road = graphService.filterNodeByLabel(data, "光路");

        result.put("pontree",pontree);

        result.put("opt_road",opt_road);

        log.info("检查pontree资源树情况"+pontree.toString());
        log.info("检查opt_road资源树情况"+opt_road.toString());



        //执行pon口检测
        JSONObject poncheckResult = checkpon(request);

        log.info("poncheckResult", poncheckResult);
        result.put("poncheckResult", poncheckResult);


        String message = poncheckResult.getString("message");
        if (ObjectUtil.equal(message, "success")) {
            JSONArray ponresultList = poncheckResult.getJSONArray("checkpon");
            log.info("ponresultList: {}", ponresultList);
            // JSONObject ipmap = poncheckResult.getJSONObject("ipmap"); // 不再需要IPMap了

            JSONArray ponCheckCombinePonTree = new JSONArray();


            for (int i = 0; i < ponresultList.size(); i++) {
                JSONObject item = (JSONObject) ponresultList.get(i);
                JSONObject tempResult = new JSONObject();
                String msg = item.getString("msg");
                String duration = item.getString("duration");
                tempResult.put("msg", msg);
                tempResult.put("duration", duration);

                if (ObjectUtil.equal(item.getString("msg"), "调用成功")) {

                    JSONObject resultObject = item.getJSONObject("result");
                    String devip = resultObject.getString("devip");
                    String frame = resultObject.getString("res_frame");
                    String slot = resultObject.getString("res_slot");
                    String port = resultObject.getString("res_port");
                    String res_dev_ip = resultObject.getString("res_dev_ip");
                    String res_port_code = resultObject.getString("res_port_code");

                    tempResult.put("devip", devip);
                    tempResult.put("res_dev_ip", res_dev_ip);
                    tempResult.put("frame", frame);
                    tempResult.put("slot", slot);
                    tempResult.put("port", port);
                    tempResult.put("port_code", res_port_code);
                    List<String> loids = new ArrayList<String>();
                    JSONArray LOID = item.getJSONObject("result").getJSONArray("LOID");

                    // 构造接入设备属性Map对象,key为接入设备的LOID，value为接入设备的在线状态和光衰等
                    Map<String, JSONObject> loidObjectMap = new HashMap<>();
                    if (ObjectUtil.isEmpty(LOID)) {
                        continue;
                    }
                    for (int j = 0; j < LOID.size(); j++) {
                        JSONObject loidObj = LOID.getJSONObject(j);
                        loids.add(loidObj.getString("loid"));
                        String loid = loidObj.getString("loid");
                        loidObjectMap.put(loid, loidObj); // 除了找runState之外，还需要rxPower
                    }

                    // 在产品列表中设置设备的运行状态
                    JSONObject param = new JSONObject();
                    param.put("loids", loids);
                    param.put("areaCode", request.getString("city"));
                    JSONObject res2 = ponService.queryLOID2CFS(param);
                    List<JSONObject> ponInfoList =JSONObjectUtil.jsonObjectArrayToList( res2.getJSONArray("pro_info_list"));
                    // 为每个设备添加运行状态
                    ponInfoList.forEach(jsonObjectProInfo -> {
                        String loid = jsonObjectProInfo.getString("loid");
                        String runState = loidObjectMap.getOrDefault(loid, new JSONObject()).getOrDefault("runState", "未知状态").toString();
                        String rxPower = loidObjectMap.getOrDefault(loid, new JSONObject()).getOrDefault("rxPower", "未知状态").toString();
                        String txPower = loidObjectMap.getOrDefault(loid, new JSONObject()).getOrDefault("txPower", "未知状态").toString();
                        String ontid = loidObjectMap.getOrDefault(loid, new JSONObject()).getOrDefault("ontid", "未知状态").toString();

                        // String runState = loidToRunStateMap.getOrDefault(loid, "未知状态"); // 如果找不到loid，则默认为"未知状态"
                        jsonObjectProInfo.put("runState", runState); // 将运行状态添加到设备信息中
                        jsonObjectProInfo.put("rxPower", rxPower);
                        jsonObjectProInfo.put("txPower", txPower);
                        jsonObjectProInfo.put("ontid", ontid);

                    });
                    tempResult.put("access_device", ponInfoList);
                } else {
                    tempResult.put("result", item);
                }
                ponCheckCombinePonTree.add(tempResult);
            }

            // 在PON口检测结果中补充资源系统的终端
            for (int i = 0; i < ponCheckCombinePonTree.size(); i++) {
                JSONObject resultObj = ponCheckCombinePonTree.getJSONObject(i);
                String resDevIp = resultObj.getString("res_dev_ip");
                String resPonString = resultObj.getString("port_code");

                /*
                String resDevPonSlot = resultObj.getString("slot");
                String resDevPonPort = resultObj.getString("port");


                String resPonString = String.format("%02d/%02d/%02d",
                        Integer.parseInt(resDevPonFrame),
                        Integer.parseInt(resDevPonSlot),
                        Integer.parseInt(resDevPonPort));

                 */

                for (int j = 0; j < pontree.size(); j++) {
                    JSONObject ponObj = pontree.get(j);
                    String ponCode = ponObj.getString("pon_code");
                    // 根据资源系统中查出来的ponObj查找PON口检测的结果resultObj
                    if (ponObj.getString("ip").equals(resDevIp) && (ponCode.equals(resPonString))) {
                        JSONArray ponAccessDevices = ponObj.getJSONArray("access_device"); // 资源系统中的接入设备
                        JSONArray resultAccessDevices = resultObj.getJSONArray("access_device"); // PON口检测结果中的接入设备
                        // 如果资源系统里面的接入设备在PON口检测结果中不存在,则将资源系统中的接入设备补充到检测结果中
                        for (int k = 0; k < ponAccessDevices.size(); k++) {
                            JSONObject ponDevice = ponAccessDevices.getJSONObject(k);
                            String ponDeviceId = ponDevice.getString("id");
                            boolean exists = false;

                            for (int l = 0; l < resultAccessDevices.size(); l++) {
                                if (resultAccessDevices.getJSONObject(l).getString("id").equals(ponDeviceId)) {
                                    exists = true;
                                    break;
                                }
                            }
                            if (!exists) {
                                resultAccessDevices.add(ponDevice);
                            }
                        }
                        break; // 匹配到后退出内层循环
                    }
                }
            }
            result.put("ponCheckCombinePonTree", ponCheckCombinePonTree);
        }
        log.info("result:结果", result);
        return result;
    }


    @PostMapping("poncheckimport")
    @LogAnnotation( interfaceName="障碍定位 poncheckimport 接口-根据文件导入pon口编码")

    public JSONObject ponCheckImport(@RequestParam(name = "file") MultipartFile multipartFile) throws Exception {
        // 解析XLSX文件并提取IP和PON信息
        List<Map<String, Object>> ipPonList = parseExcel(multipartFile.getInputStream());

        // 将结果封装到JSONObject中
        JSONObject result = new JSONObject();
        result.put("data", ipPonList);

        return result;
    }


    private List<Map<String, Object>> parseExcel(InputStream inputStream) throws Exception {
        List<Map<String, Object>> ipPonList = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell ipCell = row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    Cell ponCell = row.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    String ip = getStringCellValue(ipCell);
                    String pon = getStringCellValue(ponCell);
                    if (!ip.isEmpty() && !pon.isEmpty()) {
                        Map<String, Object> ipPonMap = new HashMap<>();
                        ipPonMap.put("ip", ip);
                        ipPonMap.put("pon", pon);
                        ipPonList.add(ipPonMap);
                    }
                }
            }
        }
        return ipPonList;
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return Double.toString(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return Boolean.toString(cell.getBooleanCellValue());
            default:
                return "";
        }
    }


    public static String getNativeNetId(String cityAbbreviation) {
        switch (cityAbbreviation.toLowerCase()) { // 转换为小写以确保匹配不受大小写影响
            case "nj":
                return "0000003"; // 南京
            case "zj":
                return "0000013"; // 镇江
            case "wx":
                return "0000004"; // 无锡
            case "sz":
                return "0000007"; // 苏州
            case "nt":
                return "0000008"; // 南通
            case "yz":
                return "0000012"; // 扬州
            case "yc":
                return "0000011"; // 盐城
            case "xz":
                return "0000005"; // 徐州
            case "ha":
                return "0000010"; // 淮安
            case "lyg":
                return "0000009"; // 连云港
            case "cz":
                return "0000006"; // 常州
            case "tz":
                return "0000014"; // 泰州
            case "sq":
                return "0000015"; // 宿迁
            default:
                return "Unknown"; // 如果找不到匹配项，则返回Unknown或其他默认值
        }
    }


    //条目编号	条目值
    //BAS	1
    //OLT	2
    //分光器	3
    //DSLAM	6
    //ONU	4
    //IAD	5
    //楼道交换机	7
    //BAN	8
    //ZAN	9
    //交接箱	10
    //分线盒	11
    //主干电缆	12
    //AG	13
    //光缆段编码	701

    public static String getdeviceType(String devtype) {
        switch (devtype.toLowerCase()) { // 转换为小写以确保匹配不受大小写影响
            case "pon":
                return "2";
            case "frstobd":
                return "3";
            case "scndobd":
                return "3";
            case "cbl":
                return "701";
            case "cbl_sect":
                return "701";
            case "fiber_busi_node":
                return "701";   //没有，用光缆段替代
            case "net_code":
                return "12";
            case "pipe_code":
                return "701";//没有，用光缆段替代
            case "olt":
                return "2";
            case "card":
                return "2";//没有，用olt替代
            case "obd":
                return "3";
            case "onu":
                return "4";
            case "odb":
                return "10";
            case "odb2":
                return "11";
            case "odf":
                return "2";//没有，用xx替代
            case "idc":
                return "10"; //没有，用xx替代
            default:
                return "701"; // 如果找不到匹配项，则返回Unknown或其他默认值
        }
    }




    @PostMapping("queryPonByObd")
    @LogAnnotation( interfaceName="障碍定位 queryPonByObd 接口-根据obd查到pon口")

    public JSONObject queryPonByObd(@RequestBody JSONObject request) {
        JSONObject result = new JSONObject();
        log.info("queryPonByObd  request", request.toString());
        //首先关联到接入设备，也就是猫
        String areaCode = request.getString("city");
        String shardingCode = "ds_bc_o3_" + areaCode.toLowerCase();
        String obdinfo = request.getString("obdinfo");

        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("query_pon_by_obd").setShardingCode(shardingCode)
                .appendWhereBodyItem(new GraphWhereBodyItem("device", "code", obdinfo)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        graphService.buildTree(data, "设备", "PON端口", "pon_port");
        //光缆段清单
        List<JSONObject> ponlist = graphService.filterNodeByLabel(data, "PON端口");
        result.put("ponlist",ponlist);

        log.info("return result", result);
        return result ;

    }


        @PostMapping("makecause")
        @LogAnnotation( interfaceName="障碍定位 makecause 接口-生成障碍")

        public JsonNode makecause(@RequestBody JSONObject request) {


        String city = request.getString("city");
        String causeName = request.getString("causeName");
        String devType = request.getString("devType");
        String devCode = request.getString("devCode");
        Date startTime = request.getDate("startTime");
        Date endTime = request.getDate("endTime");
        String remark2 = request.getString("remark2");

        String recordType = "";
        String faultType = "";
        String areaId = "";
        String deviceIp = "";
        String maintainTimes = "";
        String dutyUserId = "";
        String createUser = "";
        String remark = "";
        String isValid = "";

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeString = format.format(startTime);
        String endTimeString = format.format(endTime);

        String nativeNetId = getNativeNetId(city);
        String deviceType = getdeviceType(devType);


        //todo  city  devtype

        StringBuffer xmlmiddleString = new StringBuffer("");

        List<String> all_accs_code_list = new ArrayList<>();


        xmlmiddleString.append("      <nativeNetId>" + nativeNetId + "</nativeNetId>\n" +
                "                           <causeName>" + causeName + devCode + "</causeName>\n" +
                "                           <recordType>project</recordType>\n" +
                "                           <causeType>importData</causeType>\n" +
                "                           <startTime>" + startTimeString + "</startTime>\n" +
                "                           <endTime>" + endTimeString + "</endTime>\n" +
                "                           <remark2>" + remark2 + "</remark2>\n"
        );

        if (ObjectUtil.isNotEmpty(request.getString("recordType"))) {
            recordType = request.getString("recordType");
            xmlmiddleString.append("<recordType>" + recordType + "</recordType>");
        }
        if (ObjectUtil.isNotEmpty(request.getString("faultType"))) {
            faultType = request.getString("faultType");
            xmlmiddleString.append("<faultType>" + faultType + "</faultType>");

        } else {
            xmlmiddleString.append("<faultType>04</faultType>");
        }
        if (ObjectUtil.isNotEmpty(request.getString("areaId"))) {
            areaId = request.getString("areaId");
            xmlmiddleString.append("<areaId>" + areaId + "</areaId>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("deviceIp"))) {
            deviceIp = request.getString("deviceIp");
            xmlmiddleString.append("<deviceIp>" + deviceIp + "</deviceIp>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("maintainTimes"))) {
            maintainTimes = request.getString("maintainTimes");
            xmlmiddleString.append("<maintainTimes>" + maintainTimes + "</maintainTimes>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("dutyUserId"))) {
            dutyUserId = request.getString("dutyUserId");
            xmlmiddleString.append("<dutyUserId>" + dutyUserId + "</dutyUserId>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("createUser"))) {
            createUser = request.getString("createUser");
            xmlmiddleString.append("<createUser>" + createUser + "</createUser>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("remark"))) {
            remark = request.getString("remark");
            xmlmiddleString.append("<remark>" + remark + "</remark>");

        }
        if (ObjectUtil.isNotEmpty(request.getString("isValid"))) {
            isValid = request.getString("isValid");
            xmlmiddleString.append("<isValid>" + isValid + "</isValid>");

        }


        if (ObjectUtil.isNotEmpty(request.getJSONArray("numberList")) && (request.getJSONArray("numberList").size() > 0)) {

            all_accs_code_list = JSONObjectUtil.jsonStringArrayToList(request.getJSONArray("numberList"));


            xmlmiddleString.append("  <numberList>\n");
            int count = 0;
            for (int i = 0; i < all_accs_code_list.size(); i++) {
                String accs_nbr_no = all_accs_code_list.get(i);
                xmlmiddleString.append("  <relnumber>" + accs_nbr_no + "</relnumber>\n");
            }
            xmlmiddleString.append("  </numberList>\n");


        } else if (ObjectUtil.equal(devType, "scndobd") || ObjectUtil.equal(devType, "frstobd")) {

            JSONObject obdrequest = new JSONObject();
//
//            List<String> devCodeArray = new ArrayList<>();
//            devCodeArray.add(devCode);

            obdrequest.put("phy_eqp_id", devCode);
            obdrequest.put("city", city);
            obdrequest.put("current_page", 1);
            obdrequest.put("page_size", 5000);


            PageResponse<JSONObject> res = getAccsCodeByphyportid(obdrequest);

            List<JSONObject> data = res.getData();


            xmlmiddleString.append("  <numberList>\n");
            int count = 0;
            for (int i = 0; i < data.size(); i++) {
                String accs_nbr_no = data.get(i).getString("accs_nbr_no");
                xmlmiddleString.append("  <relnumber>" + accs_nbr_no + "</relnumber>\n");
                all_accs_code_list.add(accs_nbr_no);
            }
            xmlmiddleString.append("  </numberList>\n");


        } else if (ObjectUtil.equal(devType, "pon")) {

            JSONObject oltrequest = new JSONObject();
//
//            List<String> devCodeArray = new ArrayList<>();
//            devCodeArray.add(devCode);

            oltrequest.put("phy_port_id", devCode);
            oltrequest.put("city", city);
            oltrequest.put("current_page", 1);
            oltrequest.put("page_size", 5000);


            PageResponse<JSONObject> res = getAccsCodeByphyportid(oltrequest);

            List<JSONObject> data = res.getData();


            xmlmiddleString.append("  <numberList>\n");
            int count = 0;
            for (int i = 0; i < data.size(); i++) {
                String accs_nbr_no = data.get(i).getString("accs_nbr_no");
                xmlmiddleString.append("  <relnumber>" + accs_nbr_no + "</relnumber>\n");
                all_accs_code_list.add(accs_nbr_no);
            }
            xmlmiddleString.append("  </numberList>\n");


        } else if (ObjectUtil.equal(devType, "cbl")) {
            JSONObject cblrequest = new JSONObject();

            List<String> devCodeArray = new ArrayList<>();
            devCodeArray.add(devCode);
//            JSONArray devCodeArray = new JSONArray();
//            devCodeArray.add("segments",devCode);

            cblrequest.put("segments", devCodeArray);
            cblrequest.put("city", city);
            cblrequest.put("current_page", 1);
            cblrequest.put("page_size", 5000);


            JSONArray res = getAllAccCodesByFiberCableSegments(cblrequest);


            xmlmiddleString.append("  <numberList>\n");
            int count = 0;
            for (int i = 0; i < res.size(); i++) {
                List<String> accs_nbr_noList = res.getJSONObject(i).getObject("accs_nbr_no", List.class);
                count += accs_nbr_noList.size();
                for (String accs_nbr_no : accs_nbr_noList) {
                    xmlmiddleString.append("  <relnumber>" + accs_nbr_no + "</relnumber>\n");
                    all_accs_code_list.add(accs_nbr_no);

                }
            }
            xmlmiddleString.append("  </numberList>\n");


        }


        String XmlStartString = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.asip.regaltec.com/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <ser:executeXML>\n" +
                "        <text><![CDATA[<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "            <SERVICE>\n" +
                "                <IDA_SVR_SA>\n" +
                "                    <CALL_METHOD>acceptSaCommCause</CALL_METHOD>\n" +
                "                        <INPUT_XMLDATA>\n";

        String XmlEndString = "                        </INPUT_XMLDATA>\n" +
                "                </IDA_SVR_SA>\n" +
                "        </SERVICE>]]></text>\n" +
                "      </ser:executeXML>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";


        //构造webservice请求参数
        StringBuffer soapRequestData = new StringBuffer("");
        soapRequestData.append(XmlStartString);
        soapRequestData.append(xmlmiddleString);
        soapRequestData.append(XmlEndString);


        //构造http请求头
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("text/xml;charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> formEntity = new HttpEntity<String>(soapRequestData.toString(), headers);

            // 配置超时参数
            int connectTimeout = 5000; // 连接超时时间（毫秒）
            int readTimeout = 30000;   // 读取超时时间（毫秒）

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(readTimeout)
                    .build();

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(requestConfig)
                    .build();

// 将HttpClient注入RestTemplate
            HttpComponentsClientHttpRequestFactory factory =
                    new HttpComponentsClientHttpRequestFactory(httpClient);
            factory.setConnectTimeout(connectTimeout);
            factory.setReadTimeout(readTimeout);



            RestTemplate restTemplate = new RestTemplate(factory);
        //返回结果


        System.out.println("soapRequestData.toString()" + soapRequestData.toString());

        String resultStr = "";
        try {
            //                 resultStr = restTemplate.postForObject("http://132.224.244.230:8001/ida/services/AsigService", formEntity, String.class);

            resultStr = restTemplate.postForObject(zd_url, formEntity, String.class);

            log.info(resultStr);
        } catch (HttpClientErrorException e) {
            // 处理 4xx 客户端错误
            System.err.println("Client error: " + e.getStatusCode());
            System.err.println("Error details: " + e.getResponseBodyAsString());
        } catch (HttpServerErrorException e) {
            // 处理 5xx 服务器错误
            System.err.println("Server error: " + e.getStatusCode());
            System.err.println("Error details: " + e.getResponseBodyAsString());
        } catch (ResourceAccessException e) {
            // 处理资源访问异常，如连接超时、I/O 错误等
            System.err.println("Resource access exception: " + e.getMessage());
        } catch (RestClientException e) {
            // 处理其他 RestClient 异常
            System.err.println("General RestClient exception: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他未知异常
            System.err.println("Unexpected exception occurred: " + e.getMessage());
        }

        String tmpStr = StringEscapeUtils.unescapeXml(resultStr);
        String resultXML = StringUtils.substringBetween(tmpStr, "<data_info>", "</data_info>");

        XmlMapper xmlMapper = new XmlMapper();
        ObjectMapper jsonMapper = new ObjectMapper();

        JsonNode jsonNode = null;
        try {
            jsonNode = xmlMapper.readTree(resultXML.getBytes("UTF-8"));
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
        String json = "{}";

        //打印
        try {
            json = jsonMapper.writeValueAsString(jsonNode);


            System.out.println("makecause结果" + json);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        JSONObject jsonObject = JSON.parseObject(json);
        String ReturnCode = jsonObject.getString("ReturnCode");
        String CauseId = jsonObject.getString("CauseId");
//        String Message = jsonObject.getString("Message");

        if (ObjectUtil.equal(ReturnCode, "1")) {


//            causeName: causeName,
//            city: city,
//            devType:devType,
//            devCode:devCode,
//            startTime:makecauseTime.value,
//            endTime:endDate,
//            operator:userStore.getUserInfo.realName ,
//            remark:remark ,
//            remark2:remark2

            request.put("isValid", true);
            request.put("CauseId", CauseId);

            request.put("accs_nbr_no", all_accs_code_list);
            savecause(request);

        }


        return jsonNode;
    }


    //    @PostMapping("getAccsCodeByphyportid")
    //用于找到OBD和OLT影响的全部用户，进行一键拦截用的
    public PageResponse<JSONObject> getAccsCodeByphyportid(@RequestBody JSONObject request) {

        String phy_port_id;
        String phy_eqp_id;
        JSONObject param = new JSONObject();
        int currentPage = 1;
        int pageSize = 5000;

        if (ObjectUtil.isNotEmpty(request.getString("phy_port_id"))) {
            phy_port_id = request.getString("phy_port_id");
            param.put("phy_port_id", phy_port_id);

        }
        if (ObjectUtil.isNotEmpty(request.getString("phy_eqp_id"))) {
            phy_eqp_id = request.getString("phy_eqp_id");
            param.put("phy_eqp_id", phy_eqp_id);

        }
        if (ObjectUtil.isNotEmpty(request.getString("current_page"))) {
            currentPage = Integer.parseInt(request.getString("current_page"));
        }
        if (ObjectUtil.isNotEmpty(request.getString("page_size"))) {
            pageSize = Integer.parseInt(request.getString("page_size"));
        }
        String sharding_code = "ds_odso_" + request.getString("city");

        PageResponse<JSONObject> response = new PageResponse<JSONObject>();


        response = groupFaultRapidPositioningDao.pageQueryGetAccsCode(param, pageSize, currentPage, sharding_code);


        return response;
    }


    public boolean savecause(@RequestBody JSONObject request) {

        JSONObject param = new JSONObject();
        String shardingCode = "ds_graph_js";

        String city = request.getString("city");
        String causeName = request.getString("causeName");
        String devType = request.getString("devType");
        String devCode = request.getString("devCode");
        Date startTime = request.getDate("startTime");
        Date endTime = request.getDate("endTime");

        Boolean isvalid = request.getBoolean("isValid");
        String causeid = request.getString("CauseId");
        String operator = request.getString("operator");
        String remark = request.getString("remark");
        String remark2 = request.getString("remark2");

        JSONArray accNbrNoList = request.getJSONArray("accs_nbr_no");


        param.put("causeid", causeid);
        param.put("causename", causeName);
        param.put("city", city);
        param.put("devtype", devType);
        param.put("devip", "");
        param.put("devcode", devCode);
        param.put("starttime", startTime);
        param.put("endtime", endTime);
        param.put("relnumbersum", accNbrNoList.size());
        param.put("isvalid", isvalid);
        param.put("operator", operator);
        param.put("remark", remark);
        param.put("remark2", remark2);


        //{
        //  "causeid": 123123123,
        //  "causename": "test",
        //  "city": "yz",
        //  "devtype": "pon",
        //  "devip": "**************",
        //  "devcode": "ASQ.12345PQCSADQWE",
        //  "starttime": "2024-04-10 00:00:00",
        //  "endtime": "2024-04-11 00:00:00",
        //  "relnumbersum": 24,
        //  "isvalid": true,
        //  "operator": "peiqicheng",
        //  "remark": "备注test",
        //  "remark2": "备注test2"
        //}


        int result = groupFaultRapidPositioningDao.tb_comm_cause_insert(param, shardingCode);


        JSONObject param2 = new JSONObject();

        //{
        //  "list": [
        //    {
        //      "causeid": 1234,
        //      "relnumber": "111111"
        //    },
        //    {
        //      "causeid": 1234,
        //      "relnumber": "222222"
        //    },
        //    {
        //      "causeid": 1234,
        //      "relnumber": "333333"
        //    }
        //  ]
        //}


        //插入群障关联的号码
        JSONArray numberObjectArray = new JSONArray();

        for (int i = 0; i < accNbrNoList.size(); i++) {
            String number = accNbrNoList.getString(i);

            JSONObject numerObject = new JSONObject();
            numerObject.put("causeid", causeid);
            numerObject.put("relnumber", number);

            numberObjectArray.add(numerObject);
        }
        param2.put("list", numberObjectArray);


        JSONObject result2 = groupFaultRapidPositioningDao.tb_cause_number_batch_insert(param2, shardingCode);


        return true;


    }


    @GetMapping("querycause")
    @LogAnnotation( interfaceName="障碍定位 querycause 接口-查询障碍")

    public BiyiPageResult<JSONObject> queryCause(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);


//            Set<String> keySet = jsonObject.keySet();
        // 遍历JSONObject并删除空字符串的内容
//            for (String key : keySet) {
//                if (ObjectUtil.isEmpty(jsonObject.getString(key))) {
//                    jsonObject.remove(key);
//                }
//            }


        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        // log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        PageResponse<JSONObject> pageResponse = groupFaultRapidPositioningDao.pageQueryqueryCause(jsonObject, pageable.getSize(), pageable.getPage(), "ds_graph_js");
        log.info("pageResponse");
        if (pageResponse != null && pageResponse.getData() != null && pageResponse.getData().size() != 0) {
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());

            data.addAll(pageResponse.getData());
            totalCount = totalCount + pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data, totalCount, totalCount);
    }

    @PostMapping("changecausestate")
    @LogAnnotation( interfaceName="障碍定位 changecausestate 接口-修改障碍状态-主要是恢复障碍")

    public JSONObject ChangeCauseCtate(@RequestBody JSONObject request) {

        JSONObject result = new JSONObject();

        String causeId = request.getString("causeId");
        String state = request.getString("state");
        String operator = request.getString("operator");


        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = format.format(new Date());

        //  <causeId>2027888323</causeId>
        //   <recoveryTime>2019-12-09 07:41:51</recoveryTime>
        //   <remark>备注</remark>


        //todo  city  devtype

        StringBuffer xmlmiddleString = new StringBuffer("");

        List<String> all_accs_code_list = new ArrayList<>();

//        xmlmiddleString.append( "      <causeId>"+causeId+"</causeId>\n"+
//                "                      <recoveryTime>"+now+"</causeName>\n"
//          //      + "                      <modifyUser>"+operator+"</modifyUser>\n"
//        );

        xmlmiddleString.append(
                "   <causeId>" + causeId + "</causeId>\n" +
                        "<modifyUser>" + operator + "</modifyUser>\n" +
                        "   <recoveryTime>" + now + "</recoveryTime>\n" +
                        "   <remark>元凤平台恢复</remark>\n");


        String XmlStartString = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.asip.regaltec.com/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <ser:executeXML>\n" +
                "        <text><![CDATA[<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "            <SERVICE>\n" +
                "                <IDA_SVR_SA>\n" +
                "                    <CALL_METHOD>recoverySaCommCause</CALL_METHOD>\n" +
                "                        <INPUT_XMLDATA>\n";

        String XmlEndString = "     </INPUT_XMLDATA>\n" +
                "                </IDA_SVR_SA>\n" +
                "        </SERVICE>]]></text>\n" +
                "      </ser:executeXML>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";


        //构造webservice请求参数
        StringBuffer soapRequestData = new StringBuffer("");
        soapRequestData.append(XmlStartString);
        soapRequestData.append(xmlmiddleString);
        soapRequestData.append(XmlEndString);

        //构造http请求头
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("text/xml;charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> formEntity = new HttpEntity<String>(soapRequestData.toString(), headers);
        RestTemplate restTemplate = new RestTemplate();
        //返回结果

        String resultStr = "";
        try {
            //resultStr = restTemplate.postForObject("http://132.224.244.230:8001/ida/services/AsigService", formEntity, String.class);

            resultStr = restTemplate.postForObject(zd_url, formEntity, String.class);
        } catch (HttpClientErrorException e) {
            // 处理 4xx 客户端错误
            System.err.println("Client error: " + e.getStatusCode());
            System.err.println("Error details: " + e.getResponseBodyAsString());
        } catch (HttpServerErrorException e) {
            // 处理 5xx 服务器错误
            System.err.println("Server error: " + e.getStatusCode());
            System.err.println("Error details: " + e.getResponseBodyAsString());
        } catch (ResourceAccessException e) {
            // 处理资源访问异常，如连接超时、I/O 错误等
            System.err.println("Resource access exception: " + e.getMessage());
        } catch (RestClientException e) {
            // 处理其他 RestClient 异常
            System.err.println("General RestClient exception: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他未知异常
            System.err.println("Unexpected exception occurred: " + e.getMessage());
        }

        String tmpStr = StringEscapeUtils.unescapeXml(resultStr);


        String resultXML = StringUtils.substringBetween(tmpStr, "<data_info>", "</data_info>");

        XmlMapper xmlMapper = new XmlMapper();
        ObjectMapper jsonMapper = new ObjectMapper();

        JsonNode jsonNode = null;
        try {
            jsonNode = xmlMapper.readTree(resultXML.getBytes("UTF-8"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        String json = "{}";

        //打印
        try {
            json = jsonMapper.writeValueAsString(jsonNode);

        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        JSONObject jsonObject = JSON.parseObject(json);


        if (ObjectUtil.equal(jsonObject.get("ReturnCode"), "1")) {


            //修改群障状态
            groupFaultRapidPositioningDao.updatetb_comm_cause_isrecoveried(causeId, "ds_graph_js");

        }


        return jsonObject;


    }


    @PostMapping("getstartendpointinfo")
    @LogAnnotation( interfaceName="障碍定位 otdr定位 get_start_end_point_info 接口-获取光缆段起点终点信息-otdr定位")
    public JSONObject getStartEndPointInfo(@RequestBody JSONObject request) {

        JSONObject result = new JSONObject();
        String input = request.getString("input");
        String city = request.getString("city");

        JSONObject param = new JSONObject();
        param.put("cbl_no", input);
        String Shardingcode = "ds_odso_" + city;
        PageResponse<JSONObject> pageResponse = groupFaultRapidPositioningDao.get_start_end_point_info(param, 10, 1, Shardingcode);
        result.put("data", pageResponse.getData());
        return result;
    }


    @PostMapping("getbsesectinfobycblsect")
    @LogAnnotation( interfaceName="障碍定位 otdr定位 getbsesectinfobycblsect 接口-获取光缆段对应的管道段")

    public JSONObject getBseSectInfoByCblSect(@RequestBody JSONObject request) {

        JSONObject result = new JSONObject();
        String input = request.getString("input");
        String city = request.getString("city");

        String Shardingcode = "ds_bc_o3_" + city;


        GraphRequestBuilder builder = new GraphRequestBuilder();
        GraphRequest graphRequest = builder.setApiId("otdr_cable_2_bsesect").setShardingCode(Shardingcode)
                .appendWhereBodyItem(new GraphWhereBodyItem("cable_segment", "code", input)).build();
        Graph graph = graphApiService.doApi(graphRequest);
        JSONObject data = graphService.getData(graph);
        graphService.buildTree(data, "光缆段", "管道段", "pipelineList");
        //光缆段清单
        List<JSONObject> cable_segment_list = graphService.filterNodeByLabel(data, "光缆段");


        graphService.buildReverseTree(data, "光缆盘留", "支撑设施", "cable_reel");
        List<JSONObject> facility = graphService.filterNodeByLabel(data, "支撑设施");


        //设备清单
        List<JSONObject> deviceList = graphService.filterNodeByLabel(data, "设备");
        ponService.sortcableSegmentList(cable_segment_list, deviceList);
        result.put("cable_segment_list", cable_segment_list);

        result.put("facility", facility);

        return result;
    }

    @PostMapping("query_oltip_by_accs_nbr_no")
    @LogAnnotation( interfaceName="障碍定位 query_oltip_by_accs_nbr_no 接口-根据接入号查olt的ip地址")
    public JSONObject query_oltip_by_accs_nbr_no(@RequestBody JSONObject request) {

        String city = request.getString("city");

        String Shardingcode = "ds_odso_" + city;


        System.out.println("request"+request);
        System.out.println("Shardingcode"+Shardingcode);


        JSONObject result = groupFaultRapidPositioningDao.query_oltip_by_accs_nbr_no(request,Shardingcode);

        System.out.println(result.toString());




        return result;
    }

    @PostMapping("query_tb_groupfault_log_postion")
    @LogAnnotation( interfaceName="障碍定位 query_tb_groupfault_log_postion 接口-查障碍定位历史障碍的位置")
    public JSONObject query_tb_groupfault_log_postion(@RequestBody JSONObject request) {


        System.out.println("request"+request);


        JSONObject result = groupFaultRapidPositioningDao.query_tb_groupfault_log_postion(request,"ds_graph_js");

        System.out.println(result.toString());




        return result;

    }

    /**
     * 查询光缆段涉及的产品总数
     * @param request 请求参数，包含光缆段编码列表和城市代码
     * @return 返回每个光缆段对应的产品总数
     */
    @PostMapping("cable-section-product-count")
    @LogAnnotation(interfaceName = "障碍定位 cable-section-product-count 接口-查询光缆段涉及产品总数")
    public ResponseEntity<JSONObject> getCableSectionProductCount(@RequestBody JSONObject request) {
        String city = request.getString("city");
        JSONArray cableCodes = request.getJSONArray("cableCodes");

        if (ObjectUtil.isEmpty(cableCodes) || ObjectUtil.isEmpty(city)) {
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "参数不能为空：cableCodes 和 city 是必需的");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        JSONObject response = new JSONObject();
        JSONArray resultList = new JSONArray();

        try {
            String shardingCode = "ds_odso_" + city.toLowerCase();

            // 遍历每个光缆段编码，查询其涉及的产品总数
            for (int i = 0; i < cableCodes.size(); i++) {
                String cableCode = cableCodes.getString(i);

                try {
                    // 构建查询参数
                    JSONObject param = new JSONObject();
                    param.put("cbl_sect", cableCode);

                    // 调用影响分析DAO查询CFS业务
                    JSONObject cfsResult = influenceAnalysisDao.query_cfs_influence_by_cbl_sect_code(param, shardingCode);

                    // 统计产品总数
                    int productCount = 0;
                    if (cfsResult != null && cfsResult.containsKey("cfs_influence")) {
                        JSONArray cfsInfluenceList = cfsResult.getJSONArray("cfs_influence");
                        productCount = cfsInfluenceList != null ? cfsInfluenceList.size() : 0;
                    }

                    // 构建结果对象
                    JSONObject cableResult = new JSONObject();
                    cableResult.put("cableCode", cableCode);
                    cableResult.put("productCount", productCount);
                    cableResult.put("success", true);

                    resultList.add(cableResult);

                    log.info("光缆段 {} 涉及产品总数: {}", cableCode, productCount);

                } catch (Exception e) {
                    log.error("查询光缆段 {} 产品总数失败", cableCode, e);

                    // 查询失败时返回0
                    JSONObject cableResult = new JSONObject();
                    cableResult.put("cableCode", cableCode);
                    cableResult.put("productCount", 0);
                    cableResult.put("success", false);
                    cableResult.put("error", e.getMessage());

                    resultList.add(cableResult);
                }
            }

            response.put("success", true);
            response.put("data", resultList);
            response.put("total", resultList.size());

            log.info("光缆段产品总数查询完成，共查询 {} 个光缆段", cableCodes.size());

        } catch (Exception e) {
            log.error("查询光缆段产品总数时发生错误", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }

        return ResponseEntity.ok(response);
    }

}
