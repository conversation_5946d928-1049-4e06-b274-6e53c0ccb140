package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.UserDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.ResTreeBackDto;
import com.telecom.nrm.domain.ResTreeParamDto;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;


@RestController
@RequestMapping("/api/resTree")
@Slf4j
public class ResTreeController {

    @Autowired
    private CustLinkService custLinkService;

    @Autowired
    private CableSectionService cableSectionService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ProductService productService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private HunderRiverService hunderRiverService;

    @Autowired
    private UserDao userDao;

    @PostMapping("doApi")
    public ResTreeBackDto doApi(@RequestBody ResTreeParamDto param) {
        ResTreeBackDto res = new ResTreeBackDto();
        res.setPageSize(param.getPageSize());
        res.setPageNum(param.getPageNum());

        try {
            if (param.getBusinessParam() == null) {
                throw new RuntimeException("入参businessParam不能为空");
            }

            String apiId = param.getBusinessParam().getApiId();
            if (StringUtils.isEmpty(apiId)) {
                throw new RuntimeException("入参apiId不能为空");
            }

            //根据不同的apiId，执行不同的方法
            JSONObject data = new JSONObject();
            if ("432927000923940".equals(apiId)) {
                //3.49服务组件_业务电路精确查询业务电路精确查
                data = custLinkService.queryCircuitByParam(param);
            } else if ("948720015557157".equals(apiId)) {
                //3.50服务组件_根据电路查询业务电路（样板）
                data = custLinkService.queryCircuitByCondition(param);
            } else if ("876805519404841".equals(apiId)) {
                //3.51服务组件_根据光缆查询业务电路
                data = cableSectionService.queryCircuitByCable(param);
            } else if ("277769484477877".equals(apiId)) {
                //3.52服务组件_根据设备查询业务电路
                data = deviceService.queryCircuitByDevice(param);
            } else if ("930958263607055".equals(apiId)) {
                //3.53服务组件_根据业务电路查询告警清单

            } else if ("260112776988917".equals(apiId)) {
                //3.54服务组件_查询业务电路在途开通单清单

            } else if ("753278078532869".equals(apiId)) {
                //3.55服务组件_查询业务电路在途故障单清单
                data = custLinkService.queryFaultListByCondition(param);
            } else if ("773717831992436".equals(apiId)) {
                //3.56服务组件_业务电路查询产品信息
                data = productService.queryProdByCircuit(param);
            } else if ("885532279080405".equals(apiId)) {
                //3.57服务组件_业务电路查询客户信息
                data = customerService.queryCustomerByCircuit(param);
            } else if ("465936429691554".equals(apiId)) {
                //3.58服务组件_业务电路属性查询
                data = custLinkService.queryCircuitDetailByCondition(param);
            } else if ("117415111663040".equals(apiId)) {
                //3.59服务组件_查询业务电路全程路由(简单路由)
                data = custLinkService.queryCircuitRouteByCondition(param);
            } else if ("374737931072294".equals(apiId)) {
                //3.60服务组件_查询业务电路全程路由(详细路由)
                data = custLinkService.queryCircuitRoutePlusByCondition(param);
            } else if ("504486099043344".equals(apiId)) {
                //3.61服务组件_实时告警查询

            } else if ("588032264829012".equals(apiId)) {
                //3.62服务组件_性能数据查询

            } else if ("386600045053826".equals(apiId)) {
                //3.63服务组件_质量数据查询

            } else if ("369696838016845".equals(apiId)) {
                //3.64服务组件_点资源空间位置信息查询
                data = deviceService.queryResInfoByIds(param);
            } else if ("278440884826968".equals(apiId)) {
                //3.30服务组件_根据客户/电路查询客户信息
                data = customerService.queryCustomerByCircuitOrCust(param);
            } else if ("343685876564931".equals(apiId)) {
                //3.31服务组件_根据客户Id统计业务电路总数
                data = custLinkService.queryCircuitCountByCusId(param);
            } else if ("730802617674915".equals(apiId)) {
                //3.32服务组件_根据客户Id统计业务电路告警数

            } else if ("819031490779547".equals(apiId)) {
                //3.33服务组件_根据客户Id统计业务电路开通单数

            } else if ("341592163238799".equals(apiId)) {
                //3.34服务组件_根据客户Id统计业务电路故障单数

            } else if ("533776426910409".equals(apiId)) {
                //3.35服务组件_根据客户Id查询业务电路清单
                data = custLinkService.queryCircuitListByCusId(param);
            } else if ("496304176514873".equals(apiId)) {
                //3.36服务组件_根据客户Id查询业务电路告警清单

            } else if ("939935072977909".equals(apiId)) {
                //3.37服务组件_根据客户Id查询业务电路在途开通单清单

            } else if ("805733089017861".equals(apiId)) {
                //3.38服务组件_根据客户Id查询业务电路在途故障单清单

            } else if ("443656805455114".equals(apiId)) {
                //3.39服务组件_根据客户Id查询业务电路资源树拓扑

            } else if ("160468653910486".equals(apiId)) {
                //3.40服务组件_线资源钻取下层路由

            } else if ("938726625122188".equals(apiId)) {
                //3.41服务组件_线资源查看详情

            } else if ("573349253603451".equals(apiId)) {
                //3.42服务组件_点资源查看详情

            } else if ("979288120072777".equals(apiId)) {
                //3.43服务组件_实时告警加载

            } else if ("355305197461117".equals(apiId)) {
                //3.44服务组件_根据客户Id查询业务电路路由点资源空间位置信息

            } else if ("514294963595590".equals(apiId)) {
                //3.45服务组件_根据客户Id统计业务总带宽数

            } else {
                throw new RuntimeException("apiId：" + apiId + "不存在");
            }

            res.setCode("0");
            res.setMessage("成功");
            res.setData(data);
        } catch (Exception e) {
            res.setCode("1");
            res.setMessage(e.toString());
            res.setData(new JSONObject());
        }

        return res;
    }

    //网络故障工单进度查询
    @PostMapping("queryNetFautlProgress")
    public ResTreeBackDto queryNetFautlProgress(@RequestBody JSONObject param) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ResTreeBackDto result = new ResTreeBackDto();

        try {
            String orderId = param.get("orderId") == null ? null : param.getString("orderId");
            String orderNbr = param.get("orderNbr") == null ? null : param.getString("orderNbr");

            JSONObject queryObj = new JSONObject();
            if (StringUtils.isNotEmpty(orderId)) {
                queryObj.put("field_name", "a.bill_id");
                queryObj.put("fieldValue", orderId);
            } else if (StringUtils.isNotEmpty(orderNbr)) {
                queryObj.put("field_name", "a.bill_sn");
                queryObj.put("fieldValue", orderNbr);
            }

            JSONObject hunderRiverRes = hunderRiverService.zdNetFaultQuery(queryObj);
            JSONObject hunderRiverValue = hunderRiverRes.getJSONObject("value");
            String revertTime = hunderRiverValue.get("revert_time") == null ? null : sdf.format(new Date(hunderRiverValue.getLong("revert_time")));
            String dispatchTime = hunderRiverValue.get("dispatch_time") == null ? null : sdf.format(new Date(hunderRiverValue.getLong("dispatch_time")));

            JSONArray actualTacheInfo = new JSONArray();
            JSONObject actualTacheObj = new JSONObject();
            actualTacheObj.put("num", 1);
            actualTacheObj.put("tacheName", "故障处理");
            actualTacheObj.put("tacheState", revertTime == null ? 1 : 0);
            actualTacheObj.put("tacheType", 1);
            actualTacheObj.put("startTime", dispatchTime);
            actualTacheObj.put("endTime", revertTime);
            actualTacheObj.put("errorMsg", "");
            actualTacheObj.put("errorMsgCode", "");
            actualTacheObj.put("handlePeople", "");
            actualTacheInfo.add(actualTacheObj);

            JSONObject data = new JSONObject();
            data.put("actualTacheInfo", actualTacheInfo);
            data.put("orderId", orderId);
            data.put("orderNbr", orderNbr);

            result.setCode("0");
            result.setMessage("成功");
            result.setData(data);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage(e.toString());
            result.setData(null);
        }

        return result;
    }

    //客户故障申告工单进度查询
    @PostMapping("queryCustFautlProgress")
    public ResTreeBackDto queryCustFautlProgress(@RequestBody JSONObject param) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ResTreeBackDto result = new ResTreeBackDto();

        try {
            JSONObject hunderRiverRes = hunderRiverService.zdCustOrderQuery(param);
            JSONObject hunderRiverValue = hunderRiverRes.getJSONObject("value");
            String revertTime = hunderRiverValue.get("reverttime") == null ? null : sdf.format(new Date(hunderRiverValue.getLong("reverttime")));
            String createTime = hunderRiverValue.get("create_time") == null ? null : sdf.format(new Date(hunderRiverValue.getLong("create_time")));

            JSONArray tacheInfo = new JSONArray();
            JSONObject tacheObj = new JSONObject();
            tacheObj.put("endTime", revertTime);
            tacheObj.put("num", 1);
            tacheObj.put("startTime", createTime);
            tacheObj.put("tacheId", param.getString("orderNbr") + "01");
            tacheObj.put("tacheName", "工单处理");
            tacheObj.put("tacheState", revertTime == null ? 1 : 0);
            tacheObj.put("tacheType", 1);
            tacheInfo.add(tacheObj);

            JSONArray flowInfo = new JSONArray();
            JSONObject flowObj = new JSONObject();
            flowObj.put("endTime", revertTime);
            flowObj.put("flowId", param.getString("orderNbr") + "01");
            flowObj.put("flowName", "申告工单处理流程");
            flowObj.put("flowState", revertTime == null ? 1 : 0);
            flowObj.put("num", 1);
            flowObj.put("parentFlowId", 0);
            flowObj.put("startTime", createTime);
            flowObj.put("tacheInfo", tacheInfo);
            flowInfo.add(flowObj);

            JSONObject data = new JSONObject();
            data.put("flowInfo", flowInfo);
            data.put("orderId", param.getString("orderNbr"));
            data.put("orderNbr", param.getString("orderNbr"));

            result.setCode("0");
            result.setMessage("成功");
            result.setData(data);
        } catch (Exception e) {
            result.setCode("1");
            result.setMessage(e.toString());
            result.setData(null);
        }

        return result;
    }

    @GetMapping("/queryRestInterfaceInvokeCount")
    public Object queryRestInterfaceInvokeCount() {
        SimpleDateFormat sdf = new SimpleDateFormat("MM月dd日");

        JSONObject json = userDao.queryRestInterfaceInvokeCount(new JSONObject(), NRMConstants.SHARDING_CODE);
        JSONArray array = json.getJSONArray("result");
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            obj.put("day", sdf.format(new Date(obj.getLong("day"))));
        }
        return json.getJSONArray("result");
    }

    @GetMapping("/queryRestDataCount")
    public Object queryRestDataCount() {
        JSONObject json = userDao.queryRestDataCount(new JSONObject(), NRMConstants.SHARDING_CODE);

        JSONObject result = new JSONObject();
        result.put("vpnUserCount", json.getJSONObject("vpnUserCount").getString("count"));
        result.put("vpnInstanceCount", json.getJSONObject("vpnInstanceCount").getString("count"));
        result.put("old85Count", json.getJSONObject("old85Count").getString("count"));
        result.put("new85count", json.getJSONObject("new85count").getString("count"));
        return result;
    }

}
