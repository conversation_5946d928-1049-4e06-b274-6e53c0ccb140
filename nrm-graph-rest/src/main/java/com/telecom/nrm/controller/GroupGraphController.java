package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphResponse;
import com.telecom.nrm.service.GroupGraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;

@RestController
@RequestMapping("/api/groupGraph")
@Slf4j
public class GroupGraphController {

    @Autowired
    GroupGraphService groupGraphService;

    @PostMapping("")
    public ResponseEntity<GraphResponse> doApi(@RequestBody GraphRequest request) throws URISyntaxException {
        log.debug("REST request to save entity : {}", request);
        GraphResponse graphResponse = new GraphResponse();
        try {
            JSONObject graph = groupGraphService.doApi(request);
            graphResponse.setData(graph);
            graphResponse.setCode("200");
            graphResponse.setMessage("ok");
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            graphResponse.setCode("500");
            graphResponse.setMessage(ex.getMessage());
        }
        return ResponseEntity.ok(graphResponse);
    }


}
