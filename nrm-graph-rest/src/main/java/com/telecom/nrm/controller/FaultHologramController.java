package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.FaultHologramDao;
import com.telecom.nrm.dao.InfluenceAnalysisDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import com.telecom.nrm.service.ObsLocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.MessageService;

@RestController
@RequestMapping("/api/faulthologram")
@Slf4j
public class FaultHologramController {

    static String appKey = "34a4a5f34a0d826e";
    static String appSecret = "c5916b6c34a4a5f34a0d826ec436d81b";


    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;
    @Autowired
    ObsLocationService obsLocationService;


    @Autowired
    InfluenceAnalysisDao influenceAnalysisDao;


    @Autowired
    FaultHologramDao faultHologramDao;

    @Autowired
    private MessageService messageService;

    @Autowired
    RegionDao regionDao;

    //查在途的网络障碍单
    @PostMapping("/queryfaultordernow")
    @LogAnnotation(interfaceName="障碍全息视图 queryfaultordernow 接口-查询当前障碍单")
    public JSONObject queryfaultordernow(@RequestBody JSONObject params) {
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/wod/yybz/queryfaultorderbymultipleparam";


        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 打印响应体
        System.out.println(response.getBody());


        JSONObject result = JSON.parseObject(response.getBody());

        return result;
    }



    //查在途的网络障碍单
    @PostMapping("/queryfaultordernowunionhis")
    @LogAnnotation(interfaceName="障碍全息视图 queryfaultordernowunionhis 接口-查询历史障碍单")
    public JSONObject queryfaultordernowunionhis(@RequestBody JSONObject params) {
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/pon/wod/yybz/queryfaultordernowunionhis";


        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        log.info(response.getBody());

        JSONObject result = JSON.parseObject(response.getBody());

        return result;
    }



    //查在途的网络障碍单
    @PostMapping("/queryFaultOrderByTitle")
    @LogAnnotation(interfaceName="障碍全息视图 queryFaultOrderByTitle 接口-根据名称查询障碍单")
    public JSONObject queryFaultOrderByTitle(@RequestBody JSONObject params) {
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/pon/wod/yybz/queryFaultOrderByTitle";


        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        log.info(response.getBody());

        JSONObject result = JSON.parseObject(response.getBody());

        return result;
    }






    //查在途的网络障碍单
    @PostMapping("/queryCachefaultordernow")
    @LogAnnotation(interfaceName="障碍全息视图 queryCachefaultordernow 接口-根据缓存查询查询障碍单")
    public JSONObject queryCachefaultordernow(@RequestBody JSONObject params) {



        JSONObject request = params.getJSONObject("params");

        request.put("bill_status","处理中");
        JSONObject result =        faultHologramDao.query_zd_fault_main_bill(  request  ,"ds_graph_js");

        return result;
    }

    //为了避免影响之前的功能，新建一个自动获取障碍的接口
    @PostMapping("/auto-queryfaultordernow")
    @LogAnnotation(interfaceName="障碍全息视图 auto-queryfaultordernow 接口-自动查询当前障碍单")
    public JSONObject auto_queryfaultordernow() {
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/wod/yybz/queryfaultorderbymultipleparam";



        JSONObject params = new JSONObject();

        List<String> nativenet_id = new ArrayList<>();
        nativenet_id.add("0000003");
        nativenet_id.add("0000013");
        nativenet_id.add("0000004");
        nativenet_id.add("0000007");
        nativenet_id.add("0000008");
        nativenet_id.add("0000012");
        nativenet_id.add("0000011");
        nativenet_id.add("0000005");
        nativenet_id.add("0000010");
        nativenet_id.add("0000009");
        nativenet_id.add("0000006");
        nativenet_id.add("0000014");
        nativenet_id.add("0000015");


        params.put("nativenet_id",nativenet_id);

        List<String> lxs = new ArrayList<>();
        lxs.add("主干断纤软告警");
        params.put("lxs",lxs);
//        params.put("day",1);

        JSONObject newRequest = new JSONObject();
        newRequest.put("params",params);

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        System.out.println("newRequest"+newRequest);
        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(newRequest, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);


        // 检查响应状态码是否成功
        if (response.getStatusCode().is2xxSuccessful()) {
            // 获取响应体中的JSON字符串
            String jsonString = response.getBody();

            // 使用FastJSON将JSON字符串解析为JSONObject
            JSONObject jsonObject = JSONObject.parseObject(jsonString);

            JSONArray  listValue = jsonObject.getJSONArray("value");


            System.out.println("listValue"+listValue);

            // 字段名映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("主专业","parent_business_name");
            fieldMapping.put("主处理岗","main_deal_group_name");
            fieldMapping.put("主处理人","main_deal_person_name");
            fieldMapping.put("子专业","businessname");
            fieldMapping.put("bill_id","bill_id");
            fieldMapping.put("工单号","bill_sn");
            fieldMapping.put("工单标题","bill_title");
            fieldMapping.put("设备信息","deal_code");
            fieldMapping.put("工单状态","bill_status");
            fieldMapping.put("障碍来源","fault_src");
            fieldMapping.put("派单时间","create_time");
            fieldMapping.put("设备编码","equipment_code");
            fieldMapping.put("设备名称","equipment_name");
            fieldMapping.put("设备ip地址","equipment_ip");
            fieldMapping.put("局站名","sp_office");
            fieldMapping.put("告警信息","fault_info");
            fieldMapping.put("故障位置","fault_location_info");
            fieldMapping.put("联系电话","main_deal_oper_phone");
            fieldMapping.put("接单时间","reply_time");
            fieldMapping.put("恢复时间","fault_recover_time");
            fieldMapping.put("工单时限","bill_limit");
            fieldMapping.put("工单剩余历时","remaining_time");
            fieldMapping.put("工单历时","elapsed_time");
            fieldMapping.put("类型","alert_type");
            fieldMapping.put("nativenet_id","nativenet_id");

            //替换字段名为数据库里面的字段名
            JSONArray newJsonArray = replaceFields(listValue, fieldMapping);
//            System.out.println("newJsonArray"+newJsonArray);

            JSONObject param = new JSONObject();
            param.put("list",newJsonArray);
            JSONObject result = new JSONObject();

            if(newJsonArray.size()!=0){

                JSONObject insertResult = faultHologramDao.tb_zd_fault_main_bill_batch_insert(param,"ds_graph_js");


//                System.out.println("insertResult"+insertResult);

                result.put("省网资graph库插入：",insertResult);

                Map<String, List<JSONObject>> groupedMap = new HashMap<>();

                for (int i = 0; i < newJsonArray.size(); i++) {
                    JSONObject jsonObject2 = newJsonArray.getJSONObject(i);
                    String nat = jsonObject2.getString("nativenet_id");

                    // 根据nat分组
                    if (!groupedMap.containsKey(nat)) {
                        groupedMap.put(nat, new ArrayList<>());
                    }
                    groupedMap.get(nat).add(jsonObject2);


                }



                for (String key : NRMConstants.ZD_NATIVENET_ID.keySet()) {
                    if(groupedMap.keySet().contains(key)){

                        String Shardingcode = NRMConstants.ZD_NATIVENET_ID.get(key);
                        List Natfaultorder = groupedMap.get(key);
                        if(Natfaultorder.size()>0){

                            JSONObject listtosave = new JSONObject();



                            listtosave.put("list",Natfaultorder);
                            //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
//                            System.out.println("listtosave"+listtosave);

                            JSONObject insertResult2 = faultHologramDao.tb_zd_fault_main_bill_batch_insert(listtosave,Shardingcode);
//                            System.out.println(key+"插入结果"+insertResult2);

                            result.put(Shardingcode,insertResult2);
                        }

                    }

                }
            }




            return result;








        } else {
            // 处理不成功的响应
//            System.out.println("Request failed. Status code: " + response.getStatusCode());

            JSONObject result = new JSONObject();
            result.put("result","查询百川失败");
            return result;

        }





    }

    //为了避免影响之前的功能，新建一个自动获取障碍的接口
    @PostMapping("/auto-queryfaultorderhis")
    @LogAnnotation(interfaceName="障碍全息视图 auto-queryfaultorderhis 接口-自动查询历史障碍单")
    public JSONObject auto_queryfaultorderhis() {
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/wod/yybz/queryhisfaultorder";


        JSONObject params = new JSONObject();

        List<String> nativenet_id = new ArrayList<>();
        nativenet_id.add("0000003");
        nativenet_id.add("0000013");
        nativenet_id.add("0000004");
        nativenet_id.add("0000007");
        nativenet_id.add("0000008");
        nativenet_id.add("0000012");
        nativenet_id.add("0000011");
        nativenet_id.add("0000005");
        nativenet_id.add("0000010");
        nativenet_id.add("0000009");
        nativenet_id.add("0000006");
        nativenet_id.add("0000014");
        nativenet_id.add("0000015");


        params.put("nativenet_id",nativenet_id);

        List<String> lxs = new ArrayList<>();
        lxs.add("主干断纤软告警");
        params.put("lxs",lxs);
        params.put("day",10);

        JSONObject newRequest = new JSONObject();
        newRequest.put("params",params);

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(newRequest, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);


        // 检查响应状态码是否成功
        if (response.getStatusCode().is2xxSuccessful()) {
            // 获取响应体中的JSON字符串
            String jsonString = response.getBody();

            // 使用FastJSON将JSON字符串解析为JSONObject
            JSONObject jsonObject = JSONObject.parseObject(jsonString);


            JSONArray  listValue = jsonObject.getJSONArray("value");


//            System.out.println("listValue"+listValue);

            // 字段名映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("主专业","parent_business_name");
            fieldMapping.put("主处理岗","main_deal_group_name");
            fieldMapping.put("主处理人","main_deal_person_name");
            fieldMapping.put("子专业","businessname");
            fieldMapping.put("bill_id","bill_id");
            fieldMapping.put("工单号","bill_sn");
            fieldMapping.put("工单标题","bill_title");
            fieldMapping.put("设备信息","deal_code");
            fieldMapping.put("工单状态","bill_status");
            fieldMapping.put("障碍来源","fault_src");
            fieldMapping.put("派单时间","create_time");
            fieldMapping.put("设备编码","equipment_code");
            fieldMapping.put("设备名称","equipment_name");
            fieldMapping.put("设备ip地址","equipment_ip");
            fieldMapping.put("局站名","sp_office");
            fieldMapping.put("告警信息","fault_info");
            fieldMapping.put("故障位置","fault_location_info");
            fieldMapping.put("联系电话","main_deal_oper_phone");
            fieldMapping.put("接单时间","reply_time");
            fieldMapping.put("恢复时间","fault_recover_time");
            fieldMapping.put("工单时限","bill_limit");
            fieldMapping.put("工单剩余历时","remaining_time");
            fieldMapping.put("工单历时","elapsed_time");
            fieldMapping.put("类型","alert_type");
            fieldMapping.put("nativenet_id","nativenet_id");

            //替换字段名为数据库里面的字段名
            JSONArray newJsonArray = replaceFields(listValue, fieldMapping);
//            System.out.println("newJsonArray"+newJsonArray);

            JSONObject param = new JSONObject();
            param.put("list",newJsonArray);


            JSONObject insertResult = faultHologramDao.tb_zd_fault_main_bill_batch_insert(param,"ds_graph_js");


//            System.out.println("insertResult"+insertResult);


            JSONObject result = new JSONObject();
            result.put("省网资graph库插入：",insertResult);

            Map<String, List<JSONObject>> groupedMap = new HashMap<>();

            for (int i = 0; i < newJsonArray.size(); i++) {
                JSONObject jsonObject2 = newJsonArray.getJSONObject(i);
                String nat = jsonObject2.getString("nativenet_id");

                // 根据nat分组
                if (!groupedMap.containsKey(nat)) {
                    groupedMap.put(nat, new ArrayList<>());
                }
                groupedMap.get(nat).add(jsonObject2);


            }



            for (String key : NRMConstants.ZD_NATIVENET_ID.keySet()) {
                if(groupedMap.keySet().contains(key)){

                    String Shardingcode = NRMConstants.ZD_NATIVENET_ID.get(key);
                    List Natfaultorder = groupedMap.get(key);
                    if(Natfaultorder.size()>0){

                        JSONObject listtosave = new JSONObject();



                        listtosave.put("list",Natfaultorder);
                        //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
//                        System.out.println("listtosave"+listtosave);

                        JSONObject insertResult2 = faultHologramDao.tb_zd_fault_main_bill_batch_insert(listtosave,Shardingcode);
//                        System.out.println(key+"插入结果"+insertResult2);



                        result.put(Shardingcode,insertResult2);
                    }

                }

            }



            return result;

        } else {
            // 处理不成功的响应
//            System.out.println("Request failed. Status code: " + response.getStatusCode());

            JSONObject result = new JSONObject();
            result.put("result","查询百川查询历史工单失败");
            return result;

        }





    }











    private static JSONArray replaceFields(JSONArray jsonArray, Map<String, String> fieldMapping) {
        JSONArray newJsonArray = new JSONArray();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONObject newJsonObject = new JSONObject();

            for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                String oldField = entry.getKey();
                String newField = entry.getValue();

                if (jsonObject.containsKey(oldField)) {
                    newJsonObject.put(newField, jsonObject.get(oldField));
                }
            }

            // 保留那些不需要替换的字段
            for (String key : jsonObject.keySet()) {
                if (!fieldMapping.containsKey(key)) {
                    newJsonObject.put(key, jsonObject.get(key));
                }
            }

            newJsonArray.add(newJsonObject);
        }

        return newJsonArray;
    }

    //查归档的网络障碍单
    @PostMapping("/queryfaultorderhis")
    @LogAnnotation(interfaceName="障碍全息视图 queryfaultorderhis 接口-查询当前和历史障碍单混合查询")
    public JSONObject queryhisfaultorder(@RequestBody JSONObject params) {

        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/zd_reporter/common/wod/yybz/queryhisfaultorder";

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 打印响应体
//        System.out.println(response.getBody());

        JSONObject result = JSON.parseObject(response.getBody());
        return result;

    }



    //查归档的网络障碍单
    @PostMapping("/queryCachefaultorderhis")
    @LogAnnotation(interfaceName="障碍全息视图 queryCachefaultorderhis 接口-查询缓存的历史障碍单")
    public JSONObject queryCachefaultorderhis(@RequestBody JSONObject params) {


        JSONObject request = params.getJSONObject("params");
        request.put("bill_status","归档");

        JSONObject result =        faultHologramDao.query_zd_fault_main_bill(  request  ,"ds_graph_js");

        return result;

    }



    //查告警信息
    @PostMapping("/queryalarm")
    @LogAnnotation(interfaceName="障碍全息视图 queryalarm 接口-查询告警信息")

    public JSONObject queryalarmbyuserlabelopentime(@RequestBody JSONObject params) {

        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/alarm_data/pon/wod/dzxp/queryalarmbyuserlabelopentime";

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 打印响应体
//        System.out.println(response.getBody());

        JSONObject result = JSON.parseObject(response.getBody());

        return result;
    }



    //查告警信息
    @PostMapping("/queryCacheAlarm")
    public JSONObject queryCacheAlarm(@RequestBody JSONObject params) {



        JSONObject request = params.getJSONObject("params");


        JSONObject result = faultHologramDao.query_alarm_data_all(request  ,"ds_graph_js");

        return result;


    }

    /**
     *
     *
     * @param params
     *  params: {
     * //        day: alarmindays,
     * //                userLabel: userLabel
     * //    }
     * */

    //查指定设备告警并保存
    @PostMapping("/savealarmsbyuserlabel")
    public JSONObject savealarmsbyuserlabel(@RequestBody JSONObject params) {
        JSONObject jsonObject = queryalarmbyuserlabelopentime(params);

            //这里进行存储
//            System.out.println("返回结果"+jsonObject);
            JSONArray  listValue = jsonObject.getJSONArray("value");

            // 字段名映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("id","id");
            fieldMapping.put("告警流水号","sid");
            fieldMapping.put("网管首次告警时间","open_time");
            fieldMapping.put("网管恢复时间","close_time");
            fieldMapping.put("首次采集时间","create_time");
            fieldMapping.put("专业代码","spec_code");
            fieldMapping.put("故障区域代码","related_area_code");
            fieldMapping.put("设备名称","user_label");
            fieldMapping.put("告警概要","summary");
            fieldMapping.put("网元名称","eqp_name");
            fieldMapping.put("故障局站","eqp_spoffice");
            fieldMapping.put("地点信息","eqp_place");
            fieldMapping.put("优先级","priority");
            fieldMapping.put("恢复标识","cleared");
            fieldMapping.put("day_id","day_id");
            fieldMapping.put("告警累计次数","alm_count");
            fieldMapping.put("客户名称","cust_name");
            fieldMapping.put("电路名称","cir_name");
            fieldMapping.put("派单标识","billed");
            fieldMapping.put("工单号","bill_num");
            fieldMapping.put("附加a","appenda");
            fieldMapping.put("附加b","appendb");
            fieldMapping.put("附加c","appendc");
            fieldMapping.put("子专业名称","subspec_name");
            fieldMapping.put("网管代码","nm_code");
            fieldMapping.put("网管名称","nm_name");
            fieldMapping.put("网管区域名","area_name");
            fieldMapping.put("故障子区域名称","fault_subarea_name");
            fieldMapping.put("网管当前告警时间","event_time");
            fieldMapping.put("采集机当前告警时间","mediation_time");
            fieldMapping.put("消息类型编号","msty_id");
            fieldMapping.put("消息类型名","msty_name");
            fieldMapping.put("网管告警编号","alarm_id");
            fieldMapping.put("故障信息","fault_info");
            fieldMapping.put("网元类型编号","eqty_id");
            fieldMapping.put("网元类型名","eqty_name");
            fieldMapping.put("网元号","eqp_id");
            fieldMapping.put("位置信息","eqp_post");
            fieldMapping.put("端口编号","port_id");
            fieldMapping.put("端口名","port_name");
            fieldMapping.put("扩充a","contexta");
            fieldMapping.put("扩充b","contextb");
            fieldMapping.put("扩充c","contextc");
            fieldMapping.put("扩充d","contextd");
            fieldMapping.put("扩充e","contexte");
            fieldMapping.put("扩充f","contextf");
            fieldMapping.put("网管网元编号","eqp_me_id");
            fieldMapping.put("网元统一唯一值","eqp_uuid");
            fieldMapping.put("ip地址","eqp_ip");
            fieldMapping.put("fault_area_name","fault_area_name");
            fieldMapping.put("node_name","node_name");
            fieldMapping.put("屏蔽类型","shield_type");
            fieldMapping.put("屏蔽编号","shield_id");
            fieldMapping.put("延时内恢复不监控","is_cleared_shield");
            fieldMapping.put("派单恢复状态","bill_clear_stat");
            fieldMapping.put("派单恢复时间","bill_clear_time");
            fieldMapping.put("延时结束时间","delay_end_time");
            fieldMapping.put("延时结束","is_delay_end");
            fieldMapping.put("屏蔽","is_shield");
            fieldMapping.put("nm_priority","nm_priority");
            fieldMapping.put("父告警id","parent_alarm_id");
            fieldMapping.put("机房","house_id");
            fieldMapping.put("网元恢复时间","ne_close_time");
            fieldMapping.put("网元首告时间","ne_open_time");
            fieldMapping.put("实体所属厂家","ne_vendor");
            fieldMapping.put("归档标志","arch_flag");
            fieldMapping.put("归档时间","arch_time");
            fieldMapping.put("派单规则id","pbil_id");

            //替换字段名为数据库里面的字段名
            JSONArray newJsonArray = replaceFields(listValue, fieldMapping);
//            System.out.println("newJsonArray"+newJsonArray);

            JSONObject param = new JSONObject();
            param.put("list",newJsonArray);

            //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
            JSONObject insertResult = faultHologramDao.tb_alarm_data_all_batch_insert(param,"ds_graph_js");

            JSONObject result = new JSONObject();
            result.put("ds_graph_js：",insertResult);

            Map<String, List<JSONObject>> groupedMap = new HashMap<>();

            for (int i = 0; i < newJsonArray.size(); i++) {
                JSONObject jsonObject2 = newJsonArray.getJSONObject(i);
                String nat = jsonObject2.getString("fault_area_name");

                // 根据nat分组
                if (!groupedMap.containsKey(nat)) {
                    groupedMap.put(nat, new ArrayList<>());
                }
                groupedMap.get(nat).add(jsonObject2);


            }

            for (String key : NRMConstants.FAULT_AREA_NAME.keySet()) {
                if(groupedMap.keySet().contains(key)){
                    String Shardingcode = NRMConstants.FAULT_AREA_NAME.get(key);
                    List Natfaultorder = groupedMap.get(key);
                    if(Natfaultorder.size()>0){
                        JSONObject listtosave = new JSONObject();

                        listtosave.put("list",Natfaultorder);
                        //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
//                        System.out.println("listtosave"+listtosave);

                        JSONObject insertResult2 = faultHologramDao.tb_alarm_data_all_batch_insert(listtosave,Shardingcode);
//                        System.out.println(key+"插入结果"+insertResult2);



                        result.put(Shardingcode,insertResult2);
                    }
                }
            }



            return result;

    }





    //查告警信息
    @PostMapping("/auto-queryalarm")
    public JSONObject auto_queryalarmbyuserlabelopentime() {

        //首先从后端的障碍单表里获取到哪些处理中的障碍单
        JSONObject processingFaultOrder = faultHologramDao.query_processing_faultorder(new JSONObject(),"ds_graph_js");
//        System.out.println("处理中的障碍单processingFaultOrder"+processingFaultOrder);

//        {"result":[{"equipment_name":"南通如皋白蒲广场/A-1-ATN910I(************)"},{"equipment_name":"盐城射阳射阳港中兴OLT-2(************)"},{"equipment_name":"蓉中HW-OLT2-1(*********)"},{"equipment_name":"盐城亭湖永丰中兴OLT-2(************)"}]}
        //障碍单对应的设备名称
        JSONArray UserLabelsJsonArray= processingFaultOrder.getJSONArray("result");
        List<String> equipmentNames = (List<String>) UserLabelsJsonArray.stream()
                .map(obj -> ((JSONObject) obj).getString("equipment_name"))
                .collect(Collectors.toList());

        //从百川获取告警清单
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/alarm_data/pon/wod/dzxp/queryalarmbyduouserlabel";

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        JSONObject params = new JSONObject();
        params.put("day",2);
        params.put("userLabels",equipmentNames);

        JSONObject requestBaichuan = new JSONObject();
        requestBaichuan.put("params",params);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(requestBaichuan, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 检查响应状态码是否成功
        if (response.getStatusCode().is2xxSuccessful()) {
            // 获取响应体中的JSON字符串
            String jsonString = response.getBody();

            // 使用FastJSON将JSON字符串解析为JSONObject
            JSONObject jsonObject = JSONObject.parseObject(jsonString);

            //这里进行存储
//            System.out.println("返回结果"+jsonObject);
            JSONArray  listValue = jsonObject.getJSONArray("value");

            // 字段名映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("id","id");
            fieldMapping.put("告警流水号","sid");
            fieldMapping.put("网管首次告警时间","open_time");
            fieldMapping.put("网管恢复时间","close_time");
            fieldMapping.put("首次采集时间","create_time");
            fieldMapping.put("专业代码","spec_code");
            fieldMapping.put("故障区域代码","related_area_code");
            fieldMapping.put("设备名称","user_label");
            fieldMapping.put("告警概要","summary");
            fieldMapping.put("网元名称","eqp_name");
            fieldMapping.put("故障局站","eqp_spoffice");
            fieldMapping.put("地点信息","eqp_place");
            fieldMapping.put("优先级","priority");
            fieldMapping.put("恢复标识","cleared");
            fieldMapping.put("day_id","day_id");
            fieldMapping.put("告警累计次数","alm_count");
            fieldMapping.put("客户名称","cust_name");
            fieldMapping.put("电路名称","cir_name");
            fieldMapping.put("派单标识","billed");
            fieldMapping.put("工单号","bill_num");
            fieldMapping.put("附加a","appenda");
            fieldMapping.put("附加b","appendb");
            fieldMapping.put("附加c","appendc");
            fieldMapping.put("子专业名称","subspec_name");
            fieldMapping.put("网管代码","nm_code");
            fieldMapping.put("网管名称","nm_name");
            fieldMapping.put("网管区域名","area_name");
            fieldMapping.put("故障子区域名称","fault_subarea_name");
            fieldMapping.put("网管当前告警时间","event_time");
            fieldMapping.put("采集机当前告警时间","mediation_time");
            fieldMapping.put("消息类型编号","msty_id");
            fieldMapping.put("消息类型名","msty_name");
            fieldMapping.put("网管告警编号","alarm_id");
            fieldMapping.put("故障信息","fault_info");
            fieldMapping.put("网元类型编号","eqty_id");
            fieldMapping.put("网元类型名","eqty_name");
            fieldMapping.put("网元号","eqp_id");
            fieldMapping.put("位置信息","eqp_post");
            fieldMapping.put("端口编号","port_id");
            fieldMapping.put("端口名","port_name");
            fieldMapping.put("扩充a","contexta");
            fieldMapping.put("扩充b","contextb");
            fieldMapping.put("扩充c","contextc");
            fieldMapping.put("扩充d","contextd");
            fieldMapping.put("扩充e","contexte");
            fieldMapping.put("扩充f","contextf");
            fieldMapping.put("网管网元编号","eqp_me_id");
            fieldMapping.put("网元统一唯一值","eqp_uuid");
            fieldMapping.put("ip地址","eqp_ip");
            fieldMapping.put("fault_area_name","fault_area_name");
            fieldMapping.put("node_name","node_name");
            fieldMapping.put("屏蔽类型","shield_type");
            fieldMapping.put("屏蔽编号","shield_id");
            fieldMapping.put("延时内恢复不监控","is_cleared_shield");
            fieldMapping.put("派单恢复状态","bill_clear_stat");
            fieldMapping.put("派单恢复时间","bill_clear_time");
            fieldMapping.put("延时结束时间","delay_end_time");
            fieldMapping.put("延时结束","is_delay_end");
            fieldMapping.put("屏蔽","is_shield");
            fieldMapping.put("nm_priority","nm_priority");
            fieldMapping.put("父告警id","parent_alarm_id");
            fieldMapping.put("机房","house_id");
            fieldMapping.put("网元恢复时间","ne_close_time");
            fieldMapping.put("网元首告时间","ne_open_time");
            fieldMapping.put("实体所属厂家","ne_vendor");
            fieldMapping.put("归档标志","arch_flag");
            fieldMapping.put("归档时间","arch_time");
            fieldMapping.put("派单规则id","pbil_id");

            //替换字段名为数据库里面的字段名
            JSONArray newJsonArray = replaceFields(listValue, fieldMapping);
//            System.out.println("newJsonArray"+newJsonArray);

            JSONObject param = new JSONObject();
            param.put("list",newJsonArray);

            //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
            JSONObject insertResult = faultHologramDao.tb_alarm_data_all_batch_insert(param,"ds_graph_js");



            JSONObject result = new JSONObject();
            result.put("ds_graph_js：",insertResult);

            Map<String, List<JSONObject>> groupedMap = new HashMap<>();

            for (int i = 0; i < newJsonArray.size(); i++) {
                JSONObject jsonObject2 = newJsonArray.getJSONObject(i);
                String nat = jsonObject2.getString("fault_area_name");

                // 根据nat分组
                if (!groupedMap.containsKey(nat)) {
                    groupedMap.put(nat, new ArrayList<>());
                }
                groupedMap.get(nat).add(jsonObject2);


            }



            for (String key : NRMConstants.FAULT_AREA_NAME.keySet()) {
                if(groupedMap.keySet().contains(key)){

                    String Shardingcode = NRMConstants.FAULT_AREA_NAME.get(key);
                    List Natfaultorder = groupedMap.get(key);
                    if(Natfaultorder.size()>0){

                        JSONObject listtosave = new JSONObject();



                        listtosave.put("list",Natfaultorder);
                        //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
//                        System.out.println("listtosave"+listtosave);

                        JSONObject insertResult2 = faultHologramDao.tb_alarm_data_all_batch_insert(listtosave,Shardingcode);
//                        System.out.println(key+"插入结果"+insertResult2);



                        result.put(Shardingcode,insertResult2);
                    }
                }
            }



            return result;








        } else {
            // 处理不成功的响应
//            System.out.println("Request failed. Status code: " + response.getStatusCode());

            JSONObject result = new JSONObject();
            result.put("result","查询百川查询告警失败");
            return result;

        }

    }

//
//    @PostMapping("/auto-FaultInfluence")
//    public JSONObject autoFaultInfluence(){
//
//        //TODO：插入告警信息之后，同时进行影响分析，插入影响的接入号清单到这个表  fault_alarm_influence_data
////            INSERT INTO fault_alarm_influence_data (fault_bill_id, alarm_id, city, accs_nbr_no, create_time)
////            VALUES (123456, 789012, 'New York', 'ACCS123456', CURRENT_TIMESTAMP);
////            SELECT * FROM fault_alarm_influence_data WHERE fault_bill_id = 123456;
//
//        //todo:step1 首先把告警拿出来分析
//
//
//
//        List<String> ponPortList =  extractGroupedPonPortsFromJsonArray(newJsonArray);
//        //得到IP地址
//
//
//        System.out.println("ponPortList"+ponPortList);
//
////todo：格式应该是如下样式的：
////            {
////                "poninfo": [
////                {
////                    "ip": "************",
////                        "pon_code": "00/11/04"
////                },
////                {
////                    "ip": "************",
////                        "pon_code": "00/04/16"
////                }
////  ]
////            }
//
//
////todo:这里需要进一步完成逻辑，需要从单个故障+多个告警入手，开展影响分析，并缓存到库里
//        JSONObject request;
//        String areaCode = request.getString("city");
//        String shardingCode = "ds_bc_o3_" + areaCode.toLowerCase();
//        JSONArray poninfo = request.getJSONArray("poninfo");
//        GraphRequestBuilder builder = new GraphRequestBuilder();
//        GraphRequest graphRequest = builder.setApiId("pon_2_access_device").setShardingCode(shardingCode)
//                .appendWhereBodyItem(new GraphWhereBodyItem("pon_port", "poninfo", poninfo)).build();
//        Graph graph = graphApiService.doApi(graphRequest);
//        JSONObject data = graphService.getData(graph);
//        graphService.buildTree(data, "PON端口", "接入设备", "access_device");
//        graphService.buildTree(data, "接入设备", "cfs", "cfs");
//        List<JSONObject> pontree = graphService.filterNodeByLabel(data, "PON端口");
//
//
//        JSONObject result = new JSONObject();
//        return result;
//    }
    // 正则表达式，用于验证IPv4地址
    private static final String IP_V4_PATTERN =
            "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                    "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                    "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                    "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";



    private static final Pattern pattern = Pattern.compile(IP_V4_PATTERN);

    // 检查IP地址是否合规
    public static boolean isValidIPv4(String ip) {
        if (ip == null) {
            return false;
        }
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }



    //最后用python实现调用告警清单，然后循环调用存影响分析结果

    //还需要再写一个接口，查询不在tb_rr_alarm_user_accs 里面的告警清单，


    //根据指定的alarm的id，分析影响，并保存到   tb_rr_alarm_user_accs


    /**
     * @params : {
     *
     * 	"alarm_bill_id": 217022919
     * }
     * 也支持输入多个告警ID，如
     * @params : {
     *
     *       	"alarm_bill_ids": [217022919,226839236]
     *       }
     * */

    @PostMapping("/savetotb_rr_alarm_user_accs")
    public JSONObject savetotb_rr_alarm_user_accs(@RequestBody JSONObject params) {
        JSONObject alarminfo = faultHologramDao.query_alarm_data_all(params  ,"ds_graph_js");
        //根据告警信息取pon口
//        System.out.println("alarminfo"+alarminfo);
        JSONArray alarm  = alarminfo.getJSONArray("value");

        //抽取告警信息里面的pon口
        JSONArray groupedPonPorts =  extractGroupedPonInfo(alarm);

//        System.out.println("groupedPonPorts"+groupedPonPorts);

        //todo:下面根据pon口查影响分析：


        for (int i = 0; i < groupedPonPorts.size(); i++) {
            JSONObject cityObject = groupedPonPorts.getJSONObject(i);
            String shardingCode = cityObject.getString("city");
            JSONArray alarmBillIdArray = cityObject.getJSONArray("alarm_bill_idArray");
            JSONArray poninfo = cityObject.getJSONArray("poninfo");

            for (int j = 0; j < alarmBillIdArray.size(); j++) {
                String alarmBillId = alarmBillIdArray.getString(j);
                boolean found = false;

                for (int k = 0; k < poninfo.size(); k++) {
                    JSONObject ponInfoItem = poninfo.getJSONObject(k);
                    if (ponInfoItem.getString("alarm_bill_id").equals(alarmBillId)) {
                        found = true;
                        // 实际的查询操作，并处理结果
                        JSONObject param = ponInfoItem; // 构建你的查询参数
                        // 这里假设shardingCode是你需要传递的参数之一，根据实际情况调整
                        JSONObject response = influenceAnalysisDao.query_cfs_influence_by_pon_code(param, shardingCode);

                        JSONArray cfs_influence = response.getJSONArray("cfs_influence");
                        if(ObjectUtil.isNotEmpty(cfs_influence) ){
                            for(int l=0;l<cfs_influence.size();l++){
                                JSONObject cfs = cfs_influence.getJSONObject(l);
                                cfs.put("alarmBillId",alarmBillId);
                            }
                        }


//                        System.out.println("response" + response);

                        JSONObject insertGraphdb = faultHologramDao.batch_insert_tb_rr_alarm_accs_nbr(response,"ds_graph_js");
                        JSONObject insertshardingCode = faultHologramDao.batch_insert_tb_rr_alarm_accs_nbr(response,shardingCode);

//                        System.out.println("insertGraphdb"+insertGraphdb);
//                        System.out.println("insertshardingCode"+insertshardingCode);

                        // 这里执行你的后续写入操作，比如保存到数据库或者其他处理
                        // saveResponse(response);

                        break;
                    }
                }

                if (!found) {
                    // 如果没有找到对应的 alarm_bill_id，生成一个空的 JSONObject 并放入 alarm_bill_id
                    //  相当于记录已经对这个alarm_bill_id做过了处理
                    JSONObject emptyResponse = new JSONObject();

                    JSONArray cfs_influence = new JSONArray();
                    JSONObject emptyObject = new JSONObject();

                    emptyObject.put("alarmBillId",alarmBillId);

                    cfs_influence.add(emptyObject);
                    emptyResponse.put("cfs_influence", cfs_influence);

//                    System.out.println("emptyResponse" + emptyResponse);
                    // 这里执行你的后续写入操作，比如保存到数据库或者其他处理
                    // saveResponse(emptyResponse);

                    JSONObject insertGraphdb = faultHologramDao.batch_insert_tb_rr_alarm_accs_nbr(emptyResponse,"ds_graph_js");
                    JSONObject insertshardingCode = faultHologramDao.batch_insert_tb_rr_alarm_accs_nbr(emptyResponse,shardingCode);


//                    System.out.println("insertGraphdb"+insertGraphdb);
//                    System.out.println("insertshardingCode"+insertshardingCode);
                }
            }
        }



        JSONObject result = new JSONObject();

        result.put("groupedPonPorts",groupedPonPorts);
        return result;


    }



    // 从userLabel中提取IP地址
    public static String extractIPFromUserLabel(String userLabel) {
        // 使用正则表达式从userLabel中找到IP地址
        String ipPattern = "\\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b";
        Pattern pattern = Pattern.compile(ipPattern);
        Matcher matcher = pattern.matcher(userLabel);
        if (matcher.find()) {
            return matcher.group();
        }
        return null; // 没有找到IP地址
    }


    //根据告警信息，抽取其中的的pon口、IP地址、地市信息，如果合规的话，会进行分组，最终得到一个各个地市的ip、pon的jsonarry
    public static JSONArray extractGroupedPonInfo(JSONArray alarmTableData) {
        JSONObject result = new JSONObject();
        JSONArray poninfo = new JSONArray();
        result.put("poninfo", poninfo);

        // 定义正则表达式来检查PON端口格式
        Pattern ponRegex = Pattern.compile("^(\\d{2})/(\\d{2})/(\\d{2})$");
        // 定义正则表达式来从告警信息中提取PON端口
        Pattern alarmPonRegex = Pattern.compile("<<(\\d+)/(\\d+)/(\\d+)>>");

        Map<String, List<JSONObject>> groupedByCity = new HashMap<>();

        Map<String, List<String>> alarmidArray = new HashMap<>();

        //保存本次提取的全部的alarmid，用于记录哪些告警已经进行了分析

        for (int i = 0; i < alarmTableData.size(); i++) {

            JSONObject item = alarmTableData.getJSONObject(i);
            String alarm_bill_id = item.getString("id");

            String ip = item.getString("ip地址");
            String userLabel = item.getString("设备名称");

            //判断ip地址是否合规，并且如果不合规的话，就从userlabel里面提取IP地址
            if (!isValidIPv4(ip)) {
                System.out.println("IP地址不合规，从userLabel中提取IP地址...");
                String extractedIP = extractIPFromUserLabel(userLabel);
                if (extractedIP != null) {
                    System.out.println("提取到的IP地址: " + extractedIP);
                    ip = extractedIP;
                } else {
                    System.out.println("未在userLabel中找到合规的IP地址。");
                }
            } else {
                System.out.println("IP地址合规: " + ip);
            }
            if (!isValidIPv4(ip)){
                continue; // 没拿到IP地址，直接跳过本次循环
            }

            //下面拿pon口信息
            String portName = item.getString("端口名");
            String alarmSummary = item.getString("告警概要");
            String alarmCity = item.getString("alarmCity"); // 假设alarmCity字段存在

            // 尝试将端口名转换成"00/04/06"这样的格式
            String ponCode = portName.split("\\|")[0].replaceAll("(\\d+)", "00$1").replaceAll("/([^/])", "/0$1").replaceAll("//", "/");

            // 检查PON端口格式是否符合要求
            Matcher ponMatcher = ponRegex.matcher(ponCode);
            if (!ponMatcher.matches()) {
                // 格式不合规，尝试从告警概要中提取PON端口
                if (alarmSummary != null) {
                    Matcher alarmPonMatcher = alarmPonRegex.matcher(alarmSummary);
                    if (alarmPonMatcher.find()) {
                        // 重新构造PON端口格式
                        ponCode = String.format("%02d/%02d/%02d",
                                Integer.parseInt(alarmPonMatcher.group(1)),
                                Integer.parseInt(alarmPonMatcher.group(2)),
                                Integer.parseInt(alarmPonMatcher.group(3))
                        );

                        // 再次检查格式
                        ponMatcher = ponRegex.matcher(ponCode);
                        if (!ponMatcher.matches()) {
                            continue; // 如果还是不合规，跳过这条记录
                        }
                    } else {
                        // 尝试匹配另一种格式：框号x, 槽位号y, 端口号z
                        Matcher frameSlotPortMatcher = Pattern.compile("框号(\\d+),\\s*槽位号(\\d+),\\s*端口号(\\d+)").matcher(alarmSummary);
                        if (frameSlotPortMatcher.find()) {
                            ponCode = String.format("%02d/%02d/%02d",
                                    Integer.parseInt(frameSlotPortMatcher.group(1)),
                                    Integer.parseInt(frameSlotPortMatcher.group(2)),
                                    Integer.parseInt(frameSlotPortMatcher.group(3))
                            );

                            // 再次检查格式
                            ponMatcher = ponRegex.matcher(ponCode);
                            if (!ponMatcher.matches()) {
                                continue; // 如果还是不合规，跳过这条记录
                            }
                        } else {
                            continue; // 如果匹配失败，跳过这条记录
                        }
                    }
                }
            }

            // 处理特定城市的PON代码格式（例如：去掉前两位数字和斜杠）
            if ("nt".equals(alarmCity)) {
                ponCode = ponCode.substring(3);
            }

            // 将提取的信息添加到结果对象中
            JSONObject ponInfo = new JSONObject();

            ponInfo.put("ip_addr", ip);
            ponInfo.put("pon_code", ponCode);
            ponInfo.put("alarm_bill_id",alarm_bill_id);

            //按照城市分组
            String fault_area_name = item.getString("fault_area_name");
            String city = NRMConstants.FAULT_AREA_NAME.get(fault_area_name);

            groupedByCity.computeIfAbsent(city, k -> new ArrayList<>()).add(ponInfo);
            alarmidArray.computeIfAbsent(city, k -> new ArrayList<>()).add(alarm_bill_id);

        }



        // 构建最终的JSONArray
        JSONArray resultArray = new JSONArray();
        for (Map.Entry<String, List<JSONObject>> entry : groupedByCity.entrySet()) {
            JSONObject cityObj = new JSONObject();
            cityObj.put("city", entry.getKey());

            List alarm_bill_idArray = alarmidArray.get(entry.getKey());

            // 创建一个包含所有ponInfo对象的JSONArray
            JSONArray ponInfoArray = new JSONArray(entry.getValue());
            cityObj.put("poninfo", ponInfoArray);
            cityObj.put("alarm_bill_idArray",alarm_bill_idArray);

            resultArray.add(cityObj);
        }

        return resultArray;


    }





    //插入日志
    @PostMapping("/insert_into_tb_fault_hologram_log")
    @LogAnnotation(interfaceName="障碍全息视图 insert_into_tb_fault_hologram_log 接口-插入日志")
    public void insert_into_tb_fault_hologram_log(@RequestBody JSONObject request) {

        JwtUser jwtUser = SecurityContext.getJwtUser();

        System.out.println(jwtUser);
        request.put("operator",jwtUser.getUsername());
        faultHologramDao.insert_into_tb_fault_hologram_log(request,"ds_graph_js");


    }




    //根据告警查资源
    @PostMapping("/query_res_by_alarm")
    @LogAnnotation(interfaceName="障碍全息视图 query_res_by_alarm 接口-根据告警查资源")
    public JSONObject query_res_by_alarm(@RequestBody JSONObject request)  {



        String areaCode = request.getString("city");
        String shardingCode = "ds_bc_o3_"+ areaCode.toLowerCase();

//        System.out.println("shardingCode"+shardingCode);
        JSONArray poninfo = request.getJSONArray("poninfo");
        JSONObject result = obsLocationService.queryObstaclepointsbyalarm(poninfo,shardingCode);

        return result;

    }




    //查询申告信息
    @PostMapping("/querycomplaint")
    public JSONObject query_complaint(@RequestBody JSONObject params)  {


//        System.out.println("params"+params);
        //查询申告接口
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/gxpt_user/pon/common/yybz/gd10000DetailDataQL";

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> request = new HttpEntity<>(params, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

//        System.out.println("response"+response);
//        // 打印响应体
//        System.out.println(response.getBody());

        JSONObject result = JSON.parseObject(response.getBody());

//        System.out.println("result"+result);



        return result;

    }



    //查询申告信息
    @PostMapping("/auto-savecomplaint")
    public JSONObject auto_savecomplaint( @RequestBody JSONObject request)  {


        LocalDateTime now = LocalDateTime.now();

        LocalDateTime tenMinutesAgo;

        if(ObjectUtil.isNotNull(request.getInteger("hours"))){

            tenMinutesAgo  = now.minus(request.getInteger("hours"), ChronoUnit.HOURS);

        }
        else{
            tenMinutesAgo = now.minus(24, ChronoUnit.HOURS);
        }

        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化时间
        String formattedstartTime = tenMinutesAgo.format(formatter);
        String formattedendTime = now.format(formatter);

        JSONObject params = new JSONObject();

        if(ObjectUtil.isNotNull(request.getString("startTime"))){

            formattedstartTime  = request.getString("startTime");

        }

        if(ObjectUtil.isNotNull(request.getString("endTime"))){

            formattedendTime  = request.getString("endTime");

        }


        if(ObjectUtil.isNotNull(request.getJSONArray("nat"))){

            params.put("nat", request.getJSONArray("nat"));

        }else{
            params.put("nat",NRMConstants.NAT_ABBREVIATION_MAP.keySet());

        }



        params.put("business","kd");
        params.put("startTime",formattedstartTime);
        params.put("endTime",formattedendTime);

        JSONObject request1 = new JSONObject();
        request1.put("params",params);


//        System.out.println("params"+request1);
        //查询申告接口
        String url= "http://sjgxpt.telecomjs.com:8090/dataway/api/gxpt_user/pon/common/yybz/gd10000DetailDataQL";

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("appKey",appKey);
        headers.set("appSecret",appSecret);

        // 封装请求头和请求体
        HttpEntity<JSONObject> requestBody = new HttpEntity<>(request1, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestBody, String.class);


        JSONObject responseJson = JSON.parseObject(response.getBody());

        JSONArray originalArray = responseJson.getJSONArray("value");


        //根据这个NAT_ABBREVIATION_MAP进行循环
        JSONObject result = new JSONObject();

        JSONObject listtosave1 = new JSONObject();

        listtosave1.put("list",originalArray);

        JSONObject insertResult1 = faultHologramDao.insert_into_tb_complaints(listtosave1,"ds_graph_js");
        result.put("ds_graph_js",insertResult1);


        Map<String, List<JSONObject>> groupedMap = new HashMap<>();

        for (int i = 0; i < originalArray.size(); i++) {
            JSONObject jsonObject = originalArray.getJSONObject(i);
            String nat = jsonObject.getString("nat");

            // 根据nat分组
            if (!groupedMap.containsKey(nat)) {
                groupedMap.put(nat, new ArrayList<>());
            }
            groupedMap.get(nat).add(jsonObject);


        }



        for (String key : NRMConstants.NAT_ABBREVIATION_MAP.keySet()) {
            if(groupedMap.keySet().contains(key)){

                String Shardingcode = NRMConstants.NAT_ABBREVIATION_MAP.get(key);
                List NatComplaintlist = groupedMap.get(key);
                if(NatComplaintlist.size()>0){

                    JSONObject listtosave = new JSONObject();



                    listtosave.put("list",NatComplaintlist);
                    //这个接口插入完之后，还会自动更新告警的bill_id，后续关联查询会比较快
//                    System.out.println("listtosave"+listtosave);

                    JSONObject insertResult = faultHologramDao.insert_into_tb_complaints(listtosave,Shardingcode);
//                    System.out.println(key+"插入结果"+insertResult);

                    result.put(key,insertResult);
                }

            }

        }



        return result;

    }





    //查询申告信息
    @PostMapping("/query_cache_tb_complaints")
    public JSONObject query_cache_tb_complaints(@RequestBody JSONObject params)  {


        JSONObject request = params.getJSONObject("params");


        JSONObject result = faultHologramDao.query_cache_tb_complaints(request  ,"ds_graph_js");

        return result;




    }


    //查询申告信息
    @PostMapping("/query_olt_position")
    public JSONObject query_olt_position(@RequestBody JSONObject params)  {


        JSONObject request = params.getJSONObject("params");

        JSONObject result = faultHologramDao.query_olt_position(request  ,"ds_odso_"+ request.getString("city"));

        return result;




    }


    /***
     *
     *
     * @param params =
     * {
     * 	"params": {
     *             		"business": "kd",
     *                     "nats": ["徐州分公司"],
     *                     "startTime": "2024-11-18 00:00:00",
     *                     "endTime": "2024-11-18 20:43:00"
     *         }
     * }
     * 入参样例
     *
     *
     * */


    //自动根据申告数据汇聚之后的结果，判断是否有必要推送到微信群，如果推送的话，
    @PostMapping("/query_complaints_collect")
    public JSONObject query_complaints_collect(@RequestBody JSONObject params)  {


        JSONObject complaints = query_complaint(params);

        log.info(complaints.toString());
        JSONObject result = analyzeComplaints(complaints);
        System.out.println(result.toJSONString());



        //下面发送微信
        JSONObject weixinMessage = new JSONObject();

        StringBuilder formattedString = new StringBuilder();

        for (String key : result.keySet()) {
            JSONObject value = result.getJSONObject(key);
            int count = value.getIntValue("count");
            String detailNums = value.getString("detail_nums");

            formattedString.append("地区：")
                    .append(key)
                    .append("  申告数：")
                    .append(count)
                    .append(" ，申告接入号：")
                    .append(detailNums)
                    .append("\n");
        }


        weixinMessage.put("alarm_content",formattedString.toString());
        weixinMessage.put("level",399);
        weixinMessage.put("type",1);


        sendMessageWeixin(weixinMessage);

        return result;

    }
    static class Pair {
        public int count;
        public List<String> detailNums;

        public Pair(int count, List<String> detailNums) {
            this.count = count;
            this.detailNums = detailNums;
        }
    }

    public static JSONObject analyzeComplaints(JSONObject jsonObject) {
        JSONArray valueArray = jsonObject.getJSONArray("value");

        Map<String, Pair> addressPrefixMap = new HashMap<>();

        // 统计每个地址前缀的出现次数以及对应的detail_num列表
        for (int i = 0; i < valueArray.size(); i++) {
            JSONObject complaint = valueArray.getJSONObject(i);
            if(ObjectUtil.isNull(complaint.getString("address"))){
                continue;
            }
            String addressPrefix = extractPrefix(complaint.getString("address"));
            String detailNum = complaint.getString("detail_num");

            if (!addressPrefixMap.containsKey(addressPrefix)) {
                addressPrefixMap.put(addressPrefix, new Pair(1, new ArrayList<>()));
            } else {
                Pair pair = addressPrefixMap.get(addressPrefix);
                pair.count++;
            }
            addressPrefixMap.get(addressPrefix).detailNums.add(detailNum);
        }

        // 将结果转换为JSONObject
        JSONObject result = new JSONObject();
        for (Map.Entry<String, Pair> entry : addressPrefixMap.entrySet()) {
            String addressPrefix = entry.getKey();
            Pair pair = entry.getValue();
            if(pair.count<=2){

                continue;
            }
            // 将detail_num列表转换为逗号分隔的字符串
            String detailNumsStr = String.join(",", pair.detailNums);
            result.put(addressPrefix, JSON.toJSON(new JSONObject() {{
                put("count", pair.count);
                put("detail_nums", detailNumsStr);
            }}));
        }

        return result;
    }



    private static String extractPrefix(String address) {
        // 找到第一个数字的位置
        int firstDigitIndex = -1;
        if(ObjectUtil.isNotNull(address)){
            for (int i = 0; i < address.length(); i++) {
                if (Character.isDigit(address.charAt(i))) {
                    firstDigitIndex = i;
                    break;
                }
            }
        }

        // 提取前缀
        return firstDigitIndex == -1 ? address : address.substring(0, firstDigitIndex);
    }



    //发送微信消息接口

    /**
     *
     * @param params {
     * "alarm_content":"告警内容"，
     * "level":399，
     * "type":1
     * }
     *               或者 发送图片：
     *
     *               {
     * "level":399
     * "image":"/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/",
     *  "type":2
     * }
     *
     *               或者  发送地址链接，打开可以直接跳转地图
     *
     *        {
     *   "alarm_content": "障碍位置：https://maps.apple.com/?ll=32.079006,118.784398  点击链接跳转",
     *   "level": 399,
     *   "type": 1
     * }
     *
     * */
    @PostMapping("/sendMessageWeixin")
    public JSONObject sendMessageWeixin(@RequestBody JSONObject params) {
        return messageService.sendWeixinMessage(params);
    }


    /**
     * 获取字典数据，包括地市和区县信息
     * 用于障碍定位页面的区域筛选功能
     */
    @PostMapping("/dictionary")
    @LogAnnotation(interfaceName="障碍定位南通视图 dictionary 接口-获取字典数据")
    public ResponseEntity<JSONObject> get_dictionary(@RequestBody(required = false) JSONObject params) {
        log.info("REST request to get dictionary : {}", params);

        JSONObject meta = new JSONObject();

        try {
            // ========== 获取所有区域数据 ==========        // 创建空的Region查询参数，表示查询所有区域
            Region regionParam = new Region();
            List<Region> regionList = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);

            // ========== 筛选地市级数据 ==========
            List<Region> cityList = new ArrayList<>();
            for(int i = 0; i < regionList.size(); i++){
                // 筛选条件：area_level_id = 100700 (地市级行政区划)
                if(regionList.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                    cityList.add(regionList.get(i));
                }
            }
            meta.put("cityList", cityList);

            // ========== 根据选择的地市获取区县数据 ==========
            if (params != null) {
                // 从前端请求参数中获取当前选择的地市名称
                String currentCityName = params.getString("ds");
                if (ObjectUtil.isNotEmpty(currentCityName)) {
                    // 注意：地市名称可能需要模糊匹配，"南通市"和"南通"是等价的
                    Region currentCity = cityList.stream()
                            .filter(c -> c.getName().contains(currentCityName) || currentCityName.contains(c.getName()))
                            .findFirst()
                            .orElse(null);

                    if (currentCity != null) {
                        // 获取当前地市的ID
                        String currentCityId = currentCity.getId();

                        // 筛选该地市下的所有区县
                        // 筛选条件：parent_id = 当前地市ID
                        List<Region> leafRegionList = regionList.stream()
                                .filter(r -> ObjectUtil.isNotEmpty(r.getParentId()) &&
                                        r.getParentId().equals(currentCityId))
                                .collect(Collectors.toList());

                        meta.put("leafRegionList", leafRegionList);

                        log.info("找到 city: {}, ID: {}, Districts count: {}",
                                currentCity.getName(), currentCityId, leafRegionList.size());
                    } else {
                        log.warn("未找到: {}", currentCityName);
                        meta.put("leafRegionList", new ArrayList<Region>());
                    }
                } else {
                    meta.put("leafRegionList", new ArrayList<Region>());
                }
            } else {
                meta.put("leafRegionList", new ArrayList<Region>());
            }

            log.info("成功获取地区数据. Cities: {}, Regions: {}",
                    cityList.size(), meta.getJSONArray("leafRegionList").size());

            return ResponseEntity.ok().body(meta);

        } catch (Exception e) {
            log.error("获取地区数据出错：", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "获取地区数据失败");
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
