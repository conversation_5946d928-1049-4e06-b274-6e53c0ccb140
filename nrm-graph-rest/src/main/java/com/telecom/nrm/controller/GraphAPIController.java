package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphResponse;
import com.telecom.nrm.service.GraphApiService;
import com.telecom.nrm.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Date;

@RestController
@RequestMapping("/api/graph-api")
@Slf4j
public class GraphAPIController {
    @Autowired
    GraphApiService graphApiService;

    @Autowired
    GraphService graphService;

    @PostMapping("")
    public ResponseEntity<GraphResponse> doApi(@RequestBody GraphRequest request) throws URISyntaxException {
        log.debug("REST request to save entity : {}", request);
        GraphResponse graphResponse = new GraphResponse();
        try {
                Graph graph=graphApiService.doApi(request);
            graphResponse.setData(graph);
            graphResponse.setCode("200");
            graphResponse.setMessage("ok");
        }catch (Exception ex) {
            log.error(ex.getMessage(),ex);
            graphResponse.setCode("500");
            graphResponse.setMessage(ex.getMessage());
        }
        return ResponseEntity.ok(graphResponse);
    }


    @PostMapping("/data")
    public ResponseEntity<GraphResponse> doApiData(@RequestBody GraphRequest request) throws URISyntaxException {
        log.debug("REST request to save entity : {}", request);
        GraphResponse graphResponse = new GraphResponse();
        try {
            Graph graph=graphApiService.doApi(request);
            JSONObject resultData = graphService.getData(graph);
            graphResponse.setData(resultData);
            graphResponse.setCode("200");
            graphResponse.setMessage("ok");
        }catch (Exception ex) {
            log.error(ex.getMessage(),ex);
            graphResponse.setCode("500");
            graphResponse.setMessage(ex.getMessage());
        }
        return ResponseEntity.ok(graphResponse);
    }

    

}
