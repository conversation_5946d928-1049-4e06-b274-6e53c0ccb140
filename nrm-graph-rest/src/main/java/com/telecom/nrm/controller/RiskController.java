package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/risk-api")
@Slf4j
public class RiskController {

    @Autowired
    CustLinkService custLinkService;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CircuitPairService circuitPairService;

    @Autowired
    OptGroupService optGroupService;

    @Autowired
    CustViewMemberService custViewMemberService;


    @Autowired
    LifeCircuitService lifeCircuitService;
    //@LogAnnotation( interfaceName="光路组风险检测")
    @PostMapping("/risk-analyze")
    public JSONObject riskAnalyze(@RequestBody JSONObject request){
        // JSONObject result =new JSONObject();
        // List<JSONObject> pair_list_data =   new ArrayList<>();
        //  log.debug("123344");

        String type = request.getString("type");

        JSONObject result = new JSONObject();

        if (type.equals("circuit-pair")) {
            result =  circuitPairService.risk_analyze(request);
        }else if (type.equals("opt-road-group")) {
            result =  optGroupService.risk_analyze(request);
        }else if (type.equals("cust-view-member")) {
            result =  custViewMemberService.risk_analyze(request);
        }
        return result;
    }

    @LogAnnotation( interfaceName="光路组风险检测结果查询")

    @PostMapping("/risk-analyze-get")
    public JSONObject riskAnalyzeGet(@RequestBody JSONObject request){
        // JSONObject result =new JSONObject();
        // List<JSONObject> pair_list_data =   new ArrayList<>();
        //  log.debug("123344");

        String type = request.getString("type");

        JSONObject result = new JSONObject();

        if (type.equals("circuit-pair")) {
            result =  circuitPairService.risk_analyze_get(request);
        }else if (type.equals("opt-road-group")) {
            result =  optGroupService.risk_analyze_previous(request);
        }else if (type.equals("cust-view-member")) {
            result =  custViewMemberService.risk_analyze(request);
        }
        return result;
    }



    @PostMapping("/white-insert")
    public Integer pair_circuit_input_fiber_result_corr_insert(@RequestBody JSONObject jsonObject){
        String type = jsonObject.getString("type");
        if (type.equals("cust-view-member")) {
            JSONObject object = jsonObject.getJSONObject("object");
            jsonObject.put("code", object.getString("code"));
            jsonObject.put("name", object.getString("name"));
            return lifeCircuitService.white_insert(jsonObject);
        }
        jsonObject.put("pipleline_code", jsonObject.getJSONObject("object").getString("code"));
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/white-delete")
    public Integer pipleline_white_delete(@RequestBody JSONObject jsonObject){
        String code = jsonObject.getJSONObject("object").getString("code");
        String type = jsonObject.getString("type");
        if(code.contains("----已忽略")){
            code = code.substring(0, code.indexOf("----已忽略"));
        }
        if (type.equals("cust-view-member")) {
            JSONObject param_white =  new JSONObject();
            param_white.put("code", code.trim());
            return lifeCircuitService.white_delete(param_white);
        }
        jsonObject.put("pipleline_code", code);
        return custLinkInterfaceDao.pair_circuit_input_fiber_result_corr_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }
}
