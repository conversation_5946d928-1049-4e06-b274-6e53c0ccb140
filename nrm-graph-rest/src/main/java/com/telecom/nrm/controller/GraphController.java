package com.telecom.nrm.controller;


import com.telecom.nrm.dao.GraphDao;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.Node;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/graph")
public class GraphController {

    @Autowired
    GraphDao graphDao;

    @GetMapping("list-neighbors")
    public Graph getNeighbors (@RequestParam(required = false) Map exampleMap) {
        String id = exampleMap.get("fromId").toString();
        Node node = new Node();
        node.setId(id);
        Graph g = graphDao.getNeighbors(node);
        return g;
    }

}
