package com.telecom.nrm.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.GraphSceneDao;
import com.telecom.nrm.domain.NRMConstants;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/api/province")
public class ProvinceController {

    @Resource
    private GraphSceneDao graphSceneDao;

    /**
     * 接入资源数据一致率
     * @return
     */
    @RequestMapping(value ="/getRS", method = RequestMethod.GET , produces ="text/html;charset=UTF-8")
    public @ResponseBody String getRS() {
        try {
            SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            String time = s.format(date);
            JSONObject province = new JSONObject();
            province.put("cycleType","M");
            province.put("town","");
            province.put("reportDate",time);
            province.put("city","");
            province.put("provice","321122930000000000000014");
            province.put("district","");
            province.put("InterfaceName","businessMetricsFromPlatformToGroupsToProvice");
            province.put("type","58");
            //做成api接口 以防补推
            JSONObject year = graphSceneDao.getYear(NRMConstants.SHARDING_GRAPH_DB);
            String data1 = year.getString("data");
            data1 = data1.substring(1, data1.length()-1);
            JSONObject js = JSONObject.parseObject(data1);
            province.put("cycle",js.getString("yearMonth"));
            List<Map<String,String>> list = new ArrayList<>();
            Map<String,String> map = new HashMap<>();
            JSONObject rs = graphSceneDao.getRs(NRMConstants.SHARDING_GRAPH_DB);
            String data = rs.getString("data");
            data = data.substring(1, data.length()-1);//去掉字符串开头和结尾的大括号
            JSONObject jsonObject = JSONObject.parseObject(data);
            String a=jsonObject.getString("percentage");
            map.put("Rs_ResourceConsistency",jsonObject.getString("percentage"));
            list.add(map);
            province.put("dataMap",list);
            Map<String,String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-APP-ID","85c792f967cd7872f85ed5ed702504c9");
            headers.put("X-APP-KEY","78bed6a0ee8680b4cc39da3972e7fb4e");
            HttpResponse response = HttpRequest.post("http://jsjteop.telecomjs.com:8764/jseop/oss/ProvinceToCTPropPostInterface")
                    .headerMap(headers,false)
                    .body(province.toJSONString())
                    .timeout(5*60*1000)
                    .execute();
            return "接入资源数据一致率: "+a+"<br>"+"入参: "+province+"<br>"+"出参: "+"<br>"+response.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 中继资源数据一致率
     * @return
     */
    @RequestMapping(value ="/getIP", method = RequestMethod.GET , produces ="text/html;charset=UTF-8")
    public @ResponseBody String getIP() {
        try {
            SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            String time = s.format(date);
            JSONObject province = new JSONObject();
            province.put("cycleType","M");
            province.put("town","");
            province.put("reportDate",time);
            province.put("city","");
            province.put("provice","321122930000000000000014");
            province.put("district","");
            province.put("InterfaceName","businessMetricsFromPlatformToGroupsToProvice");
            province.put("type","90");
            JSONObject year = graphSceneDao.getYear(NRMConstants.SHARDING_GRAPH_DB);
            String data1 = year.getString("data");
            data1 = data1.substring(1, data1.length()-1);
            JSONObject js = JSONObject.parseObject(data1);
            province.put("cycle",js.getString("yearMonth"));
            List<Map<String,String>> list = new ArrayList<>();
            Map<String,String> map = new HashMap<>();
            JSONObject rs = graphSceneDao.getIP(NRMConstants.SHARDING_GRAPH_DB);
            String data = rs.getString("data");
            data = data.substring(1, data.length()-1);//去掉字符串开头和结尾的大括号
            JSONObject jsonObject = JSONObject.parseObject(data);
            String a=jsonObject.getString("percentage");
            map.put("IP_NMSDataConsistency",a);
            list.add(map);
            province.put("dataMap",list);
            Map<String,String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-APP-ID","85c792f967cd7872f85ed5ed702504c9");
            headers.put("X-APP-KEY","78bed6a0ee8680b4cc39da3972e7fb4e");
            HttpResponse response = HttpRequest.post("http://jsjteop.telecomjs.com:8764/jseop/oss/ProvinceToCTPropPostInterface")
                    .headerMap(headers,false)
                    .body(province.toJSONString())
                    .timeout(5*60*1000)
                    .execute();
            return "中继资源数据一致率: "+a+"<br>"+"入参: "+province+"<br>"+"出参: "+"<br>"+response.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
