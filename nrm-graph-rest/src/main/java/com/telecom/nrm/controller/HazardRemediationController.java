package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.service.S3Service;
import com.telecom.nrm.service.HazardRemediationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.UUID;

/**
 * 隐患整改控制器
 * 处理隐患整改相关的API请求，包括文件上传功能
 */
@RestController
@RequestMapping("/api/hazard-remediation")
public class HazardRemediationController {
    
    private static final Logger log = LoggerFactory.getLogger(HazardRemediationController.class);
    
    private static final String BUCKET_NAME = "yuanfeng";
    private static final String FOLDER_PATH = "shuangluyou/hazard-remediation/";
    
    @Autowired
    private S3Service s3Service;

    @Autowired
    private HazardRemediationService hazardRemediationService;

    // 默认分片代码，可以根据实际情况调整
    private static final String DEFAULT_SHARDING_CODE = "ds_bc_o3_xz";

    /**
     * 标准化shardingCode格式
     * @param shardingCode 原始shardingCode
     * @return 标准化后的shardingCode
     */
    private String normalizeShardingCode(String shardingCode) {
        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            return DEFAULT_SHARDING_CODE;
        }

        // 如果已经是正确格式，直接返回
        if (shardingCode.startsWith("ds_bc_o3_")) {
            return shardingCode.toLowerCase();
        }

        // 如果是旧格式，使用默认值
        if ("SHARDING_GRAPH_DB".equals(shardingCode)) {
            log.warn("检测到旧格式shardingCode: {}，使用默认值: {}", shardingCode, DEFAULT_SHARDING_CODE);
            return DEFAULT_SHARDING_CODE;
        }

        // 其他情况也使用默认值
        log.warn("未识别的shardingCode格式: {}，使用默认值: {}", shardingCode, DEFAULT_SHARDING_CODE);
        return DEFAULT_SHARDING_CODE;
    }

    /**
     * 创建隐患整改单
     */
    @PostMapping("/create")
    public ResponseEntity<JSONObject> createHazardRemediation(@RequestBody JSONObject param) {
        // 标准化shardingCode
        String shardingCode = normalizeShardingCode(param.getString("shardingCode"));

        log.info("创建隐患整改单，原始shardingCode: {}, 标准化后: {}", param.getString("shardingCode"), shardingCode);
        JSONObject result = hazardRemediationService.createHazardRemediation(param, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 保存隐患整改单（兼容前端调用）
     */
    @PostMapping("/save")
    public ResponseEntity<JSONObject> saveHazardRemediation(@RequestBody JSONObject param) {
        // 从请求参数中提取shardingCode，如果没有则使用默认值
        String shardingCode = param.getString("shardingCode");
        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            shardingCode = DEFAULT_SHARDING_CODE;
            log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
        }

        // 如果有ID则更新，否则创建
        if (param.containsKey("id") && param.getString("id") != null && !param.getString("id").trim().isEmpty()) {
            log.info("更新隐患整改单，ID: {}, shardingCode: {}", param.getString("id"), shardingCode);
            JSONObject result = hazardRemediationService.updateHazardRemediation(param, shardingCode);
            if (result.getBooleanValue("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        } else {
            log.info("创建隐患整改单，shardingCode: {}", shardingCode);
            JSONObject result = hazardRemediationService.createHazardRemediation(param, shardingCode);
            if (result.getBooleanValue("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        }
    }

    /**
     * 查询隐患整改单列表（分页）
     */
    @PostMapping("/query")
    public ResponseEntity<PageResponse<JSONObject>> queryHazardRemediationList(
            @RequestBody JSONObject param,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(defaultValue = "1") Integer currentPage) {
        try {
            // 从请求参数中提取shardingCode，如果没有则使用默认值
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            currentPage=param.getInteger("currentPage");
            pageSize=param.getInteger("pageSize");

            log.info("查询隐患整改单列表，shardingCode: {}, pageSize: {}, currentPage: {}",
                    shardingCode, pageSize, currentPage);
            PageResponse<JSONObject> result = hazardRemediationService.queryHazardRemediationList(
                    param, pageSize, currentPage, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询隐患整改单列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 查询隐患整改单详情（GET方式）
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<JSONObject> getHazardRemediationDetail(@PathVariable String id) {
        JSONObject result = hazardRemediationService.getHazardRemediationDetail(id, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 查询隐患整改单详情（POST方式，兼容前端调用）
     */
    @PostMapping("/get")
    public ResponseEntity<JSONObject> getHazardRemediationByPost(@RequestBody JSONObject param) {
        // 标准化shardingCode
        String shardingCode = normalizeShardingCode(param.getString("shardingCode"));

        String id = param.getString("id");
        if (id == null || id.trim().isEmpty()) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "缺少必要参数：id");
            return ResponseEntity.badRequest().body(errorResult);
        }

        log.info("查询隐患整改单详情，ID: {}, 原始shardingCode: {}, 标准化后: {}", id, param.getString("shardingCode"), shardingCode);
        JSONObject result = hazardRemediationService.getHazardRemediationDetail(id, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 更新隐患整改单（PUT方法）
     */
    @PutMapping("/update")
    public ResponseEntity<JSONObject> updateHazardRemediation(@RequestBody JSONObject param) {
        // 从请求参数中提取shardingCode，如果没有则使用默认值
        String shardingCode = param.getString("shardingCode");
        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            shardingCode = DEFAULT_SHARDING_CODE;
            log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
        }

        log.info("更新隐患整改单，shardingCode: {}", shardingCode);
        JSONObject result = hazardRemediationService.updateHazardRemediation(param, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 更新隐患整改单（POST方法，兼容前端调用）
     */
    @PostMapping("/update")
    public ResponseEntity<JSONObject> updateHazardRemediationByPost(@RequestBody JSONObject param) {
        // 标准化shardingCode
        String shardingCode = normalizeShardingCode(param.getString("shardingCode"));

        log.info("更新隐患整改单（POST），原始shardingCode: {}, 标准化后: {}", param.getString("shardingCode"), shardingCode);
        JSONObject result = hazardRemediationService.updateHazardRemediation(param, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 删除隐患整改单（路径参数方式）
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<JSONObject> deleteHazardRemediation(@PathVariable String id) {
        JSONObject result = hazardRemediationService.deleteHazardRemediation(id, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 删除隐患整改单（POST请求体方式，兼容前端调用）
     * 先在Java层验证业务逻辑，再执行删除操作
     */
    @PostMapping("/delete")
    public ResponseEntity<JSONObject> deleteHazardRemediationByPost(@RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode和id
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
                log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
            }

            String id = param.getString("id");
            if (id == null || id.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "缺少必要参数：id");
                return ResponseEntity.badRequest().body(errorResult);
            }

            // 获取操作人信息（可选，用于权限验证）
            String operator = param.getString("operator");

            log.info("删除隐患整改单，ID: {}, shardingCode: {}, operator: {}", id, shardingCode, operator);

            // 调用服务层方法，包含业务逻辑验证
            JSONObject result = hazardRemediationService.deleteHazardRemediation(id, shardingCode);
            log.info("删除隐患整改单，ID: {}, 删除结果{}", id, result);

            if (result.getBooleanValue("success")) {
                return ResponseEntity.ok(result);
            } else {
                // 根据错误类型返回不同的HTTP状态码
                String message = result.getString("message");
                if (message != null && (message.contains("不存在") || message.contains("状态"))) {
                    // 业务逻辑错误，返回400
                    return ResponseEntity.badRequest().body(result);
                } else {
                    // 系统错误，返回500
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
                }
            }
        } catch (Exception e) {
            log.error("删除隐患整改单失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "删除隐患整改单失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 保存光路信息
     */
    @PostMapping("/optical-paths/{remediationId}")
    public ResponseEntity<JSONObject> saveOpticalPaths(
            @PathVariable String remediationId,
            @RequestBody JSONObject opticalPaths) {
        JSONObject result = hazardRemediationService.saveOpticalPaths(remediationId, opticalPaths, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 批量保存光路信息
     */
    @PostMapping("/optical-paths/batch")
    public ResponseEntity<JSONObject> batchSaveOpticalPaths(@RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
                log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
            }

            String remediationId = param.getString("remediationId");
            if (remediationId == null || remediationId.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "缺少必要参数：remediationId");
                return ResponseEntity.badRequest().body(errorResult);
            }

            JSONArray opticalPaths = param.getJSONArray("opticalPaths");
            if (opticalPaths == null) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "缺少必要参数：opticalPaths");
                return ResponseEntity.badRequest().body(errorResult);
            }

            log.info("批量保存光路信息，整改单ID: {}, 光路数量: {}, shardingCode: {}",
                    remediationId, opticalPaths.size(), shardingCode);

            // 直接传递光路数组给服务层
            JSONObject opticalPathsParam = new JSONObject();
            opticalPathsParam.put("opticalPaths", opticalPaths);

            JSONObject result = hazardRemediationService.saveOpticalPaths(remediationId, opticalPathsParam, shardingCode);

            if (result.getBooleanValue("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        } catch (Exception e) {
            log.error("批量保存光路信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "批量保存光路信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 查询光路信息
     */
    @GetMapping("/optical-paths/{remediationId}")
    public ResponseEntity<JSONObject> queryOpticalPaths(@PathVariable String remediationId) {
        JSONObject result = hazardRemediationService.queryOpticalPaths(remediationId, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 查询附件列表
     */
    @GetMapping("/attachments/{remediationId}")
    public ResponseEntity<JSONObject> queryAttachments(@PathVariable String remediationId) {
        JSONObject result = hazardRemediationService.queryAttachments(remediationId, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 删除附件记录
     */
    @DeleteMapping("/attachment/{attachmentId}")
    public ResponseEntity<JSONObject> deleteAttachment(@PathVariable String attachmentId) {
        JSONObject result = hazardRemediationService.deleteAttachment(attachmentId, DEFAULT_SHARDING_CODE);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 保存附件列表（兼容前端调用）
     */
    @PostMapping("/save-attachments")
    public ResponseEntity<JSONObject> saveAttachments(@RequestBody JSONObject param) {
        JSONObject result = new JSONObject();
        try {
            String remediationId = param.getString("id");
            JSONArray attachments = param.getJSONArray("attachments");
            String shardingCode = normalizeShardingCode(param.getString("shardingCode"));

            if (remediationId == null || attachments == null) {
                result.put("success", false);
                result.put("message", "参数不完整");
                return ResponseEntity.badRequest().body(result);
            }

            log.info("保存附件列表，整改单ID: {}, 附件数量: {}", remediationId, attachments.size());

            // 批量保存附件记录
            for (int i = 0; i < attachments.size(); i++) {
                JSONObject attachment = attachments.getJSONObject(i);
                if (attachment != null && attachment.getString("originalFilename") != null) {
                    JSONObject attachmentParam = new JSONObject();
                    attachmentParam.put("remediationId", remediationId);
                    attachmentParam.put("originalFilename", attachment.getString("originalFilename"));
                    attachmentParam.put("storedFilename", attachment.getString("storedFilename"));
                    attachmentParam.put("fileSize", attachment.getLong("fileSize"));
                    attachmentParam.put("fileType", attachment.getString("fileType"));
                    attachmentParam.put("s3Bucket", attachment.getString("s3Bucket"));
                    attachmentParam.put("s3Key", attachment.getString("s3Key"));
                    attachmentParam.put("uploader", attachment.getString("uploader"));

                    hazardRemediationService.saveAttachment(attachmentParam, shardingCode);
                }
            }

            result.put("success", true);
            result.put("message", "附件保存成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("保存附件列表失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "保存附件失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 获取统计信息
     */
    @PostMapping("/statistics")
    public ResponseEntity<JSONObject> getStatistics(@RequestBody JSONObject param) {
        // 从请求参数中提取shardingCode，如果没有则使用默认值
        String shardingCode = param.getString("shardingCode");
        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            shardingCode = DEFAULT_SHARDING_CODE;
        }

        log.info("获取隐患整改统计信息，shardingCode: {}", shardingCode);
        JSONObject result = hazardRemediationService.getStatistics(param, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 更新整改单状态
     */
    @PostMapping("/update-status")
    public ResponseEntity<JSONObject> updateRemediationStatus(@RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode，如果没有则使用默认值
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
                log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
            }

            String id = param.getString("id");
            String newStatus = param.getString("status");

            if (id == null || id.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "缺少必要参数：id");
                return ResponseEntity.badRequest().body(errorResult);
            }

            if (newStatus == null || newStatus.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "缺少必要参数：status");
                return ResponseEntity.badRequest().body(errorResult);
            }

            // 验证状态值
            if (!Arrays.asList("draft", "inProgress", "completed").contains(newStatus)) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "无效的状态值，支持的状态：draft, inProgress, completed");
                return ResponseEntity.badRequest().body(errorResult);
            }

            log.info("更新整改单状态，ID: {}, 新状态: {}, shardingCode: {}", id, newStatus, shardingCode);

            // 根据状态设置相应的时间戳
            if ("inProgress".equals(newStatus)) {
                param.put("progressStartTime", new java.util.Date());
            } else if ("completed".equals(newStatus)) {
                param.put("completionTime", new java.util.Date());
            }

            JSONObject result = hazardRemediationService.updateHazardRemediation(param, shardingCode);
            if (result.getBooleanValue("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        } catch (Exception e) {
            log.error("更新整改单状态失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "更新状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 完成整改（更新状态为completed）
     */
    @PostMapping("/complete")
    public ResponseEntity<JSONObject> completeRemediation(@RequestBody JSONObject param) {
        // 从请求参数中提取shardingCode，如果没有则使用默认值
        String shardingCode = param.getString("shardingCode");
        if (shardingCode == null || shardingCode.trim().isEmpty()) {
            shardingCode = DEFAULT_SHARDING_CODE;
            log.warn("未提供shardingCode，使用默认值: {}", DEFAULT_SHARDING_CODE);
        }

        String id = param.getString("id");
        if (id == null || id.trim().isEmpty()) {
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "缺少必要参数：id");
            return ResponseEntity.badRequest().body(errorResult);
        }

        // 设置状态为completed和完成时间
        param.put("status", "completed");
        param.put("completionTime", new java.util.Date());

        // 如果没有提供完成操作人，可以从其他地方获取（如当前登录用户）
        if (!param.containsKey("completionOperator") || param.getString("completionOperator") == null) {
            param.put("completionOperator", "系统用户"); // 可以根据实际情况修改
        }

        log.info("完成隐患整改，ID: {}, shardingCode: {}, 完成说明: {}, 遗留问题: {}, 豁免理由: {}",
                id, shardingCode,
                param.getString("completionDescription"),
                param.getString("userRemainingIssues"),
                param.getString("exemptionReason"));
        JSONObject result = hazardRemediationService.updateHazardRemediation(param, shardingCode);
        if (result.getBooleanValue("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 上传隐患整改附件
     * 支持防止文件名重复，使用UUID作为存储文件名
     */
    @PostMapping("/upload-attachment")
    public ResponseEntity<JSONObject> uploadAttachment(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "remediationId", required = false) String remediationId,
            @RequestParam(value = "uploader", required = false) String uploader) {
        JSONObject result = new JSONObject();
        try {
            log.info("收到文件上传请求 - 文件名: {}, 大小: {} bytes, remediationId: {}, uploader: {}",
                    file.getOriginalFilename(), file.getSize(), remediationId, uploader);
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                result.put("code", "400");
                result.put("message", "文件名不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (!isValidFileType(contentType, originalFilename)) {
                result.put("code", "400");
                result.put("message", "不支持的文件类型，仅支持：PDF、Word、Excel、图片等格式");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 验证文件大小（限制为50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                result.put("code", "400");
                result.put("message", "文件大小不能超过50MB");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 生成UUID文件名，保持原始扩展名
            String fileExtension = getFileExtension(originalFilename);
            String uuidFilename = UUID.randomUUID().toString() + fileExtension;
            String s3Key = FOLDER_PATH + uuidFilename;
            
            // 上传到S3
            s3Service.writeStream(BUCKET_NAME, s3Key, file.getInputStream());
            
            log.info("文件上传成功 - 原始文件名: {}, UUID文件名: {}, 大小: {} bytes",
                    originalFilename, uuidFilename, file.getSize());

            // 构建文件信息
            JSONObject fileInfo = new JSONObject();
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("storedFilename", uuidFilename);
            fileInfo.put("fileSize", file.getSize());
            fileInfo.put("fileType", contentType);
            fileInfo.put("s3Key", s3Key);
            fileInfo.put("s3Bucket", BUCKET_NAME);

            // 如果提供了remediationId，则保存附件记录到数据库
            if (remediationId != null && !remediationId.trim().isEmpty()) {
                try {
                    JSONObject attachmentParam = new JSONObject();
                    attachmentParam.put("remediationId", remediationId);
                    attachmentParam.put("originalFilename", originalFilename);
                    attachmentParam.put("storedFilename", uuidFilename);
                    attachmentParam.put("fileSize", file.getSize());
                    attachmentParam.put("fileType", contentType);
                    attachmentParam.put("s3Bucket", BUCKET_NAME);
                    attachmentParam.put("s3Key", s3Key);
                    attachmentParam.put("uploader", uploader != null ? uploader : "未知用户");

                    JSONObject saveResult = hazardRemediationService.saveAttachment(attachmentParam, DEFAULT_SHARDING_CODE);
                    if (saveResult.getBooleanValue("success")) {
                        fileInfo.put("attachmentId", saveResult.getJSONObject("data").get("id"));
                        log.info("附件记录保存成功，附件ID: {}", saveResult.getJSONObject("data").get("id"));
                    } else {
                        log.warn("附件记录保存失败: {}", saveResult.getString("message"));
                    }
                } catch (Exception e) {
                    log.error("保存附件记录失败: {}", e.getMessage(), e);
                    // 不影响文件上传结果，只记录日志
                }
            }

            result.put("code", "200");
            result.put("message", "文件上传成功");
            result.put("data", fileInfo);

            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            result.put("code", "500");
            result.put("message", "文件上传失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 批量上传隐患整改附件
     */
    @PostMapping("/upload-attachments")
    public ResponseEntity<JSONObject> uploadAttachments(@RequestParam("files") MultipartFile[] files) {
        JSONObject result = new JSONObject();
        JSONObject successFiles = new JSONObject();
        JSONObject failedFiles = new JSONObject();
        
        try {
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                String originalFilename = file.getOriginalFilename();
                
                try {
                    // 验证单个文件
                    if (originalFilename == null || originalFilename.trim().isEmpty()) {
                        failedFiles.put("file_" + i, "文件名不能为空");
                        continue;
                    }
                    
                    if (!isValidFileType(file.getContentType(), originalFilename)) {
                        failedFiles.put(originalFilename, "不支持的文件类型");
                        continue;
                    }
                    
                    if (file.getSize() > 50 * 1024 * 1024) {
                        failedFiles.put(originalFilename, "文件大小超过50MB");
                        continue;
                    }
                    
                    // 上传单个文件
                    String fileExtension = getFileExtension(originalFilename);
                    String uuidFilename = UUID.randomUUID().toString() + fileExtension;
                    String s3Key = FOLDER_PATH + uuidFilename;
                    
                    s3Service.writeStream(BUCKET_NAME, s3Key, file.getInputStream());
                    
                    // 记录成功上传的文件信息
                    JSONObject fileInfo = new JSONObject();
                    fileInfo.put("originalFilename", originalFilename);
                    fileInfo.put("storedFilename", uuidFilename);
                    fileInfo.put("fileSize", file.getSize());
                    fileInfo.put("fileType", file.getContentType());
                    fileInfo.put("s3Key", s3Key);
                    fileInfo.put("s3Bucket", BUCKET_NAME);
                    
                    successFiles.put(originalFilename, fileInfo);
                    
                } catch (Exception e) {
                    log.error("上传文件 {} 失败: {}", originalFilename, e.getMessage());
                    failedFiles.put(originalFilename, "上传失败: " + e.getMessage());
                }
            }
            
            result.put("code", "200");
            result.put("message", "批量上传完成");
            result.put("successFiles", successFiles);
            result.put("failedFiles", failedFiles);
            result.put("successCount", successFiles.size());
            result.put("failedCount", failedFiles.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("批量文件上传失败: {}", e.getMessage(), e);
            result.put("code", "500");
            result.put("message", "批量文件上传失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 删除隐患整改附件
     * 支持删除S3中的文件和数据库记录
     */
    @DeleteMapping("/delete-attachment")
    public ResponseEntity<JSONObject> deleteAttachment(
            @RequestParam("s3Key") String s3Key,
            @RequestParam(value = "attachmentId", required = false) String attachmentId) {
        JSONObject result = new JSONObject();
        try {
            log.info("删除隐患整改附件 - S3Key: {}, attachmentId: {}", s3Key, attachmentId);

            // 删除S3中的文件
            try {
                s3Service.deleteObject(BUCKET_NAME, s3Key);
                log.info("S3文件删除成功: {}", s3Key);
            } catch (Exception e) {
                log.warn("S3文件删除失败或文件不存在: {}, 错误: {}", s3Key, e.getMessage());
                // 继续执行，删除数据库记录
            }

            // 如果提供了attachmentId，删除数据库记录
            if (attachmentId != null && !attachmentId.trim().isEmpty()) {
                try {
                    JSONObject deleteResult = hazardRemediationService.deleteAttachment(attachmentId, DEFAULT_SHARDING_CODE);
                    if (deleteResult.getBooleanValue("success")) {
                        log.info("附件数据库记录删除成功，attachmentId: {}", attachmentId);
                    } else {
                        log.warn("附件数据库记录删除失败: {}", deleteResult.getString("message"));
                    }
                } catch (Exception e) {
                    log.error("删除附件数据库记录失败: {}", e.getMessage(), e);
                    // 不影响整体删除结果
                }
            }

            result.put("code", "200");
            result.put("message", "附件删除成功");
            result.put("success", true);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("删除附件失败: {}", e.getMessage(), e);
            result.put("code", "500");
            result.put("message", "删除附件失败: " + e.getMessage());
            result.put("success", false);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }


    /**
     * 验证文件类型是否支持
     */
    private boolean isValidFileType(String contentType, String filename) {
        if (contentType == null) {
            contentType = "";
        }
        
        // 支持的MIME类型
        String[] allowedMimeTypes = {
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/gif",
            "text/plain"
        };
        
        // 检查MIME类型
        for (String allowedType : allowedMimeTypes) {
            if (contentType.toLowerCase().contains(allowedType.toLowerCase())) {
                return true;
            }
        }
        
        // 检查文件扩展名（作为备用验证）
        if (filename != null) {
            String lowerFilename = filename.toLowerCase();
            String[] allowedExtensions = {".pdf", ".doc", ".docx", ".xls", ".xlsx", 
                                        ".jpg", ".jpeg", ".png", ".gif", ".txt"};
            for (String ext : allowedExtensions) {
                if (lowerFilename.endsWith(ext)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex);
        }
        
        return "";
    }

    // ==================== 风险检测相关接口 ====================

    @PostMapping("/{id}/risk-detection")
    public ResponseEntity<JSONObject> performRiskDetection(
            @PathVariable String id,
            @RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode，如果没有则使用默认值
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            param.put("remediationId", id);
            JSONObject result = hazardRemediationService.performRiskDetection(param, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("执行风险检测失败：{}", e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "执行风险检测失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    @PostMapping("/{id}/save-risk-detection")
    public ResponseEntity<JSONObject> saveRiskDetection(
            @PathVariable String id,
            @RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode，如果没有则使用默认值
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            param.put("remediationId", id);
            JSONObject result = hazardRemediationService.saveRiskDetection(param, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("保存风险检测结果失败：{}", e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "保存风险检测结果失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    @GetMapping("/{id}/risk-detections")
    public ResponseEntity<JSONObject> queryRiskDetections(
            @PathVariable String id,
            @RequestParam(value = "shardingCode", required = false) String shardingCode) {
        try {
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            JSONObject result = hazardRemediationService.queryRiskDetections(id, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询风险检测记录失败：{}", e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询风险检测记录失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    @PostMapping("/{id}/risk-comparison")
    public ResponseEntity<JSONObject> performRiskComparison(
            @PathVariable String id,
            @RequestBody JSONObject param) {
        try {
            // 从请求参数中提取shardingCode，如果没有则使用默认值
            String shardingCode = param.getString("shardingCode");
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            param.put("remediationId", id);
            JSONObject result = hazardRemediationService.performRiskComparison(param, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("执行风险对比分析失败：{}", e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "执行风险对比分析失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }







    @GetMapping("/{id}/detection-timeline")
    public ResponseEntity<JSONObject> queryDetectionTimeline(
            @PathVariable String id,
            @RequestParam(value = "shardingCode", required = false) String shardingCode) {
        try {
            if (shardingCode == null || shardingCode.trim().isEmpty()) {
                shardingCode = DEFAULT_SHARDING_CODE;
            }

            JSONObject result = hazardRemediationService.queryDetectionTimeline(id, shardingCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询检测时间线失败：{}", e.getMessage(), e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "查询检测时间线失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 获取隐患整改检测时间线（POST方式）
     */
    @PostMapping("/detection-timeline")
    public ResponseEntity<JSONObject> getDetectionTimeline(@RequestBody JSONObject param) {
        try {
            log.info("获取隐患整改检测时间线，参数: {}", param);

            JSONObject result = hazardRemediationService.getDetectionTimeline(param);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取隐患整改检测时间线失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "获取检测时间线失败: " + e.getMessage());
            errorResult.put("data", null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }





}
