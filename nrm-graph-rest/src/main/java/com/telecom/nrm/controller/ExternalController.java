package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.ResponseFactory;
import com.telecom.common.biyi.Result;
import com.telecom.nrm.dao.IPRanJsDao;
import com.telecom.nrm.domain.NRMConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/external")
@Slf4j
public class ExternalController {

    @Autowired
    IPRanJsDao ipRanJsDao;

    @PostMapping("/queryAll")
    public JSONObject queryAll(@RequestBody JSONObject body) {
        try {
            return ipRanJsDao.ipranPonCityNetWorkQuery(body, NRMConstants.SHARDING_CODE);
        } catch (Exception e) {
            return null;
        }

    }


}
