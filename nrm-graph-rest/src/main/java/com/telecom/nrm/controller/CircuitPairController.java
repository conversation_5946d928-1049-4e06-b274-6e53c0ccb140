package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CircuitPairDao;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.CircuitPairService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.dto.ExportResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/api/circuit-pair-api")
@Slf4j
public class CircuitPairController {

    @Autowired
    RegionDao regionDao;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    CircuitPairService circuitPairService;

    @Autowired
    DocumentExportService documentExportService;

    @Autowired
    CircuitPairDao circuitPairDao;
    /**
     * 电路对下载
     */
    @PostMapping("/circuit_pair_download")
    public ResponseEntity<ExportResponseDTO> circuit_pair_download(@RequestBody JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [电路对导出] 接收到电路对下载请求");
            log.info("📋 [电路对导出] 请求参数: {}", jsonObject);

            // 参数验证
            if (jsonObject == null || jsonObject.isEmpty()) {
                log.error("❌ [电路对导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            String ds = jsonObject.getString("ds");
            log.info("📍 [电路对导出] 查询地市: {}", ds);

            if (ds == null || ds.trim().isEmpty()) {
                log.error("❌ [电路对导出] 地市参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("地市参数不能为空"));
            }

            // 1. 查询电路对数据
            log.info("🔍 [电路对导出] 步骤1: 开始查询电路对数据");
            long queryStartTime = System.currentTimeMillis();
            PageResponse<JSONObject> pageResponse = custLinkInterfaceDao.getCircuit_pair(
                jsonObject, 10000, 1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [电路对导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            if (pageResponse == null || pageResponse.getData() == null || pageResponse.getData().size() == 0) {
                log.warn("⚠️ [电路对导出] 查询结果为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("没有查询到电路对数据"));
            }

            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            List<JSONObject> resultData = pageResponse.getData();
            log.info("📊 [电路对导出] 查询到数据量: {} 条", resultData.size());

            // 2. 转换数据格式
            log.info("🔄 [电路对导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertCircuitPairData(resultData);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [电路对导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [电路对导出] 转换后数据量: {} 条记录", exportData.size());

            // 3. 定义列
            log.info("📝 [电路对导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "地市:areacode",
                "客户名称:custName",
                "业务号码:accessCode",
                "电路编号:aCode",
                "光路编码:aOptCode",
                "光路名称:aOptRoad",
                "电路备注:aNotes",
                "成对业务号码:zAccessCode",
                "成对电路编号:zCode",
                "成对光路编码:zOptCode",
                "成对光路名称:zOptRoad",
                "成对电路备注:zNotes",
                "检测结果:result"
            );
            log.info("📋 [电路对导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [电路对导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO exportResult = documentExportService.exportToDocumentSecurity(
                exportData, columns, "电路对数据", "电路对下载",
                "电路管理", "/api/circuit_pair/circuit_pair_download", "电路对数据导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [电路对导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (exportResult.isSuccess()) {
                exportResult.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [电路对导出] 导出成功完成!");
                log.info("📁 [电路对导出] 文件名: {}", exportResult.getFileName());
                log.info("📊 [电路对导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [电路对导出] 总耗时: {} ms", totalTime);
                log.info("📈 [电路对导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [电路对导出] 导出失败: {}", exportResult.getMessage());
            }

            return ResponseEntity.ok(exportResult);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [电路对导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [电路对导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    public void circuit_pair_download_response(List<JSONObject> result, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("客户名称");
        header.createCell(2).setCellValue("业务号码");
        header.createCell(3).setCellValue("电路编号");
        header.createCell(4).setCellValue("光路编码");
        header.createCell(5).setCellValue("光路名称");
        header.createCell(6).setCellValue("电路备注");
        header.createCell(7).setCellValue("成对业务号码");
        header.createCell(8).setCellValue("成对电路编号");
        header.createCell(9).setCellValue("成对光路编码");
        header.createCell(10).setCellValue("成对光路名称");
        header.createCell(11).setCellValue("成对电路备注");
        header.createCell(12).setCellValue("检测结果");

        // 填充数据
        int rowIndex = 1;
        for (int i =0; i< result.size();i++) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(result.get(i).getString("areacode"));
            row.createCell(1).setCellValue(result.get(i).getString("custName"));
            row.createCell(2).setCellValue(result.get(i).getString("accessCode"));
            row.createCell(3).setCellValue(result.get(i).getString("aCode"));
            row.createCell(4).setCellValue(result.get(i).getString("aOptCode"));
            row.createCell(5).setCellValue(result.get(i).getString("aOptRoad"));
            row.createCell(6).setCellValue(result.get(i).getString("aNotes"));
            row.createCell(7).setCellValue(result.get(i).getString("zAccessCode"));
            row.createCell(8).setCellValue(result.get(i).getString("zCode"));
            row.createCell(9).setCellValue(result.get(i).getString("zOptCode"));
            row.createCell(10).setCellValue(result.get(i).getString("zOptRoad"));
            row.createCell(11).setCellValue(result.get(i).getString("zNotes"));
            row.createCell(12).setCellValue(result.get(i).getString("result"));
        }
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    @PostMapping("/upload_circuit_pair/{ds}")
    public void upload_circuit_pair(@PathVariable String ds, @RequestParam("file") MultipartFile file) throws IOException {

        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if (rownum > 1) {
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data_1 = new JSONObject();
                    for (int j = 0; j < column; j++) {
                        if (j == 0 && !Objects.isNull(sheet.getRow(i).getCell(j)))
                            current_data_1.put("areacode", sheet.getRow(i).getCell(j).getStringCellValue());
                        if (j == 1 && !Objects.isNull(sheet.getRow(i).getCell(j))){
                            CellType cellType = sheet.getRow(i).getCell(j).getCellType();
                            if(cellType.toString().equals("NUMERIC")){
                                current_data_1.put("a_code", sheet.getRow(i).getCell(j).getNumericCellValue()+"");
                            }else current_data_1.put("a_code", sheet.getRow(i).getCell(j).getStringCellValue());
                        }
                        if (j == 2 && !Objects.isNull(sheet.getRow(i).getCell(j))){
                            CellType cellType = sheet.getRow(i).getCell(j).getCellType();
                            if(cellType.toString().equals("NUMERIC")){
                                current_data_1.put("z_code", sheet.getRow(i).getCell(j).getNumericCellValue()+"");
                            }else current_data_1.put("z_code", sheet.getRow(i).getCell(j).getStringCellValue());
                        }
                    }

                    log.info("NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds):{}",NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                    log.info("current_data_1:{}",current_data_1);
                    int pair_circuit_input_insert_result =  custLinkInterfaceDao.pair_circuit_input_insert(current_data_1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));

                    log.info("pair_circuit_input_insert_result:{}",pair_circuit_input_insert_result);
                    int pair_circuit_input_insert_access_code_result =custLinkInterfaceDao.pair_circuit_input_insert_access_code(current_data_1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));

                    log.info("pair_circuit_input_insert_access_code_result:{}",pair_circuit_input_insert_access_code_result);

                }
            }
        }
    }


    @PostMapping("/save_circuit_pair")
    public Integer save_circuit_pair(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String num_type = jsonObject.getString("num_type");
        Integer result = -1;
        if(num_type.equals("access_num")){
            result = custLinkInterfaceDao.pair_circuit_input_insert_access_code(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

        }
        else{
            result = custLinkInterfaceDao.pair_circuit_input_insert(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));

        }

                           /* current_data_1.put("areacode", sheet.getRow(i).getCell(j).getStringCellValue());
                            current_data_1.put("a_code", sheet.getRow(i).getCell(j).getStringCellValue());
                            current_data_1.put("z_code", sheet.getRow(i).getCell(j).getStringCellValue());*/
         return result;
    }

    @GetMapping("/circuit_pair_query_f")
    public BiyiPageResult<JSONObject> circuit_pair_query_f(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        String[] params = null;
        if(!Objects.isNull(jsonObject.getString("param"))&& jsonObject.getString("param")!=null &&jsonObject.getString("param")!=""){
            // params = jsonObject.getString("param").split("[,，]+");
            params = Arrays.stream(jsonObject.getString("param").split("[,，]+"))
                    .map(String::trim)
                    .toArray(String[]::new);
        }
        jsonObject.put("params",params);
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair_f(jsonObject ,pageable.getSize(),pageable.getPage(),NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @GetMapping("/circuit_pair_query_f/dictionary")
    public ResponseEntity<JSONObject> dictionary_f(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        return ResponseEntity.ok().body(meta);
    }

    @PostMapping("/circuit-pair-ignore")
    public Integer circuit_res_cor(@RequestBody JSONObject jsonObject){
        jsonObject.getJSONObject("record").put("a_code", jsonObject.getJSONObject("record").getString("aCode"));
        jsonObject.getJSONObject("record").put("z_code", jsonObject.getJSONObject("record").getString("zCode"));
        jsonObject.getJSONObject("record").put("result","正常");
        return custLinkInterfaceDao.pair_circuit_input_result_corr_insert(jsonObject.getJSONObject("record"), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/delete-circuit-pair-ignore")
    public Integer delete_circuit_res_cor(@RequestBody JSONObject jsonObject){
        jsonObject.getJSONObject("record").put("a_code", jsonObject.getJSONObject("record").getString("aCode"));
        jsonObject.getJSONObject("record").put("z_code", jsonObject.getJSONObject("record").getString("zCode"));
        return custLinkInterfaceDao.pair_circuit_input_result_corr_delete(jsonObject.getJSONObject("record"), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/circuit_pair_check")
    public void circuit_pair_check(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        jsonObject.put("id", jsonObject.getString("id"));
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair_f(jsonObject ,1000000,1, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        List<JSONObject> list = pageResponse.getData();
        log.info("pageResponse:"+list.size());
        int count =0;
        for(JSONObject custViewMember : list){
            count = count+1;
            log.info("当前count:"+custViewMember);
            circuitPairService.risk_analyze(custViewMember);
        }
    }

    @PostMapping("/note_update")
    public Integer note_update(@RequestBody JSONObject jsonObject){
        return circuitPairDao.pm_circuit_pair_note_update(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    @PostMapping("/delete")
    public Integer deleteRecord(@RequestBody JSONObject jsonObject){
        return circuitPairDao.tmp_cm_link_service_customer_1_delete(jsonObject, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

    /**
     * 转换电路对数据格式
     */
    private List<Map<String, Object>> convertCircuitPairData(List<JSONObject> resultData) {
        log.info("🔄 [数据转换] 开始转换电路对数据格式，输入数据量: {}", resultData.size());
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            int processedCount = 0;

            for (JSONObject item : resultData) {
                try {
                    Map<String, Object> row = new HashMap<>();
                    row.put("areacode", item.getString("areacode") != null ? item.getString("areacode") : "");
                    row.put("custName", item.getString("custName") != null ? item.getString("custName") : "");
                    row.put("accessCode", item.getString("accessCode") != null ? item.getString("accessCode") : "");
                    row.put("aCode", item.getString("aCode") != null ? item.getString("aCode") : "");
                    row.put("aOptCode", item.getString("aOptCode") != null ? item.getString("aOptCode") : "");
                    row.put("aOptRoad", item.getString("aOptRoad") != null ? item.getString("aOptRoad") : "");
                    row.put("aNotes", item.getString("aNotes") != null ? item.getString("aNotes") : "");
                    row.put("zAccessCode", item.getString("zAccessCode") != null ? item.getString("zAccessCode") : "");
                    row.put("zCode", item.getString("zCode") != null ? item.getString("zCode") : "");
                    row.put("zOptCode", item.getString("zOptCode") != null ? item.getString("zOptCode") : "");
                    row.put("zOptRoad", item.getString("zOptRoad") != null ? item.getString("zOptRoad") : "");
                    row.put("zNotes", item.getString("zNotes") != null ? item.getString("zNotes") : "");
                    row.put("result", item.getString("result") != null ? item.getString("result") : "");

                    data.add(row);
                    processedCount++;

                    // 每处理1000条记录输出一次进度
                    if (processedCount % 1000 == 0) {
                        log.info("📊 [数据转换] 已处理 {} / {} 条记录", processedCount, resultData.size());
                    }

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换第 {} 条记录时出错: {}", processedCount + 1, e.getMessage());
                    log.error("❌ [数据转换] 出错的数据: {}", item);
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功转换: {} 条记录", processedCount);

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

}
