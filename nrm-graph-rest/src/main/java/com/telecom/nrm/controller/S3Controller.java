package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.telecom.nrm.service.S3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/s3")
public class S3Controller {
    private static final Logger log = LoggerFactory.getLogger(S3Controller.class);

    private static final String BUCKET_NAME = "yuanfeng";
    private static final String FOLDER_PATH = "shuangluyou/";

    @Autowired
    private S3Service s3Service;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public ResponseEntity<JSONObject> uploadFile(@RequestParam("file") MultipartFile file) {
        JSONObject result = new JSONObject();
        try {
            String fileName = file.getOriginalFilename();
            String fullPath = FOLDER_PATH + fileName;

            // 直接传入文件的InputStream
            s3Service.writeStream(BUCKET_NAME, fullPath, file.getInputStream());

            result.put("code", "200");
            result.put("message", "File uploaded successfully");
            result.put("data", fileName);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("Failed to upload file: {}", e.getMessage(), e);
            result.put("code", "500");
            result.put("message", "Upload failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    @GetMapping("/download/{fileName}")
    public ResponseEntity<InputStreamResource> downloadFile(@PathVariable String fileName) {
        try {

            // URL解码文件名
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.toString());
            String fullPath = FOLDER_PATH + fileName;
            System.out.println("fullPath: " + fullPath);


            // 获取文件元数据，检查文件大小
            ObjectMetadata metadata = s3Service.getObjectMetadata(BUCKET_NAME, fullPath);
            long contentLength = metadata.getContentLength();
            log.info("Downloading file: {}, size: {} bytes", fullPath, contentLength);

            InputStream inputStream = s3Service.readStream(BUCKET_NAME, fullPath);
            InputStreamResource resource = new InputStreamResource(inputStream);

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength));

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (Exception e) {
            log.error("Error downloading file: " + fileName, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * 读取文件内容
     */
    @GetMapping("/read/{fileName}")
    public ResponseEntity<JSONObject> readFile(@PathVariable String fileName) {
        JSONObject result = new JSONObject();
        try {
            String fullPath = FOLDER_PATH + fileName;
            String content = s3Service.readFile(BUCKET_NAME, fullPath);

            result.put("code", "200");
            result.put("message", "Read successfully");
            result.put("data", content);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("code", "500");
            result.put("message", "Read failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 列出所有bucket
     */
    @GetMapping("/buckets")
    public ResponseEntity<JSONObject> listBuckets() {
        JSONObject result = new JSONObject();
        try {
            result.put("code", "200");
            result.put("message", "Success");
            result.put("data", s3Service.listBuckets());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("code", "500");
            result.put("message", "Failed to list buckets: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    @GetMapping("/list")
    public ResponseEntity<JSONObject> listFiles() {
        JSONObject result = new JSONObject();
        try {
            ObjectListing objects = s3Service.listObjects(BUCKET_NAME, FOLDER_PATH);
            List<String> files = objects.getObjectSummaries().stream()
                    .map(S3ObjectSummary::getKey)
                    .collect(Collectors.toList());

            result.put("code", "200");
            result.put("message", "Success");
            result.put("data", files);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("code", "500");
            result.put("message", "Failed to list files: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }




}