package com.telecom.nrm.controller;

import ch.qos.logback.core.joran.spi.ElementSelector;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.CryptAES;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.Page;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.dao.GroupFaultRapidPositioningDao;
import com.telecom.nrm.dao.UserDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.graph.Graph;
import com.telecom.nrm.domain.graph.GraphScene;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.domain.graph.api.GraphRequestBuilder;
import com.telecom.nrm.domain.graph.api.GraphWhereBodyItem;
import com.telecom.nrm.service.*;
import com.telecom.nrm.utils.PageUtils;
import com.telecom.nrm.dto.ExportResponseDTO;
import com.telecom.nrm.dto.SheetData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.parameters.P;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/cut-over")
@Slf4j
public class CutoverController {

    final String aseCryKey = "tfcust";
    @Autowired
    CutoverDao cutoverDao;
    @Autowired
    UserDao userDao;


    @Autowired
    GroupFaultRapidPositioningDao groupFaultRapidPositioningDao;

    @Autowired
    GroupFaultRapidPositioningController groupFaultRapidPositioningController;
    @Autowired
    InfluenceAnalysisController influenceAnalysisController;

    @Autowired
    GraphService graphService;

    @Autowired
    GraphApiService graphApiService;

    @Autowired
    OptGroupController optGroupController;

    @Autowired
    ProjectService projectService;

    @Autowired
    DocumentExportService documentExportService;

    @Autowired
    ProjectInfluenceService projectInfluenceService;

    @Autowired
    ProjectPONCheckService projectPONCheckService;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    static String ACTION_INFLUENCE = "1";
    static String ACTION_PRE_PON_CHECK = "2";
    static String ACTION_PROCESS_PON_CHECK = "3";
    static String ACTION_POST_PON_CHECK = "4";
    static Integer ROW_DATA_VALID = 1;

    /**
     * 保存割接项目
     * @param params
     * @return
     */
    @PostMapping("")
    public JSONObject saveCutOverProject(@RequestBody JSONObject params) {

        JwtUser jwtUser = SecurityContext.getJwtUser();
        params.put("create_op", jwtUser.getUsername());
        if (ObjectUtils.isEmpty(params.getString("project_type_id"))) {
            params.put("project_type_id", NRMConstants.PROJECT_TYPE_CUT_OVER);
        }

        JSONObject info = projectService.saveCutOverProject(params);
        return info;
    }


    /**
     * 查询割接项目
     * @param example
     * @param pageable
     * @return
     */
    @GetMapping("")
    public BiyiPageResult<JSONObject> queryCutOverProject(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = cutoverDao.queryCutOverProject(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);

        List<JSONObject> projectList = pageResponse.getData();
        queryUserNames(projectList);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    void queryUserNames(List<JSONObject> projectList) {
        List<String> userNames  = projectList.stream().map(d->d.getString("create_op").split("@@@")[0]).distinct().collect(Collectors.toList());
        List<JSONObject> userList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(userNames)) {
            userList = userDao.queryUsers(userNames, 100000, 1,NRMConstants.SHARDING_GRAPH_DB);
        }
        if (ObjectUtil.isEmpty(userList)) {
            userList = new ArrayList<>();
        }

        Map<String,JSONObject> userMap = new HashMap<>();
        for (JSONObject o : userList) {
            userMap.put(o.getString("username"), o);
        }
        for (JSONObject o : projectList) {
            String createOp = o.getString("create_op");
            if (ObjectUtil.isNotEmpty(createOp)) {
                createOp = createOp.split("@@@")[0];
            }
            JSONObject user = userMap.get(createOp);
            if (ObjectUtil.isNotEmpty(user)) {
                o.put("create_op_name", user.getString("name"));
                o.put("create_op_mobile", user.getString("mobile"));
            }else {
                o.put("create_op_name", o.getString("create_op"));
            }

        }
    }




    @GetMapping("/query-project-service")
    public BiyiPageResult<JSONObject> queryProjectService(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = cutoverDao.queryProjectService(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }


    @GetMapping("/query-project-road")
    public BiyiPageResult<JSONObject> queryProjectRoad(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = cutoverDao.queryPrProjectRoute(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @GetMapping("/query-project-opt-road-group")
    public BiyiPageResult<JSONObject> queryProjectOptRoadGroup(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = cutoverDao.queryPrProjectOptGroup(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @GetMapping("/query-process-pon-check")
    public BiyiPageResult<JSONObject> queryProcessPONCheck(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = cutoverDao.queryProcessPONCheck(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount() , pageResponse.getPageInfo().getTotalCount());
    }

    @GetMapping("/summary-project-service")
    public BiyiPageResult<JSONObject> summaryProjectService(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);


        PageResponse<JSONObject> summaryResponse = cutoverDao.summaryProjectService(param, 1000, pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return new BiyiPageResult(summaryResponse.getData(), summaryResponse.getPageInfo().getTotalCount() , summaryResponse.getPageInfo().getTotalCount());
    }

    @PostMapping("/download-influence")
    public ResponseEntity<ExportResponseDTO> downloadInfluence(@RequestBody JSONObject param) {
        try {
            log.info("🚀 开始导出影响业务数据");

            // 查询数据
            int currentPage = 1;
            int pageSize = 100000;
            PageResponse<JSONObject> pageResponse = cutoverDao.queryProjectService(param, pageSize, currentPage, NRMConstants.SHARDING_GRAPH_DB);

            // 转换数据格式并解密客户经理信息
            List<Map<String, Object>> data = new ArrayList<>();
            for (JSONObject item : pageResponse.getData()) {
                Map<String, Object> row = new HashMap<>();
                for (String key : item.keySet()) {
                    Object value = item.get(key);

                    // 在导出时解密客户经理信息
                    if ("customer_manager".equals(key) && value != null) {
                        try {
                            String encryptedName = value.toString();
                            log.info("导出时客户经理姓名解密 - 接入号: {}, 加密前: {}", item.getString("access_code"), encryptedName);
                            String decryptedName = CryptAES.AES_Decrypt(aseCryKey, encryptedName);
                            log.info("导出时客户经理姓名解密 - 接入号: {}, 解密后: {}", item.getString("access_code"), decryptedName);
                            value = decryptedName;
                        } catch (Exception e) {
                            log.warn("导出时客户经理姓名解密失败 - 接入号: {}, 原始值: {}, 异常: {}",
                                    item.getString("access_code"), value, e.getMessage());
                            // 解密失败时保持原值
                        }
                    } else if ("customer_manager_phone".equals(key) && value != null) {
                        try {
                            String encryptedPhone = value.toString();
                            log.info("导出时客户经理电话解密 - 接入号: {}, 加密前: {}", item.getString("access_code"), encryptedPhone);
                            String decryptedPhone = CryptAES.AES_Decrypt(aseCryKey, encryptedPhone);
                            log.info("导出时客户经理电话解密 - 接入号: {}, 解密后: {}", item.getString("access_code"), decryptedPhone);
                            value = decryptedPhone;
                        } catch (Exception e) {
                            log.warn("导出时客户经理电话解密失败 - 接入号: {}, 原始值: {}, 异常: {}",
                                    item.getString("access_code"), value, e.getMessage());
                            // 解密失败时保持原值
                        }
                    }

                    row.put(key, value);
                }
                data.add(row);
            }

            // 定义列
            List<String> columns = Arrays.asList(
                "接入号:access_code","电路编号:circuit_code","90天内割接次数:day30_times","影响业务名称:service_type","客户类型:customer_type",
                "客户等级:service_level","客户名称:customer","装机地址:address","客户经理:customer_manager","客户经理电话:customer_manager_phone",
                "客户经理所属部门:customer_manager_org","客户经理所属部门岗位:customer_manager_post","是否生命线业务:is_lifecycle",
                "资源编码:entity_code","资源名称:entity_name","资源IP:dev_ip","纤芯号:line_no","光路名称:route_name","光路编码:route_code",
                "光路A端名称:a_road_device_name","光路A端端口:a_road_port_code","光路Z端名称:z_road_device_name","光路Z端端口:z_road_port_code",
                "光缆A端名称:a_cable_device_name","光缆A端端口:a_cable_port_code","光缆Z端名称:z_cable_device_name","光缆Z端端口:z_cable_port_code"
            );

            // 使用通用导出服务
            ExportResponseDTO result = documentExportService.exportToDocumentSecurity(
                data, columns, "割接影响面分析结果", "影响业务导出",
                "割接管理", "/api/cutover/download-influence", "影响业务导出"
            );

            if (result.isSuccess()) {
                result.setDataCount(data.size());
                log.info("✅ 影响业务导出成功: {}", result.getFileName());
            } else {
                log.error("❌ 影响业务导出失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 影响业务导出异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    @GetMapping("/summary-project-pon-check")
    public Object summaryProjectPONCheck(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);

        PageResponse<JSONObject> summaryResponse = cutoverDao.summaryProjectPonCheck(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return summaryResponse.getData();
    }

    @GetMapping("/summary-process-pon-check")
    public Object summaryProcessPONCheck(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject param = (JSONObject) JSON.toJSON(example);

        PageResponse<JSONObject> summaryResponse = cutoverDao.summaryProcessPonCheck(param, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_GRAPH_DB);
        return summaryResponse.getData();
        ////////////////////////////////////
        /////////////////
    }

    @PostMapping("/download-pon-check")
    public ResponseEntity<ExportResponseDTO> downloadPoncheck(@RequestBody JSONObject param) {
        try {
            log.info("🚀 开始PON检测结果导出，参数: {}", param);

            // 1. 查询PON检测汇总数据
            PageResponse<JSONObject> summaryResponse = cutoverDao.summaryProcessPonCheck(param, 10000, 1, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> summaryData = summaryResponse.getData();

            // 2. 查询PON检测详情数据
            PageResponse<JSONObject> detailResponse = cutoverDao.queryProcessPONCheck(param, 100000, 1, NRMConstants.SHARDING_GRAPH_DB);
            List<JSONObject> detailData = detailResponse.getData();

            // 3. 转换汇总数据格式
            List<Map<String, Object>> summaryExportData = convertSummaryData(summaryData);

            // 4. 转换详情数据格式
            List<Map<String, Object>> detailExportData = convertDetailData(detailData);

            // 5. 定义汇总数据列
            List<String> summaryColumns = Arrays.asList(
                "割接前OLT:pre_olt_ip",
                "割接前端口:pre_olt_port",
                "业务数量:all_num",
                "割接前状态:pre_online_status",
                "割接前数量:pre_online_num",
                "割接后OLT:post_olt_ip",
                "割接后端口:post_olt_port",
                "割接后状态:post_online_status",
                "割接后数量:post_online_num"
            );

            // 6. 定义详情数据列
            List<String> detailColumns = Arrays.asList(
                "接入号:access_code", "产品实例ID:crm_product_id", "影响业务名称:service_type",
                "客户类型:customer_type", "客户等级:service_level", "客户名称:customer",
                "客户经理:customer_manager", "客户经理电话:customer_manager_phone", "是否生命线业务:is_lifecycle",
                "OLT资源IP(实时采集):olt_ip", "OLT网管IP(实时采集):olt_nm_ip", "PON口编码(实时采集):olt_port_code",
                "接入设备编码:access_device_code", "接入设备LOID:access_device_code_loid",
                "资源编码:entity_code", "资源名称:entity_name", "纤芯号:line_no",
                "光路编号:route_code", "分光器编码:obd_code", "分光器名称:obd_name", "分光器端口:obd_port_code",
                "操作前状态:pre_online_status", "操作后状态:process_online_status",
                "操作前光衰:pre_rx_power", "操作后光衰:process_rx_power"
            );

            // 7. 使用多Sheet导出服务
            ExportResponseDTO result = documentExportService.exportMultipleSheetToDocumentSecurity(
                Arrays.asList(
                    new SheetData("PON口检测统计", summaryExportData, summaryColumns),
                    new SheetData("PON口检测详情", detailExportData, detailColumns)
                ),
                "PON检测结果导出",
                "割接管理",
                "/api/cutover/download-pon-check",
                "PON检测结果导出"
            );

            if (result.isSuccess()) {
                result.setDataCount(summaryData.size() + detailData.size());
                log.info("✅ PON检测结果导出成功: {}", result.getFileName());
            } else {
                log.error("❌ PON检测结果导出失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ PON检测结果导出异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }


    /**
     * 转换PON检测汇总数据格式
     */
    private List<Map<String, Object>> convertSummaryData(List<JSONObject> summaryData) {
        List<Map<String, Object>> data = new ArrayList<>();

        for (JSONObject item : summaryData) {
            Map<String, Object> row = new HashMap<>();
            row.put("pre_olt_ip", item.getString("pre_olt_ip"));
            row.put("pre_olt_port", item.getString("pre_olt_port"));
            row.put("all_num", item.getString("all_num"));
            row.put("pre_online_status", item.getString("pre_online_status"));
            row.put("pre_online_num", item.getString("pre_online_num"));
            row.put("post_olt_ip", item.getString("post_olt_ip"));
            row.put("post_olt_port", item.getString("post_olt_port"));
            row.put("post_online_status", item.getString("post_online_status"));
            row.put("post_online_num", item.getString("post_online_num"));
            data.add(row);
        }

        return data;
    }

    /**
     * 转换PON检测详情数据格式
     */
    private List<Map<String, Object>> convertDetailData(List<JSONObject> detailData) {
        List<Map<String, Object>> data = new ArrayList<>();

        for (JSONObject item : detailData) {
            Map<String, Object> row = new HashMap<>();
            row.put("access_code", item.getString("access_code"));
            row.put("crm_product_id", item.getString("crm_product_id"));
            row.put("service_type", item.getString("service_type"));
            row.put("customer_type", item.getString("customer_type"));
            row.put("service_level", item.getString("service_level"));
            row.put("customer", item.getString("customer"));
            row.put("customer_manager", item.getString("customer_manager"));
            row.put("customer_manager_phone", item.getString("customer_manager_phone"));
            row.put("is_lifecycle", item.getString("is_lifecycle"));
            row.put("olt_ip", item.getString("olt_ip"));
            row.put("olt_nm_ip", item.getString("olt_nm_ip"));
            row.put("olt_port_code", item.getString("olt_port_code"));
            row.put("access_device_code", item.getString("access_device_code"));
            row.put("access_device_code_loid", item.getString("access_device_code_loid"));
            row.put("entity_code", item.getString("entity_code"));
            row.put("entity_name", item.getString("entity_name"));
            row.put("line_no", item.getString("line_no"));
            row.put("route_code", item.getString("route_code"));
            row.put("obd_code", item.getString("obd_code"));
            row.put("obd_name", item.getString("obd_name"));
            row.put("obd_port_code", item.getString("obd_port_code"));
            row.put("pre_online_status", item.getString("pre_online_status"));
            row.put("process_online_status", item.getString("process_online_status"));
            row.put("pre_rx_power", item.getString("pre_rx_power"));
            row.put("process_rx_power", item.getString("process_rx_power"));
            data.add(row);
        }

        return data;
    }





    /**
     * 获取割接项目信息
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ResponseEntity<JSONObject> get(@PathVariable String id) {
        log.debug("REST request to get entity : {}", id);
        JSONObject projectInfo = cutoverDao.getCutOverProject(id, NRMConstants.SHARDING_GRAPH_DB);

        queryUserNames(Arrays.asList(projectInfo));
        JSONObject resInfoParam = new JSONObject();
        resInfoParam.put("project_id", projectInfo.getString("id"));
        List<JSONObject> resList = cutoverDao.listProjectRes(resInfoParam, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtils.isEmpty(resList)) {
            resList = new ArrayList<>();
        }
        JSONObjectUtil.convertBigNumberToString(resList);
        projectInfo.put("resList", resList);
        return ResponseEntity.ok()
                .body(projectInfo);
    }

    @PostMapping("/influenceScope")
    public JSONObject influenceScope(@RequestBody JSONObject request) {
        String projectId = request.getString("project_id");
        JSONObject response = new JSONObject();
        List<JSONObject> actionList = cutoverDao.listProjectRunningAction(request, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtil.isNotEmpty(actionList)) {
            response.put("result", "fail");
            response.put("message", "当前有任务正在运行");
            return response;
        }

        if (ObjectUtils.isEmpty(projectId)) {
            response.put("result", "fail");
            response.put("message", "缺少projectId");
            return response;
        }

        String batchNo = generateBatchNo();
        JSONObject actionStatus = new JSONObject();
        actionStatus.put("batch_no", batchNo);
        actionStatus.put("status", 0); // 运行状态设置为0
        actionStatus.put("project_id", projectId);
        cutoverDao.insertActionStatus(actionStatus, NRMConstants.SHARDING_GRAPH_DB); // 插入批次号



        Runnable r = ()->{
            try {
                projectInfluenceService.influenceRoute(request,batchNo);
                projectInfluenceService.influenceRouteCarryCFS(request);
                projectInfluenceService.influenceBusiness(request,batchNo);
                projectInfluenceService.influenceOptGroup(request, batchNo);
            }catch (Exception ex) {
                log.error(ex.getMessage(),ex);
            }finally {
                cutoverDao.updateActionStatus(batchNo, NRMConstants.SHARDING_GRAPH_DB);
            }

        };
        executorService.submit(r);


        response.put("result", "success");
        return response;

    }




    @PostMapping("/running-status")
    public JSONObject queryProjectRunning(@RequestBody JSONObject request) {

        JSONObject response = new JSONObject();
        String projectId = request.getString("project_id");
        if (ObjectUtils.isEmpty(projectId)) {
            response.put("status", "running");
            return response;
        }
        List<JSONObject> actionList = cutoverDao.listProjectRunningAction(request, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtils.isEmpty(actionList)) {
            response.put("status", "idle");
        }else{
            response.put("status", "running");
        }
        log.info("检测状态, {}", response);
        return response;
    }





    public JSONObject ponCheck (JSONObject request, String action, List<JSONObject> targetPonList) {

        String batchNo = generateBatchNo();
        JSONObject result =  projectPONCheckService.ponCheck(request, action, targetPonList, batchNo);

        return result;
    }




    // 判断割接工单是否进入了割接阶段
    boolean checkIsPost (String projectId) {
        JSONObject checkParam = new JSONObject();

        checkParam.put("project_id", projectId);
        checkParam.put("action", ACTION_PROCESS_PON_CHECK);
        PageResponse<JSONObject> processPage =  cutoverDao.queryProcessPONCheck(checkParam, 10,1,NRMConstants.SHARDING_GRAPH_DB);
        checkParam.put("action", ACTION_POST_PON_CHECK);
        PageResponse<JSONObject> postPage =  cutoverDao.queryProcessPONCheck(checkParam, 10,1,NRMConstants.SHARDING_GRAPH_DB);

        // 统计割接过程中在线个数
        List<JSONObject> processData = processPage.getData();
        int processSize = processData.stream().filter(d->ObjectUtil.isNotEmpty(d.getString("process_online_status")) && d.getString("process_online_status").equals("online")).collect(Collectors.toList()).size();

        // 统计割接后在线个数
        List<JSONObject> postData = postPage.getData();
        int postSize = postData.stream().filter(d->ObjectUtil.isNotEmpty(d.getString("process_online_status")) && d.getString("process_online_status").equals("online")).collect(Collectors.toList()).size();

        // 如果割接过程或割接后有在线用户,则认为完成了割接前的准备
        if (processSize>0 || postSize>0) {
            return true;
        }else {
            return false;
        }
    }

    boolean checkIsPreReady (String projectId) {
        JSONObject checkParam = new JSONObject();

        checkParam.put("project_id", projectId);
        checkParam.put("action", ACTION_PRE_PON_CHECK);
        PageResponse<JSONObject> processPage =  cutoverDao.queryProcessPONCheck(checkParam, 1000000,1,NRMConstants.SHARDING_GRAPH_DB);

        // 统计割接过程中在线个数
        List<JSONObject> processData = processPage.getData();
        int processSize = processData.stream().filter(d->ObjectUtil.isNotEmpty(d.getString("process_online_status")) && d.getString("process_online_status").equals("online")).collect(Collectors.toList()).size();

        if (processSize>0) {
            return true;
        } else {
            return false;
        }


    }


    /**
     * PON口检测
     * @param request
     * @return
     */
    @PostMapping("/ponCheck")
    public JSONObject prePONCheck(@RequestBody JSONObject request) {

        // 确认有没有进行割接后检测
        String projectId = request.getString("project_id");
        JSONObject result = new JSONObject();
        if (!checkIsPost(projectId)) {
            result = ponCheck(request, ACTION_PRE_PON_CHECK, new ArrayList<>());

            JSONObject projectInfo = request.getJSONObject("projectInfo");
            projectInfo.put("status", "割接前检测在线");
            saveCutOverProject(projectInfo);
        } else {
            result.put("status", "fail");
            result.put("message", "割接准备阶段已结束，无法发起");
        }
        return result;
    }
    // ACTION_PROCESS_PON_CHECK

    public List<JSONObject> getTargetPonList(JSONObject request) {
        JSONObject projectInfo = request.getJSONObject("projectInfo");
        saveCutOverProject(projectInfo);
        JSONArray resList = projectInfo.getJSONArray("resList");
        List<JSONObject> targetPonList = new ArrayList<>();
        int resListSize = resList.size();
        for (int i = 0; i < resListSize; i++) {
            JSONObject resObj = resList.getJSONObject(i);
            if (resObj.getString("res_role").equals("target")) {
                resObj.put("ip",resObj.getString("dev_ip"));
                resObj.put("pon_code",resObj.getString("entity_code"));
                targetPonList.add(resObj);
            }
        }
        return targetPonList;
    }

    @PostMapping("/processPonCheck")
    public JSONObject processPONCheck(@RequestBody JSONObject request) {
        List<JSONObject> targetPonList = getTargetPonList(request);
        String projectId = request.getString("project_id");
        JSONObject result = new JSONObject();
        if (checkIsPreReady(projectId)) {
            result =  ponCheck(request, ACTION_PROCESS_PON_CHECK, targetPonList);

            JSONObject projectInfo = request.getJSONObject("projectInfo");
            projectInfo.put("status", "割接中恢复验证");
            saveCutOverProject(projectInfo);

            return result;
            // result.put("status", "success");
        }else{
            result.put("status", "fail");
            result.put("message", "割接前无业务在线,无法检测");
        }



        return result;
    }

    @PostMapping("/postPONCheck")
    public JSONObject postPONCheck(@RequestBody JSONObject request) {
        List<JSONObject> targetPonList = getTargetPonList(request);

        String projectId = request.getString("project_id");
        JSONObject result = new JSONObject();
        if (checkIsPreReady(projectId)) {
            result = ponCheck(request, ACTION_POST_PON_CHECK, targetPonList);

            JSONObject projectInfo = request.getJSONObject("projectInfo");
            projectInfo.put("status", "割接后恢复验证");
            saveCutOverProject(projectInfo);
            return result;
            //result.put("status", "success");
        } else {
            result.put("status", "fail");
            result.put("message", "尚未完成割接前的检测,无法检测");
        }
        return result;
    }


    @PostMapping("/downloadInfluenceRoute")
    public ResponseEntity<ExportResponseDTO> downloadInfluenceRoute(@RequestBody JSONObject request) {
        try {
            log.info("🚀 开始导出影响光路数据");

            // 查询数据
            int currentPage = 1;
            int pageSize = 100000;
            PageResponse<JSONObject> pageResponse = cutoverDao.queryPrProjectRoute(request, pageSize, currentPage, NRMConstants.SHARDING_GRAPH_DB);

            // 转换数据格式
            List<Map<String, Object>> data = new ArrayList<>();
            for (JSONObject item : pageResponse.getData()) {
                Map<String, Object> row = new HashMap<>();
                for (String key : item.keySet()) {
                    row.put(key, item.get(key));
                }
                data.add(row);
            }

            // 定义列
            List<String> columns = Arrays.asList(
                "光缆编码:net_code","缆段编码:cable_name","缆段名称:cable_code","纤芯:line_code","局向光纤编码:jx_fiber_code","光路编码:route_code",
                "光路名称:route_name","局向A端设施编码:jx_a_device_code","局向A端设施名称:jx_a_device_name","局向A设施端口:jx_a_port_code","局向Z端设施编码:jx_z_device_code",
                "局向Z端设施名称:jx_z_device_name","局向Z设施端口:jx_z_port_code","光路A设备类型:a_device_type","光路A端设备编码:a_device_code","光路A端设备名称:a_device_name",
                "光路A端设备IP地址:a_device_ip","光路A端端口:a_port_code","光路A端端口类型:a_port_type","光路Z设备类型:z_device_type","光路Z端设备编码:z_device_code","光路Z端设备名称:z_device_name",
                "光路Z端设备IP地址:z_device_ip","光路Z端端口:z_port_code","光路Z端端口类型:z_port_type","资源编码:source_code"
            );

            // 使用通用导出服务
            ExportResponseDTO result = documentExportService.exportToDocumentSecurity(
                data, columns, "影响光路", "影响光路导出",
                "割接管理", "/api/cutover/downloadInfluenceRoute", "影响光路导出"
            );

            if (result.isSuccess()) {
                result.setDataCount(data.size());
                log.info("✅ 影响光路导出成功: {}", result.getFileName());
            } else {
                log.error("❌ 影响光路导出失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 影响光路导出异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    @PostMapping("/downloadRouteGroup")
    public ResponseEntity<ExportResponseDTO> downloadRouteGroup(@RequestBody JSONObject request) {
        try {
            log.info("🚀 开始导出影响光路组数据");

            // 查询数据
            int currentPage = 1;
            int pageSize = 100000;
            JSONObject param = (JSONObject) JSON.toJSON(request);
            PageResponse<JSONObject> pageResponse = cutoverDao.queryPrProjectOptGroup(param, pageSize, currentPage, NRMConstants.SHARDING_GRAPH_DB);

            // 转换数据格式
            List<Map<String, Object>> data = new ArrayList<>();
            for (JSONObject item : pageResponse.getData()) {
                Map<String, Object> row = new HashMap<>();
                for (String key : item.keySet()) {
                    row.put(key, item.get(key));
                }
                data.add(row);
            }

            // 定义列
            List<String> columns = Arrays.asList(
                "保护组编码:opt_group_code","保护组名称:opt_group_name","割接影响:cutover_check_result","包含光路:opt_codes",
                "割接影响光路:effect_opt_codes","包含光路数量:opt_code_num","影响光路数量:effect_opt_code_num","光路组隐患:risk_check_result"
            );

            // 使用通用导出服务
            ExportResponseDTO result = documentExportService.exportToDocumentSecurity(
                data, columns, "影响光路组", "影响光路组导出",
                "割接管理", "/api/cutover/downloadRouteGroup", "影响光路组导出"
            );

            if (result.isSuccess()) {
                result.setDataCount(data.size());
                log.info("✅ 影响光路组导出成功: {}", result.getFileName());
            } else {
                log.error("❌ 影响光路组导出失败: {}", result.getMessage());
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 影响光路组导出异常: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    void exportToSheet(XSSFWorkbook workbook, String sheetName, List<JSONObject> data, List<String> columns) {
        XSSFSheet sheet = workbook.createSheet(sheetName);
        XSSFRow header = sheet.createRow(0);
        int i=0;
        for (String column : columns) {
            String columnName=column.split(":")[0];
            header.createCell(i).setCellValue(columnName);
            i++;
        }
        int rowIndex = 1;
        for (JSONObject l: data) {
            XSSFRow row = sheet.createRow(rowIndex++);
            i=0;
            for (String column : columns) {
                String columnCode=column.split(":")[1];
                row.createCell(i).setCellValue(l.getString(columnCode));
                i++;
            }
        }
    }


    public String generateBatchNo() {
        return projectService.generateBatchNo();
    }



}
