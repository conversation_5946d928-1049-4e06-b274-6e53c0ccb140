package com.telecom.nrm.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.PmIpranUserDao;
import com.telecom.nrm.domain.NRMConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/getPmIpranUserInfo")
public class PmIpranUserController {

    @Autowired
    private PmIpranUserDao pmIpranUserDao;

    /**
     * 查询IPRAN资料
     * @param example
     * @param pageable
     * @return
     */
    @GetMapping("")
    public BiyiPageResult<JSONObject> getPmIpranUserInfo(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        PageResponse<JSONObject> pageResponse = pmIpranUserDao.getPmIpranUserInfo(jsonObject, pageable.getSize(), pageable.getPage(), NRMConstants.SHARDING_CODE);
        JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
        return new BiyiPageResult(pageResponse.getData(), pageResponse.getPageInfo().getTotalCount(), pageResponse.getPageInfo().getTotalCount());
    }

}
