package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.ProjectInfluenceService;
import com.telecom.nrm.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 这是常青藤的接口
 */
@RestController
@RequestMapping("/api/IVY")
@Slf4j
public class IVYController {

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss'T'SSS");
    @Autowired
    ProjectService projectService;

    @Autowired
    ProjectInfluenceService projectInfluenceService;

    @Autowired
    CutoverDao cutoverDao;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @PostMapping("/resource-influence")
    public JSONObject resourceInfluence(@RequestBody JSONObject params) {
        JwtUser jwtUser = SecurityContext.getJwtUser();
        LocalDateTime now = LocalDateTime.now();
        params.put("create_op", jwtUser.getUsername());
        params.put("project_type_id", NRMConstants.PROJECT_TYPE_OTHER);
        params.put("name","常青藤" + now.format(formatter));
        params.put("remark", now.format(formatter));
        JSONObject projectInfo = projectService.saveProject(params);
        String projectId = projectInfo.getString("id");
        projectInfo.put("project_id", projectId);
        String batchNo = projectService.generateBatchNo();

        JSONObject actionStatus = new JSONObject();
        actionStatus.put("batch_no", batchNo);
        actionStatus.put("status", 0); // 运行状态设置为0
        actionStatus.put("project_id", projectId);
        cutoverDao.insertActionStatus(actionStatus, NRMConstants.SHARDING_GRAPH_DB); // 插入批次号

        Runnable r = ()->{
            try {
                projectInfluenceService.influenceRoute(projectInfo,batchNo);
                projectInfluenceService.influenceRouteCarryCFS(projectInfo);
                projectInfluenceService.influenceBusiness(projectInfo,batchNo);
            }catch (Exception ex) {
                log.error(ex.getMessage(),ex);
            }finally {
                cutoverDao.updateActionStatus(batchNo, NRMConstants.SHARDING_GRAPH_DB);
            }

        };

        executorService.submit(r);

        return projectInfo;
    }

    @GetMapping("/running-status")
    public JSONObject queryProjectRunning(@RequestParam(name="projectId") String projectId) {
        JSONObject request = new JSONObject();
        request.put("project_id", projectId);
        JSONObject response = new JSONObject();
        // String projectId = request.getString("project_id");
        if (ObjectUtils.isEmpty(projectId)) {
            response.put("status", "running");
            return response;
        }
        List<JSONObject> actionList = cutoverDao.listProjectRunningAction(request, NRMConstants.SHARDING_GRAPH_DB);
        if (ObjectUtils.isEmpty(actionList)) {
            response.put("status", "idle");
        }else{
            response.put("status", "running");
        }
        log.info("检测状态, {}", response);
        return response;
    }

    @GetMapping("/query-project-service")
    public JSONObject queryServices (@RequestParam(name="projectId") String projectId) {
        int currentPage = 1;
        int pageSize = 100000;
        JSONObject param = new JSONObject();
        param.put("project_id", projectId);

        PageResponse<JSONObject> pageResponse = cutoverDao.queryProjectService(param, pageSize, currentPage, NRMConstants.SHARDING_GRAPH_DB);
        JSONObject response = new JSONObject();
        response.put("data", pageResponse.getData());
        response.put("result", "success");
        return response;
    }
}
