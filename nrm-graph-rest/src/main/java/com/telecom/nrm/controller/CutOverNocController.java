package com.telecom.nrm.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.web.config.security.JwtUser;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CutoverDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.ProjectInfluenceService;
import com.telecom.nrm.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 这是常青藤的接口
 */
@RestController
@RequestMapping("/api/cut-over-noc")
@Slf4j
public class CutOverNocController {

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    ProjectService projectService;

    @Autowired
    ProjectInfluenceService projectInfluenceService;

    @Autowired
    CutoverDao cutoverDao;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @PostMapping("/route-influence")
    public JSONObject resourceInfluence(@RequestBody JSONObject params) throws ExecutionException, InterruptedException {
        JwtUser jwtUser = SecurityContext.getJwtUser();
        LocalDateTime now = LocalDateTime.now();
        params.put("create_op", jwtUser.getUsername());
        params.put("project_type_id", NRMConstants.PROJECT_TYPE_CUT_OVER_FROM_NOC);
        params.put("name","集约化平台" + now.format(formatter));
        params.put("remark", now.format(formatter));
        params.put("area_code",params.getString("city"));
        params.put("order_no", "集约化平台" + params.getString("remark"));
        params.put("start_time", now.format(formatter));
        params.put("end_time", now.format(formatter));
        JSONObject projectInfo = projectService.saveCutOverProject(params);
        String projectId = projectInfo.getString("id");
        projectInfo.put("project_id", projectId);
        String batchNo = projectService.generateBatchNo();

        JSONObject actionStatus = new JSONObject();
        actionStatus.put("batch_no", batchNo);
        actionStatus.put("status", 0); // 运行状态设置为0
        actionStatus.put("project_id", projectId);
        cutoverDao.insertActionStatus(actionStatus, NRMConstants.SHARDING_GRAPH_DB); // 插入批次号

        Callable r = ()->{
            try {
                projectInfluenceService.influenceRoute(projectInfo,batchNo);
            }catch (Exception ex) {
                log.error(ex.getMessage(),ex);
            }finally {
                cutoverDao.updateActionStatus(batchNo, NRMConstants.SHARDING_GRAPH_DB);
            }
            return true;

        };

        FutureTask<Boolean> task =new FutureTask<Boolean>(r);

        executorService.submit(task);

        task.get();

        JSONObject response =queryRoutes(projectId);


        return response;
    }



    public JSONObject queryRoutes (@RequestParam(name="projectId") String projectId) {
        int currentPage = 1;
        int pageSize = 100000;
        JSONObject param = new JSONObject();
        param.put("project_id", projectId);

        PageResponse<JSONObject> pageResponse = cutoverDao.queryPrProjectRoute(param, pageSize, currentPage, NRMConstants.SHARDING_GRAPH_DB);
        JSONObject response = new JSONObject();
        List<JSONObject> data = pageResponse.getData();
        data= data.stream().map(d->{
            JSONObject route = new JSONObject();
            route.put("cable_segment_code", d.getString("cable_code"));
            route.put("cable_segment_name", d.getString("cable_name"));
            route.put("line_code", d.getInteger("line_code"));
            route.put("cable_code", d.getString("net_code"));
            route.put("cable_name", d.getString("net_name"));
            route.put("jx_fiber_code", d.getString("jx_fiber_code"));
            route.put("opt_road_code", d.getString("route_code"));
            route.put("opt_road_name", d.getString("route_name"));

            route.put("jx_a_device_code",d.getString("jx_a_device_code"));
            route.put("jx_a_device_name",d.getString("jx_a_device_name"));
            route.put("jx_a_port_code",d.getString("jx_a_port_code"));

            route.put("jx_z_device_code",d.getString("jx_z_device_code"));
            route.put("jx_z_device_name",d.getString("jx_z_device_name"));
            route.put("jx_z_port_code",d.getString("jx_z_port_code"));


            route.put("a_device_code",d.getString("a_device_code"));
            route.put("a_device_name",d.getString("a_device_name"));
            route.put("a_port_code",d.getString("a_port_code"));

            route.put("z_device_code",d.getString("z_device_code"));
            route.put("z_device_name",d.getString("z_device_name"));
            route.put("z_port_code",d.getString("z_port_code"));

            return route;
        }).collect(Collectors.toList());
        response.put("data", data);
        response.put("result", "success");
        return response;
    }
}
