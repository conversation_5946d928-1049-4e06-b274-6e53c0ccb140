package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.GlGjDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/glgj")
@Slf4j
public class GlGjController {

    @Autowired
    GlGjDao glGjDao;

    @Autowired
    RegionDao regionDao;

    @GetMapping("")
    public BiyiPageResult<JSONObject> getCustViewList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        // log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        PageResponse<JSONObject> pageResponse =  glGjDao.gl_gj_fiber_pair_m_query(jsonObject ,pageable.getSize(),pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        log.info("pageResponse");
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }


    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary_1(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            System.out.println(Region2List.get(i).getAreaLevelId());
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }

    @PostMapping("/upload_fiber_pair/{ds}")
    public void upload_circuit_pair(@PathVariable String ds,@RequestParam("file") MultipartFile file) throws IOException {

        //System.out.println("circuit_pair_upload---"+ds);
        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    //System.out.println("current_data"+current_data);
                    for (int j = 0; j < column; j++) {
                        //System.out.println(j+","+sheet.getRow(i).getCell(j));
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("areaname",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==1 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("busi_type",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==2 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("group_name",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==3 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("busi_number",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==4 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("fiber",sheet.getRow(i).getCell(j).getStringCellValue());
                    }
                    //System.out.println("current_data"+current_data+","+ds);
                    glGjDao.gl_gj_fiber_pair_m_insert(current_data, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                }
            }
        }
    }


    @PostMapping("/upload_glgj_pair/{ds}")
    public void upload_glgj_pair(@PathVariable String ds,@RequestParam("file") MultipartFile file) throws IOException {

        //System.out.println("circuit_pair_upload---"+ds);
        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();
                    System.out.println("current_data"+current_data);
                    for (int j = 0; j < column; j++) {
                        System.out.println(j+","+sheet.getRow(i).getCell(j));
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("areaname",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==1 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("busi_type",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==2 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("group_name",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==3 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("busi_number",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==4 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("fiber",sheet.getRow(i).getCell(j).getStringCellValue());
                    }
                    //System.out.println("current_data"+current_data+","+ds);
                    glGjDao.gl_gj_fiber_pair_m_insert(current_data, NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(ds));
                }
            }
        }
    }



}
