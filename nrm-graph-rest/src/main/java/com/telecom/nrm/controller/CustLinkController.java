package com.telecom.nrm.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.identitymanagement.model.User;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.CustViewMemberDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.graph.api.GraphRequest;
import com.telecom.nrm.service.CustLinkService;
import com.telecom.nrm.service.MstpService;
import com.telecom.nrm.service.DocumentExportService;
import com.telecom.nrm.dto.ExportResponseDTO;
import org.springframework.http.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import static javax.management.openmbean.SimpleType.STRING;

@RestController
@RequestMapping("/api/custlinkapi")
@Slf4j
public class CustLinkController {

    final Long ENTITY_SPEC_ID = 1024600001L;

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    RegionDao regionDao;

    @Autowired
    CustViewMemberDao custViewMemberDao;

    @Autowired
    MstpService mstpService;

    @Autowired
    CustLinkService custLinkService;

    @Autowired
    DocumentExportService documentExportService;

    @Autowired
    CustViewRiskController custViewRiskController;

    @GetMapping("")
    public BiyiPageResult<JSONObject> getList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;

        /*

        log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        String[] circuit = jsonObject.getString("circuit_code").split(" ");
        List<String> codes = new ArrayList<>();
        Collections.addAll(codes, circuit);
        jsonObject.put("codes",codes);
         */


        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCustLink(jsonObject ,pageable.getSize()
                ,pageable.getPage(),NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        log.info("pageResponse");
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @PostMapping("/download")
    public void download(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws IOException {
        log.info("jsonObject"+jsonObject);
        JSONArray selectedRowKeys = jsonObject.getJSONArray("selectedRowKeys");
        JSONArray dataSource = jsonObject.getJSONArray("dataSource");
        log.info("selectedRowKeys"+selectedRowKeys);
        log.info("dataSource"+dataSource);
        JSONArray result = new JSONArray();
        for(int i =0;i<selectedRowKeys.size();i++) {
            for (int j = 0; j < dataSource.size(); j++) {
                if (selectedRowKeys.getString(i).equals(dataSource.getJSONObject(j).getString("id"))) {
                    result.add(dataSource.getJSONObject(j));
                    break;
                }
            }
        }
        log.info("result"+result);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        XSSFRow header = sheet.createRow(0);
        header.createCell(0).setCellValue("地市");
        header.createCell(1).setCellValue("业务类型");
        header.createCell(2).setCellValue("客户名称");
        header.createCell(3).setCellValue("业务号码");
        header.createCell(4).setCellValue("电路编号");
        header.createCell(5).setCellValue("A端装机地址");
        header.createCell(6).setCellValue("Z端装机地址");

        // 填充数据
        int rowIndex = 1;
        for (int i =0; i< result.size();i++) {
            XSSFRow row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(result.getJSONObject(i).getString("area_name"));
            row.createCell(1).setCellValue(result.getJSONObject(i).getString("service_res_type_name"));
            row.createCell(2).setCellValue(result.getJSONObject(i).getString("cus_name"));
            row.createCell(3).setCellValue(result.getJSONObject(i).getString("acc_code"));
            row.createCell(4).setCellValue(result.getJSONObject(i).getString("circuit_code"));
            row.createCell(5).setCellValue(result.getJSONObject(i).getString("a_addr_name"));
            row.createCell(6).setCellValue(result.getJSONObject(i).getString("z_addr_name"));
        }

        // 设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=list.xlsx");

        // 将Excel文档写入响应流中
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }



    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){

            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }

    /*
    @PostMapping("/upload/{id}")
    public void upload(@PathVariable String id,@RequestParam("file") MultipartFile file) throws IOException {

        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    for (int j = 0; j < column; j++) {
                        log.info("wb:---" + sheet.getRow(i).getCell(j));
                    }
                }
            }
        }
    }
     */

    /*
    @PostMapping("/upload_opt_pair/{ds}")
    public void upload_opt_pair(@PathVariable String ds,@RequestParam("file") MultipartFile file) throws IOException {


        List<Map<String, String>> list = null;
        Sheet sheet = null;
        Row row = null;
        String cellData = null;
        List<String> keys = null;


        MultipartFile file_current = (MultipartFile) file;
        Workbook wb = null;
        String fileName = file_current.getOriginalFilename();
        String extString = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(file_current.getInputStream());
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(file_current.getInputStream());
            } else {
                wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (wb != null) {
            list = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            row = sheet.getRow(0);
            int column = row.getPhysicalNumberOfCells();
            keys = new ArrayList<>();
            log.info("wb:" + rownum + "," + column);
            if(rownum >1){
                for (int i = 1; i < rownum; i++) {
                    JSONObject current_data = new JSONObject();

                    for (int j = 0; j < column; j++) {
                        if(j==0 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("areacode",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==1 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("a_fiber",sheet.getRow(i).getCell(j).getStringCellValue());
                        if(j==2 && !Objects.isNull(sheet.getRow(i).getCell(j))) current_data.put("z_fiber",sheet.getRow(i).getCell(j).getStringCellValue());
                    }
                    custLinkInterfaceDao.fiber_pair_m_upload_insert(current_data, NRMConstants.BCO3DatabaseMap.get(ds));
                }
            }
        }
    }
     */





    /*
    @GetMapping("/circuit_pair_query")
    public BiyiPageResult<JSONObject> circuit_pair_query(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        String[] params = null;
        if(!Objects.isNull(jsonObject.getString("param"))&& jsonObject.getString("param")!=null &&jsonObject.getString("param")!=""){
            params = jsonObject.getString("param").split("[,， ]+");
        }
        jsonObject.put("params",params);
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair(jsonObject ,pageable.getSize(),pageable.getPage(),NRMConstants.BCO3DatabaseMap.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

     */



    /*
    @GetMapping("/circuit_pair_query/dictionary")
    public ResponseEntity<JSONObject> dictionary_1(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }
     */



    /*
    @GetMapping("/shuangluyou_result_query")
    public BiyiPageResult<JSONObject> shuangluyou_result_query(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);

        // JSONObject result = new JSONObject();
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        List<String> code_param = new ArrayList<>();
        String[] circuit_code = jsonObject.getString("circuit_code").split("_");
        List<JSONObject> pair_list_data =   new ArrayList<>();

        JSONObject request = new JSONObject();
        request.put("id", circuit_code[0]);
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair(request ,1000,1,NRMConstants.BCO3DatabaseMap.get(circuit_code[1]));
        JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
        pair_list_data.addAll(pageResponse.getData());
        JSONObject result =new JSONObject();
        if(pair_list_data.size()>=1) {
            JSONObject current_circuits_pair = pair_list_data.get(0);
            List<String> codes = new ArrayList();
            code_param.add(current_circuits_pair.getString("aCode"));
            code_param.add(current_circuits_pair.getString("zCode"));
        }
        jsonObject.put("codes", code_param);
        PageResponse<JSONObject> pageResponse_1 =  custLinkInterfaceDao.shuangluyou_result_query(jsonObject ,10000,pageable.getPage(),NRMConstants.BCO3DatabaseMap.get(circuit_code[1]));
        log.info("pageResponse");
        if(pageResponse_1!=null &&pageResponse_1.getData()!=null && pageResponse_1.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse_1.getData());
            data.addAll(pageResponse_1.getData());
            totalCount = totalCount+ pageResponse_1.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

     */


    /*
    @PostMapping("/sly_xj")
    public void sly_xj(@RequestBody JSONObject jsonObject){
        custLinkService.sly_xj(jsonObject);
    }

    @PostMapping("/smx_sly_xj")
    public void smx_sly_xj(@RequestBody JSONObject jsonObject){
        custLinkService.smx_sly_xj(jsonObject);
    }

    @PostMapping("/opt_sly_xj")
    public void opt_sly_xj(@RequestBody JSONObject jsonObject){
        custLinkService.opt_sly_xj(jsonObject);
    }
     */


    /*
    @PostMapping("/queryBsuiToPipeCableSegment")
    public JSONObject queryBsuiToPipeCableSegment(@RequestBody JSONObject jsonObject, HttpServletResponse response){
        return custLinkService.queryOptRoadsToPipeSegment(jsonObject);
    }

     */


    /*
    @GetMapping("getOptList")
    public BiyiPageResult<JSONObject> getOptList(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        log.info("jsonobject"+";"+jsonObject.getString("ds")+";"+ jsonObject);
        String[] circuit = jsonObject.getString("circuit_code").split(" ");
        List<String> codes = new ArrayList<>();
        Collections.addAll(codes, circuit);
        jsonObject.put("codes",codes);
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.fiber_pair_m_upload_query(jsonObject ,pageable.getSize(),pageable.getPage(),NRMConstants.BCO3DatabaseMap.get(jsonObject.getString("ds")));
        log.info("pageResponse");
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @GetMapping("/getOptList/dictionary")
    public ResponseEntity<JSONObject> getOptList_dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }
     */

    /*
    @PostMapping("/deleteOptRecord/{id}")
    public Integer deleteOptRecord(@PathVariable Long id,@RequestBody JSONObject jsonObject) throws IOException {
        return custLinkInterfaceDao.fiber_pair_m_upload_delete(id,NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
    }

     */



    /*
    @GetMapping("/smxyw_a_pair")
    public BiyiPageResult<JSONObject> get_smxyw_a_pair_circuit(@RequestParam(required = false) Map example , BiyiPageRequest pageable){
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        //生命线
        List<Integer> scene_list = new ArrayList<>();
        scene_list.add(33);
        scene_list.add(27);
        int totalCount =0;
        jsonObject.put("scene_list",scene_list);
        jsonObject.put("area_name", jsonObject.getString("ds"));
        PageResponse<JSONObject> pageResponse_1 =  custViewMemberDao.queryList(jsonObject ,10000,1, NRMConstants.SHARDING_GRAPH_DB);
        // JSONObject result = new JSONObject();
        List<JSONObject> list = pageResponse_1.getData();
        list.stream().forEach(i->{i.put("group_label","生命线");i.put("aCode",i.getString("circuit_code"));i.put("accessCode",i.getString("access_code"));i.put("aOptCode",i.getString("opt_code"));});
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuit_pair(jsonObject ,10000,1,NRMConstants.BCO3DatabaseMap.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            if(jsonObject.getString("opt_code").contains("生命线")){

            }else{
                pageResponse.getData().stream().forEach(i->i.put("group_label","成对电路"));
                list.addAll(pageResponse.getData());
            }
            totalCount = list.size();
        }
        return new BiyiPageResult(list,totalCount ,totalCount);
    }

    @GetMapping("/smxyw_a_pair/dictionary")
    public ResponseEntity<JSONObject> smxyw_a_pair_dictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);


        return ResponseEntity.ok().body(meta);
    }
     */

    @PostMapping("/download_cust_view")
    public ResponseEntity<ExportResponseDTO> download_cust_view(@RequestBody JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🎯 [客户视图导出] 接收到客户视图下载请求");
            log.info("📋 [客户视图导出] 请求参数: {}", jsonObject);

            // 参数验证
            if (jsonObject == null || jsonObject.isEmpty()) {
                log.error("❌ [客户视图导出] 请求参数为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("请求参数不能为空"));
            }

            Long sceneId = jsonObject.getLong("scene_id");
            log.info("📍 [客户视图导出] 客户视图ID: {}", sceneId);

            // 1. 查询客户视图数据
            log.info("🔍 [客户视图导出] 步骤1: 开始查询客户视图数据");
            long queryStartTime = System.currentTimeMillis();
            BiyiPageRequest pageable = new BiyiPageRequest();
            pageable.setSize(1000000);
            pageable.setPage(1);
            BiyiPageResult<JSONObject> result = custViewRiskController.circuit_pair_analyze_analyze(jsonObject, pageable);
            long queryEndTime = System.currentTimeMillis();
            log.info("⏱️ [客户视图导出] 步骤1完成: 数据查询耗时 {} ms", (queryEndTime - queryStartTime));

            if (result == null || result.getData() == null || result.getData().size() == 0) {
                log.warn("⚠️ [客户视图导出] 查询结果为空");
                return ResponseEntity.ok(ExportResponseDTO.failure("没有查询到客户视图数据"));
            }

            JSONObjectUtil.convertBigNumberToString(result.getData());
            List<JSONObject> resultData = result.getData();
            log.info("📊 [客户视图导出] 查询到数据量: {} 条", resultData.size());

            // 2. 转换数据格式
            log.info("🔄 [客户视图导出] 步骤2: 开始转换数据格式");
            long convertStartTime = System.currentTimeMillis();
            List<Map<String, Object>> exportData = convertCustomerViewData(resultData);
            long convertEndTime = System.currentTimeMillis();
            log.info("⏱️ [客户视图导出] 步骤2完成: 数据转换耗时 {} ms", (convertEndTime - convertStartTime));
            log.info("📊 [客户视图导出] 转换后数据量: {} 条记录", exportData.size());

            // 3. 定义列
            log.info("📝 [客户视图导出] 步骤3: 定义Excel列结构");
            List<String> columns = Arrays.asList(
                "地市:areacode",
                "客户名称:custName",
                "业务号码:accessCode",
                "电路编号:aCode",
                "光路编码:aOptCode",
                "光路名称:aOptRoad",
                "电路备注:aNotes",
                "成对业务号码:zAccessCode",
                "成对电路编号:zCode",
                "成对光路编码:zOptCode",
                "成对光路名称:zOptRoad",
                "成对电路备注:zNotes",
                "检测结果:result"
            );
            log.info("📋 [客户视图导出] 步骤3完成: 定义了 {} 个列", columns.size());

            // 4. 使用通用导出服务
            log.info("📤 [客户视图导出] 步骤4: 开始调用文档导出服务");
            long exportStartTime = System.currentTimeMillis();
            ExportResponseDTO exportResult = documentExportService.exportToDocumentSecurity(
                exportData, columns, "客户视图数据", "客户视图下载",
                "客户视图", "/api/custlinkapi/download_cust_view", "客户视图数据导出"
            );
            long exportEndTime = System.currentTimeMillis();
            log.info("⏱️ [客户视图导出] 步骤4完成: 文档导出服务耗时 {} ms", (exportEndTime - exportStartTime));

            if (exportResult.isSuccess()) {
                exportResult.setDataCount(exportData.size());
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("✅ [客户视图导出] 导出成功完成!");
                log.info("📁 [客户视图导出] 文件名: {}", exportResult.getFileName());
                log.info("📊 [客户视图导出] 数据量: {} 条", exportData.size());
                log.info("⏱️ [客户视图导出] 总耗时: {} ms", totalTime);
                log.info("📈 [客户视图导出] 性能统计 - 查询: {}ms, 转换: {}ms, 导出: {}ms",
                    (queryEndTime - queryStartTime), (convertEndTime - convertStartTime), (exportEndTime - exportStartTime));
            } else {
                log.error("❌ [客户视图导出] 导出失败: {}", exportResult.getMessage());
            }

            return ResponseEntity.ok(exportResult);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("💥 [客户视图导出] 导出异常，总耗时: {} ms", totalTime);
            log.error("💥 [客户视图导出] 异常详情: {}", e.getMessage(), e);
            return ResponseEntity.ok(ExportResponseDTO.failure("导出失败: " + e.getMessage()));
        }
    }

    /**
     * 转换客户视图数据格式
     */
    private List<Map<String, Object>> convertCustomerViewData(List<JSONObject> resultData) {
        log.info("🔄 [数据转换] 开始转换客户视图数据格式，输入数据量: {}", resultData.size());
        List<Map<String, Object>> data = new ArrayList<>();

        try {
            int processedCount = 0;

            for (JSONObject item : resultData) {
                try {
                    Map<String, Object> row = new HashMap<>();
                    row.put("areacode", item.getString("areacode") != null ? item.getString("areacode") : "");
                    row.put("custName", item.getString("custName") != null ? item.getString("custName") : "");
                    row.put("accessCode", item.getString("accessCode") != null ? item.getString("accessCode") : "");
                    row.put("aCode", item.getString("aCode") != null ? item.getString("aCode") : "");
                    row.put("aOptCode", item.getString("aOptCode") != null ? item.getString("aOptCode") : "");
                    row.put("aOptRoad", item.getString("aOptRoad") != null ? item.getString("aOptRoad") : "");
                    row.put("aNotes", item.getString("aNotes") != null ? item.getString("aNotes") : "");
                    row.put("zAccessCode", item.getString("zAccessCode") != null ? item.getString("zAccessCode") : "");
                    row.put("zCode", item.getString("zCode") != null ? item.getString("zCode") : "");
                    row.put("zOptCode", item.getString("zOptCode") != null ? item.getString("zOptCode") : "");
                    row.put("zOptRoad", item.getString("zOptRoad") != null ? item.getString("zOptRoad") : "");
                    row.put("zNotes", item.getString("zNotes") != null ? item.getString("zNotes") : "");
                    row.put("result", item.getString("result") != null ? item.getString("result") : "");

                    data.add(row);
                    processedCount++;

                    // 每处理1000条记录输出一次进度
                    if (processedCount % 1000 == 0) {
                        log.info("📊 [数据转换] 已处理 {} / {} 条记录", processedCount, resultData.size());
                    }

                } catch (Exception e) {
                    log.error("❌ [数据转换] 转换第 {} 条记录时出错: {}", processedCount + 1, e.getMessage());
                    log.error("❌ [数据转换] 出错的数据: {}", item);
                }
            }

            log.info("✅ [数据转换] 转换完成");
            log.info("📊 [数据转换] 成功转换: {} 条记录", processedCount);

        } catch (Exception e) {
            log.error("💥 [数据转换] 数据转换过程中发生异常: {}", e.getMessage(), e);
        }

        log.info("🎯 [数据转换] 最终输出数据量: {}", data.size());
        return data;
    }

    /*
    @PostMapping("/queryMSTPCircuitPairToPipeSegment")
    public JSONObject queryMSTPCircuitPairToPipeSegment(@RequestBody JSONObject jsonObject) throws IOException {
        return mstpService.queryMSTPCircuitPairToPipeSegment(jsonObject);
    }

     */
}
