package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.NetConfigDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.impl.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api/net-config")
@Slf4j
public class NetConfigController {

    @Autowired
    NetConfigDao configDao;

    @GetMapping("/ori-config-file/{id}")
    public ResponseEntity<JSONObject> get(@PathVariable BigDecimal id) {
        log.debug("REST request to get entity : {}", id);
        JSONObject getResult  =  configDao.getNetConfigFile(id, NRMConstants.SHARDING_CODE);
        JSONObject getContent = getResult.getJSONObject("content");
        String content = "";
        String[] configProperties = {"AllConf_IPF","IPM_Congif_4A_WZ"};
        for (String configProperty : configProperties) {
            if (getContent.containsKey(configProperty)) {
                content = getContent.getString(configProperty);
                break;
            }
        }
        JSONObject result = new JSONObject();
        result.put("content", content);
        return ResponseEntity.ok().body(result);
    }
}
