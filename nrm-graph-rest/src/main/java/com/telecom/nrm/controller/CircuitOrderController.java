package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.common.web.config.security.SecurityContext;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.aop.LogAnnotation;
import com.telecom.nrm.dao.CustLinkInterfaceDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.domain.ServiceType;
import com.telecom.nrm.service.CustLinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Role;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.RolesAllowed;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/4/8 15:03
 **/
@RestController
@RequestMapping("/api/circuitsorderapi")
@Slf4j
public class CircuitOrderController {

    @Autowired
    CustLinkInterfaceDao custLinkInterfaceDao;

    @Autowired
    CustLinkService custLinkService;

    @Autowired
    RegionDao regionDao;

    @GetMapping("")
    @LogAnnotation(interfaceName ="根据电路查询业务电路")
    public BiyiPageResult<JSONObject> getPage(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;

        // int i=1/0;
        // int i=1/0;
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuitsOrder(jsonObject,pageable.getSize(),pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @GetMapping("/test")
    @PreAuthorize("hasAuthority('测试按钮')")
    public BiyiPageResult<JSONObject> getPageTest(@RequestParam(required = false) Map example , BiyiPageRequest pageable) {
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> data = new ArrayList<JSONObject>();
        Long totalCount = 0L;
        PageResponse<JSONObject> pageResponse =  custLinkInterfaceDao.getCircuitsOrder(jsonObject,pageable.getSize(),pageable.getPage(), NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        if(pageResponse!=null &&pageResponse.getData()!=null && pageResponse.getData().size() !=0){
            JSONObjectUtil.convertBigNumberToString(pageResponse.getData());
            data.addAll(pageResponse.getData());
            totalCount = totalCount+ pageResponse.getPageInfo().getTotalCount();
        }
        return new BiyiPageResult(data,totalCount ,totalCount);
    }

    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> dictionary(@RequestParam(required = false) Map example) {

        String userName = SecurityContext.getJwtUser().getUsername();
        String areaName = userName.split("@@@")[1];
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){
            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                Region region = Region2List.get(i);
                if (areaName.equals("江苏") || region.getName().equals(areaName)) {
                    result.add(region);
                }
            }
        }
        meta.put("region2List", result);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<ServiceType> serviceTypeArray = custLinkInterfaceDao.getServiceTypeDict(NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        meta.put("serviceType", serviceTypeArray);
        return ResponseEntity.ok().body(meta);
    }

    @GetMapping("/test/dictionary")
    public ResponseEntity<JSONObject> dictionaryTest(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary : {}", example);
        JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
        JSONObject meta = new JSONObject();
        // 获取1级地区清单
        Region regionParam = new Region();

        List<Region> Region2List = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
        List<Region> result = new ArrayList<>();
        for(int i =0;i<Region2List.size();i++){

            if(Region2List.get(i).getAreaLevelId().equals(Long.parseLong("100700"))){
                result.add(Region2List.get(i));
            }
        }
        meta.put("region2List", result);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<ServiceType> serviceTypeArray = custLinkInterfaceDao.getServiceTypeDict(NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds")));
        meta.put("serviceType", serviceTypeArray);
        return ResponseEntity.ok().body(meta);
    }

}
