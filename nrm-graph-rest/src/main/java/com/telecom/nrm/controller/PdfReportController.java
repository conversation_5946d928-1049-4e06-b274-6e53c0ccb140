package com.telecom.nrm.controller;


import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.layout.font.FontProvider;
import com.telecom.nrm.service.IdcReportService;
import lombok.extern.slf4j.Slf4j;
import org.commonmark.ext.gfm.tables.TablesExtension;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.Collections;

@RestController
@RequestMapping("/api/pdfreport")
@Slf4j
public class PdfReportController {

    @Autowired
    private IdcReportService reportService;

    @GetMapping("/exportHtmlPdf")
    public void exportHtmlPdf(HttpServletResponse response) throws Exception {
        File file = reportService.demo();
        OutputStream outputStream = null;
        FileInputStream fis = null;
        try {
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(URLEncoder.encode(file.getName()).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            outputStream = response.getOutputStream();
            fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
        } catch (Exception e ){
            e.printStackTrace();
        }finally {
            if (fis != null) {
                fis.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
            if (file != null) {
                file.delete();
            }
        }
    }

    @GetMapping("/exportMarkdownPdf")
    public void exportMarkdownPdf(HttpServletResponse response) throws Exception {
        OutputStream outputStream = null;
        FileInputStream fis = null;
        File file = null;
        try {
            file = this.createFile();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(URLEncoder.encode(file.getName()).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            outputStream = response.getOutputStream();
            fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
        } catch (Exception e ){
            e.printStackTrace();
        }finally {
            if (fis != null) {
                fis.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
            if (file != null) {
                file.delete();
            }
        }

    }

    public  File createFile() throws Exception {
        // 1. 读取Markdown文件
        String mdContent = "# IDC设备操作稽核报告\n" +
                "**报告时间**：2025年04月11日\n" +
                "# 本周稽核发现风险\n" +
                "## 概述\n" +
                "\n" +
                "**稽核时间范围：** 2024-04-07~2024-04-13  \n" +
                "\n" +
                "**端口操作稽核：** 总操作16次，其中存在风险8次。涉及地市：南通、苏州、无锡。风险类型分布如下：  \n" +
                "- **按单操作：** 8次，占比50%  \n" +
                "- **无工单关闭中继端口：** 4次，占比25%  \n" +
                "- **无工单关闭空闲端口：** 2次，占比12.5%  \n" +
                "- **关闭未纳管端口：** 1次，占比6.25%  \n" +
                "- **打开未纳管端口：** 1次，占比6.25%  \n" +
                "\n" +
                "**异常流量稽核：** 20个空闲端口存在流量。  \n" +
                "\n" +
                "**端口配置速率稽核：** 异常端口200个。  \n" +
                "- 端口实际速率高于资源设备配置速率的端口100个  \n" +
                "- 端口实际速率低于资源设备配置速率的端口100个  \n" +
                "\n" +
                "## 稽核情况\n" +
                "\n" +
                "### 2.1 端口操作风险分析\n" +
                "\n" +
                "#### 2.1.1 无工单关闭中继端口  \n" +
                "- **数量：** 4  \n" +
                "- **现象：** 检测到当日对中继端口关闭的命令，该端口在IDC资源系统的状态为“中继”，未匹配中继工单。  \n" +
                "- **稽核：** 无工单施工  \n" +
                "- **示例：**  \n" +
                "  - 时间：2025-04-11 03:01:59  \n" +
                "  - 设备：61.177.206.123  \n" +
                "  - 端口：GigabitEthernet12/0/1  \n" +
                "  - 操作：关闭端口  \n" +
                "- **建议措施：** 存在未按流程操作风险，系统建议补录工单或现场核查使用情况。  \n" +
                "\n" +
                "#### 2.1.2 无工单关闭空闲端口  \n" +
                "- **数量：** 2  \n" +
                "- **现象：** 检测到当日对网络设备的端口关闭的命令，该端口在IDC资源系统的状态为“空闲”，未匹配拆机工单。  \n" +
                "- **稽核：** 无工单施工  \n" +
                "- **示例：**  \n" +
                "  - 时间：2025-04-11 00:41:20  \n" +
                "  - 设备：58.211.4.225  \n" +
                "  - 端口：Ten-GigabitEthernet5/0/1  \n" +
                "  - 操作：关闭端口  \n" +
                "- **建议措施：** 存在未按流程操作风险，系统建议补录工单或现场核查使用情况。  \n" +
                "\n" +
                "#### 2.1.3 关闭未纳管端口  \n" +
                "- **数量：** 1  \n" +
                "- **现象：** 根据端口名称在IDC资源系统未查询到端口实例。  \n" +
                "- **稽核：** 未按单施工  \n" +
                "- **示例：**  \n" +
                "  - 时间：2025-04-11 00:41:20  \n" +
                "  - 设备：58.211.4.225  \n" +
                "  - 端口：TenGigabitEthernet5001  \n" +
                "  - 操作：关闭端口  \n" +
                "- **建议措施：** 端口未纳管，系统建议核实IDC资源详情，缺失请补录端口。  \n" +
                "\n" +
                "#### 2.1.4 打开未纳管端口  \n" +
                "- **数量：** 1  \n" +
                "- **现象：** 根据端口名称在IDC资源系统未查询到端口实例。  \n" +
                "- **稽核：** 未按单施工  \n" +
                "- **示例：**  \n" +
                "  - 时间：2025-04-11 00:07:54  \n" +
                "  - 设备：58.211.80.127  \n" +
                "  - 端口：GigabitEthernet1/0/2  \n" +
                "  - 操作：打开端口  \n" +
                "- **建议措施：** 端口未纳管，系统建议核实IDC资源详情，缺失请补录端口。  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 2.2 异常流量分析  \n" +
                "\n" +
                "| **上周情况**       | **本周**          | 本周新增 | 本周恢复 |\n" +
                "|--------------------|-------------------|----------|----------|\n" +
                "| 异常总数：100      | 异常总数：150     | 40       | 30       |\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 2.3 异常速率分析  \n" +
                "\n" +
                "| **上周情况**       | **本周**          | 本周新增 | 本周恢复 |\n" +
                "|--------------------|-------------------|----------|----------|\n" +
                "| 异常总数：100      | 异常总数：150     | 40       | 30       |\n" +
                "\n" +
                "---\n" +
                "\n" +
                "# 上线以来累计情况\n" +
                "\n" +
                "## 概述  \n" +
                "\n" +
                "**时间范围：** 2025年03月20日至2025年04月14日  \n" +
                "\n" +
                "**端口操作稽核：** 总操作131次，其中存在风险26次。涉及地市：南通、苏州、无锡。风险类型分布如下：  \n" +
                "- **按单操作：** 105次，占比80.15%  \n" +
                "- **无工单开启空闲端口：** 1次，占比0.76%  \n" +
                "- **无工单关闭中继端口：** 13次，占比9.92%  \n" +
                "- **无工单关闭空闲端口：** 2次，占比1.53%  \n" +
                "- **无工单开关占用端口：** 2次，占比1.53%  \n" +
                "- **未按单开关占用端口：** 1次，占比0.76%  \n" +
                "- **关闭未纳管端口：** 2次，占比1.53%  \n" +
                "- **打开未纳管端口：** 5次，占比3.82%  \n" +
                "\n" +
                "**端口异常流量稽核：** 累计发现异常500个，仍存在异常100个。  \n" +
                "\n" +
                "**端口异常速率稽核：** 累计发现异常500个，仍存在异常100个。  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "## 2、整改进度  \n" +
                "\n" +
                "### 2.1 端口操作风险整改  \n" +
                "\n" +
                "| 地市   | 累计风险数 | 派单数量 | 完成整改数量 |\n" +
                "|--------|------------|----------|--------------|\n" +
                "| 南京   | 4          | 4        | 0            |\n" +
                "| 南通   | 11         | 11       | 0            |\n" +
                "| 无锡   | 1          | 1        | 0            |\n" +
                "| 宿迁   | 1          | 1        | 0            |\n" +
                "| 苏州   | 7          | 7        | 0            |\n" +
                "| 常州   | 2          | 2        | 0            |\n" +
                "| **合计** | **26**     | **26**   | **0**        |  \n" +
                "\n" +
                "**注：** 不在范围内的地市为在该地市未发生有风险操作。  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "## 稽核详单  \n" +
                "\n" +
                "详单见“XXX.XLSX”。内容包括：  \n" +
                "1. 历史端口稽核信息清单  \n" +
                "2. 异常流量清单  \n" +
                "3. 端口速率异常清单  \n" +
                "供复合参考。  ";

        String html = convert(mdContent);
        ConverterProperties props = new ConverterProperties();
        FontProvider fontProvider = new FontProvider();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] fontResources = resolver.getResources("classpath*:fonts/*.ttf");
        try {
            for (int i = 0; i < fontResources.length; i++) {

//            FontProgram fontProgram = FontProgramFactory.createFont(fontResources[i].getFile().getAbsolutePath()); // 索引0表示第一个字体
//            fontProvider.addFont(fileToBytes(fontResources[i].getFile()));
//            fontProvider.addFont(fontProgram,"Microsoft YaHei");
                InputStream inputStream = fontResources[i].getInputStream();
                File tempFile = File.createTempFile("temp_" ,  fontResources[i].getFilename() );
                Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                fontProvider.addFont(fileToBytes(tempFile));
                tempFile.delete();
                inputStream.close();
            }
            props.setFontProvider(fontProvider);
        } catch (Exception e) {
            e.printStackTrace();
            // 如果报错尝试不设置字体生成
        }

        try (FileOutputStream fos = new FileOutputStream("a.pdf")) {
            HtmlConverter.convertToPdf(html, fos, props);
        }
        return new File("a.pdf");
//        try (OutputStream os = new FileOutputStream("output.pdf")) {
//            PdfRendererBuilder builder = new PdfRendererBuilder();
//            builder.withHtmlContent(html, null);
//
//            // 确保字体支持粗体（如使用内置字体或自定义字体）
//            builder.useFont(new File("path/to/simfang.ttf"), "simfang");
//
//            builder.toStream(os);
//            builder.run();
//        }

//        try (OutputStream os = new FileOutputStream("b.pdf")) {
//            PdfRendererBuilder builder = new PdfRendererBuilder();
//            builder.withHtmlContent(html, null);
//
//            // 确保字体支持粗体（如使用内置字体或自定义字体）
//            builder.useFont(new File("C:\\Windows\\Fonts\\simhei.ttf"), "simhei");
//
//            builder.toStream(os);
//            builder.run();
//        }
    }

    public  String convert(String markdown) {
//        Parser parser = Parser.builder().build();
//        Node document = parser.parse(markdown);
//        HtmlRenderer renderer = HtmlRenderer.builder().escapeHtml(false).build();
//        return renderer.render(document);

        Parser parser = Parser.builder()
                .extensions(Collections.singleton(TablesExtension.create())) // 启用表格扩展
                .build();
        HtmlRenderer renderer = HtmlRenderer.builder().extensions(Collections.singleton(TablesExtension.create())).build();
        String html = renderer.render(parser.parse(markdown)).replace("\u200C", "");
//                .replace("strong","span");
        String htmlHeader = "<style>" +
                "body { line-height:1.5; font-family: 'Microsoft YaHei', sans-serif;} "+
                "h1,h2,h3,h4,h5,h6,p,ul,li,div{ margin:0 0 !important;line-height: 40px !important; }  "+
                "table { border-collapse: collapse; width: 100%; margin: 10px 0; }" +
                "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }" +
                "th { background-color: #f2f2f2; }" +
                "span {font-weight: bold;}" +
                "ul { list-style-type: \"• \" !important; }" +
                "</style>";

        return "<html><body>" +html +"</body><head>" +htmlHeader  +"</head></html> "; // 合并样式与生成的 HTML

    }
    public  byte[] fileToBytes(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }
}
