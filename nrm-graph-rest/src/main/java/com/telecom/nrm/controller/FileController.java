package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.telecom.nrm.service.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件控制器
 * 处理文件上传、下载等操作
 */
@RestController
@RequestMapping("/api/file")
@Slf4j
public class FileController {

    @Autowired
    private S3Service s3Service;

    // S3配置
    private static final String BUCKET_NAME = "yuanfeng";

    /**
     * 根据S3Key下载文件
     * @param s3Key S3存储路径
     * @return 文件流
     */
    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFileByS3Key(@RequestParam String s3Key) {
        try {
            log.info("开始下载文件，S3Key: {}", s3Key);

            // URL解码S3Key
            s3Key = URLDecoder.decode(s3Key, StandardCharsets.UTF_8.toString());
            log.info("解码后的S3Key: {}", s3Key);

            // 获取文件元数据，检查文件是否存在
            ObjectMetadata metadata;
            try {
                metadata = s3Service.getObjectMetadata(BUCKET_NAME, s3Key);
            } catch (Exception e) {
                log.error("文件不存在或无法访问，S3Key: {}, 错误: {}", s3Key, e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }

            long contentLength = metadata.getContentLength();
            String contentType = metadata.getContentType();
            log.info("文件信息 - S3Key: {}, 大小: {} bytes, 类型: {}", s3Key, contentLength, contentType);

            // 获取文件流
            InputStream inputStream = s3Service.readStream(BUCKET_NAME, s3Key);
            InputStreamResource resource = new InputStreamResource(inputStream);

            // 从S3Key中提取文件名
            String fileName = extractFileNameFromS3Key(s3Key);
            log.info("提取的文件名: {}", fileName);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength));

            // 设置内容类型
            MediaType mediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (contentType != null && !contentType.isEmpty()) {
                try {
                    mediaType = MediaType.parseMediaType(contentType);
                } catch (Exception e) {
                    log.warn("无法解析内容类型: {}, 使用默认类型", contentType);
                }
            }

            log.info("文件下载成功，S3Key: {}", s3Key);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(mediaType)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载文件失败，S3Key: {}, 错误: {}", s3Key, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 从S3Key中提取文件名
     * @param s3Key S3存储路径
     * @return 文件名
     */
    private String extractFileNameFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.isEmpty()) {
            return "download";
        }

        // 获取路径中的最后一部分作为文件名
        String fileName = s3Key;
        if (s3Key.contains("/")) {
            fileName = s3Key.substring(s3Key.lastIndexOf("/") + 1);
        }

        // 如果文件名为空或只是UUID，返回默认名称
        if (fileName.isEmpty() || isUUID(fileName)) {
            return "attachment";
        }

        return fileName;
    }

    /**
     * 检查字符串是否为UUID格式
     * @param str 字符串
     * @return 是否为UUID
     */
    private boolean isUUID(String str) {
        if (str == null || str.length() != 36) {
            return false;
        }
        return str.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");
    }

    /**
     * 获取文件信息
     * @param s3Key S3存储路径
     * @return 文件信息
     */
    @GetMapping("/info")
    public ResponseEntity<JSONObject> getFileInfo(@RequestParam String s3Key) {
        JSONObject result = new JSONObject();
        try {
            log.info("获取文件信息，S3Key: {}", s3Key);

            // URL解码S3Key
            s3Key = URLDecoder.decode(s3Key, StandardCharsets.UTF_8.toString());

            // 获取文件元数据
            ObjectMetadata metadata = s3Service.getObjectMetadata(BUCKET_NAME, s3Key);

            result.put("success", true);
            result.put("message", "获取文件信息成功");
            
            JSONObject fileInfo = new JSONObject();
            fileInfo.put("s3Key", s3Key);
            fileInfo.put("fileName", extractFileNameFromS3Key(s3Key));
            fileInfo.put("fileSize", metadata.getContentLength());
            fileInfo.put("contentType", metadata.getContentType());
            fileInfo.put("lastModified", metadata.getLastModified());
            
            result.put("data", fileInfo);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取文件信息失败，S3Key: {}, 错误: {}", s3Key, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取文件信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
}
