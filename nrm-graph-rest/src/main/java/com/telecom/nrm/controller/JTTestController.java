package com.telecom.nrm.controller;

import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.utils.HttpRequestsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/jt/graph/serviceAgent")
public class JTTestController {

    /**
     * 6跨域电路资源树API《附件6-业务资源树-两级业务连接图v1.5.docx》
     * 跨域电路资源树API
     * 测试:http://10.128.91.38:20501/serviceAgent/rest/treegraphworker/api/treeGraph/simpleRoute
     * X-APP-ID：79d9e66861cf074c0d2b14f4ddf2ffb9
     * X-APP-KEY：45caafc3bbc1cb6631c66f4d917792d9
     */
    @PostMapping("/simpleRoute")
    public JSONObject simpleRoute(@RequestBody JSONObject param) {

        Map<String, String> headers = new HashMap<>();
        headers.put("X-APP-ID", "79d9e66861cf074c0d2b14f4ddf2ffb9");
        headers.put("X-APP-KEY", "45caafc3bbc1cb6631c66f4d917792d9");
        try {
            String res = HttpRequestsUtils.sendPost("http://10.128.91.38:20501/serviceAgent/rest/treegraphworker/api/treeGraph/simpleRoute",
                    headers,param);
            return JSONObject.parseObject(res);
        } catch (IOException e) {
            e.printStackTrace();
            JSONObject err = new JSONObject();
            err.put("err",e.getMessage());
            return err;
        }

    }

    /**
     * 7稽核接口设计《附件6-业务资源树-两级业务连接图v1.5.docx》
     资源树稽核_API
     http://10.128.91.38:20501/serviceAgent/rest/treegraphworker/circuit/circuitValidate
     * X-APP-ID：79d9e66861cf074c0d2b14f4ddf2ffb9
     * X-APP-KEY：45caafc3bbc1cb6631c66f4d917792d9
     */
    @PostMapping("/circuitValidate")
    public JSONObject circuitValidate(@RequestBody JSONObject param) {

        Map<String, String> headers = new HashMap<>();
        headers.put("X-APP-ID", "79d9e66861cf074c0d2b14f4ddf2ffb9");
        headers.put("X-APP-KEY", "45caafc3bbc1cb6631c66f4d917792d9");
        try {
            String res = HttpRequestsUtils.sendPost("http://10.128.91.38:20501/serviceAgent/rest/treegraphworker/circuit/circuitValidate",
                    headers,param);
            return JSONObject.parseObject(res);
        } catch (IOException e) {
            e.printStackTrace();
            JSONObject err = new JSONObject();
            err.put("err",e.getMessage());
            return err;
        }


    }
}
