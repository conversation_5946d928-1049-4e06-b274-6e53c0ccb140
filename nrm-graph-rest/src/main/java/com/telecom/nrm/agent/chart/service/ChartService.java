package com.telecom.nrm.agent.chart.service;

import com.telecom.nrm.agent.chart.model.ChartData;

import java.awt.image.BufferedImage;

/**
 * 图表服务接口
 */
public interface ChartService {

    /**
     * 根据图表数据生成图表图像
     *
     * @param chartData 图表数据
     * @return 生成的图表图像
     */
    BufferedImage generateChart(ChartData chartData);

    /**
     * 根据图表数据生成SVG矢量图
     *
     * @param chartData 图表数据
     * @return 生成的SVG字符串
     */
    String generateChartSVG(ChartData chartData);

    /**
     * 解析Base64编码的图表数据
     *
     * @param base64Data Base64编码的图表数据
     * @return 解析后的图表数据对象
     */
    ChartData parseBase64ChartData(String base64Data);
}
