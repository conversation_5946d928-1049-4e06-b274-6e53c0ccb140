package com.telecom.nrm.agent;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.telecom.nrm.agent.mcp.service.IDCAuditAgentService;
import com.telecom.nrm.utils.BeanUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

public class PortChatController extends TextWebSocketHandler {

    private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final Set<WebSocketSession> sessions = new CopyOnWriteArraySet<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        if (payload != null && payload.indexOf("<end>") >=0 ) {
            session.close();
            System.out.println("payload==>客户端请求终止");
            return;
        }
        ask(session,payload);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        sessions.remove(session);
    }

    private static final ObjectMapper mapper = new ObjectMapper();



    private void ask(WebSocketSession session,String content) {
        BeanUtils.getBean(IDCAuditAgentService.class).execute(session, content);
        try {
            JSONObject c = new JSONObject();
            c.put("content","<end>");
            session.sendMessage(new TextMessage(c.toJSONString()));
            session.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
