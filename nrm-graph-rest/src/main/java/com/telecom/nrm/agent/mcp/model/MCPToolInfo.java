package com.telecom.nrm.agent.mcp.model;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.Map;

/**
 * Class representing information about an MCP Tool.
 */
public class MCPToolInfo {
    
    private String name;
    private String description;
    private boolean enabled;
    private Class<?> targetClass;
    private Method targetMethod;
    private Map<String, Class<?>> parameters;
    private Class<?> returnType;
    
    public MCPToolInfo(String name, String description, boolean enabled, 
                      Class<?> targetClass, Method targetMethod) {
        this.name = name;
        this.description = description;
        this.enabled = enabled;
        this.targetClass = targetClass;
        this.targetMethod = targetMethod;
        this.parameters = new HashMap<>();
        
        // Extract parameter information
        Parameter[] methodParams = targetMethod.getParameters();
        for (Parameter param : methodParams) {
            this.parameters.put(param.getName(), param.getType());
        }
        
        this.returnType = targetMethod.getReturnType();
    }
    
    /**
     * Convert this MCPToolInfo to a JSON-compatible Map.
     * @return a Map representation of this MCPToolInfo
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("description", description);
        map.put("enabled", enabled);
        map.put("className", targetClass.getName());
        map.put("methodName", targetMethod.getName());
        
        Map<String, String> paramMap = new HashMap<>();
        for (Map.Entry<String, Class<?>> entry : parameters.entrySet()) {
            paramMap.put(entry.getKey(), entry.getValue().getSimpleName());
        }
        
        map.put("parameters", paramMap);
        map.put("returnType", returnType.getSimpleName());
        
        return map;
    }
    
    // Getters and setters
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public Class<?> getTargetClass() {
        return targetClass;
    }
    
    public void setTargetClass(Class<?> targetClass) {
        this.targetClass = targetClass;
    }
    
    public Method getTargetMethod() {
        return targetMethod;
    }
    
    public void setTargetMethod(Method targetMethod) {
        this.targetMethod = targetMethod;
    }
    
    public Map<String, Class<?>> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Class<?>> parameters) {
        this.parameters = parameters;
    }
    
    public Class<?> getReturnType() {
        return returnType;
    }
    
    public void setReturnType(Class<?> returnType) {
        this.returnType = returnType;
    }
}
