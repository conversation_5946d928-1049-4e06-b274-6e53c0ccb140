package com.telecom.nrm.agent.chart.controller;

import com.telecom.nrm.agent.chart.model.ChartData;
import com.telecom.nrm.agent.chart.service.ChartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 图表控制器
 * 处理图表生成请求
 */
@RestController
@RequestMapping("/api/chart")
public class ChartController {

    private static final Logger logger = LoggerFactory.getLogger(ChartController.class);

    @Autowired
    private ChartService chartService;

    /**
     * 生成图表图像或矢量图
     *
     * @param data Base64编码的图表数据
     * @param format 图像格式（png, jpg, gif, svg）默认为SVG矢量图
     * @return 图表图像或SVG矢量图
     */
    @GetMapping("/generate")
    public ResponseEntity<?> generateChart(
            @RequestParam("data") String data,
            @RequestParam(value = "format", defaultValue = "svg") String format) {

        try {
            // 解析Base64编码的图表数据
            ChartData chartData = chartService.parseBase64ChartData(data);

            // 根据格式生成不同类型的图表
            if ("svg".equalsIgnoreCase(format)) {
                // 生成SVG矢量图
                String svgContent = chartService.generateChartSVG(chartData);

                return ResponseEntity.ok()
                        .contentType(MediaType.valueOf("image/svg+xml; charset=UTF-8"))
                        .header("Content-Disposition", "inline")
                        .body(svgContent);
            } else {
                // 生成位图图像
                BufferedImage chartImage = chartService.generateChart(chartData);

                // 将图像转换为字节数组
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(chartImage, format, baos);
                byte[] imageBytes = baos.toByteArray();

                // 设置响应头
                MediaType mediaType;
                switch (format.toLowerCase()) {
                    case "jpg":
                    case "jpeg":
                        mediaType = MediaType.IMAGE_JPEG;
                        break;
                    case "gif":
                        mediaType = MediaType.IMAGE_GIF;
                        break;
                    case "png":
                    default:
                        mediaType = MediaType.IMAGE_PNG;
                        break;
                }

                return ResponseEntity.ok()
                        .contentType(mediaType)
                        .body(imageBytes);
            }

        } catch (IllegalArgumentException e) {
            logger.error("无效的图表数据: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            logger.error("生成图表图像失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


}
