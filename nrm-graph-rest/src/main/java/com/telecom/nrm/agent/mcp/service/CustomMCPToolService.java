package com.telecom.nrm.agent.mcp.service;

import com.telecom.nrm.agent.mcp.tools.SampleMCPTools;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Custom implementation of AbstractMCPToolService.
 * This service provides specific MCP Tool classes.
 */
@Service
@Qualifier("customMCPToolService")
public class CustomMCPToolService extends AbstractMCPToolService {

    /**
     * Get the MCP Tool classes.
     * This implementation returns a list of classes with @MCPTools annotation.
     * @return a list of classes annotated with @MCPTools
     */
    @Override
    protected List<Class<?>> getMCPToolClass() {
        return Arrays.asList(
            SampleMCPTools.class
            // Add more classes with @MCPTools annotation here
        );
    }
}
