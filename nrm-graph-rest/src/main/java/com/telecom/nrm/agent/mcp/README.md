# MCP Tool Service

The MCP Tool Service is a framework for managing and invoking MCP (Model-Controller-Provider) tools. It provides a way to register and discover tools, and to invoke them via reflection.

## Overview

The MCP Tool Service scans for classes annotated with `@MCPTools` and methods annotated with `@MCPTool`. It registers these tools and provides a way to invoke them via reflection.

## Components

### Annotations

- `@MCPTools`: Annotation to mark a class as an MCP Tools provider.
- `@MCPTool`: Annotation to mark a method as an MCP Tool.

### Models

- `MCPToolInfo`: Class representing information about an MCP Tool.
- `MCPToolsProvider`: Class representing an MCP Tools provider.

### Service

- `IMCPToolService`: Interface for MCP Tool Service.
- `AbstractMCPToolService`: Abstract service implementing IMCPToolService.
- `SampleMCPToolService`: Concrete implementation of AbstractMCPToolService with qualifier "sampleMCPToolService".
- `CustomMCPToolService`: Another implementation of AbstractMCPToolService with qualifier "customMCPToolService".
- `PortToolsService`: Implementation of AbstractMCPToolService for port operations with qualifier "portToolsService".
- `IDCAuditToolsService`: Implementation of AbstractMCPToolService for data center auditing with qualifier "idcAuditToolsService".

### Controllers

- `MCPToolController`: Controller for MCP Tool operations.
- `MCPToolDemoController`: Demo controller for MCP Tool operations.
- `PortToolsController`: Controller for Port Tools operations.
- `IDCAuditToolsController`: Controller for IDC Audit Tools operations.

## Usage

### Creating an MCP Tools Provider

To create an MCP Tools provider, create a class and annotate it with `@MCPTools`:

#### Example: SampleMCPTools

```java
@Component
@MCPTools(name = "MyTools", description = "My MCP Tools")
public class MyMCPTools {

    // MCP Tools methods go here
}
```

#### Example: PortTools

```java
@Component
@MCPTools(name = "PortTools", description = "Tools for network port operations")
public class PortTools {

    @MCPTool(description = "Check if a port is open on a host")
    public boolean isPortOpen(String host, int port) {
        // Implementation
    }

    @MCPTool(description = "Scan a range of ports on a host")
    public List<Integer> scanPorts(String host, int startPort, int endPort) {
        // Implementation
    }

    // More tools...
}
```

#### Example: IDCAuditTools

```java
@Component
@MCPTools(name = "IDCAuditTools", description = "Tools for data center auditing operations")
public class IDCAuditTools {

    @MCPTool(description = "Audit server configuration")
    public JSONObject auditServerConfig(String serverId) {
        // Implementation
    }

    @MCPTool(description = "Audit network configuration")
    public JSONObject auditNetworkConfig(String networkId) {
        // Implementation
    }

    // More tools...
}
```

### Creating an MCP Tool

To create an MCP Tool, create a method in an MCP Tools provider class and annotate it with `@MCPTool`:

```java
@MCPTool(name = "myTool", description = "My MCP Tool")
public String myTool(String input) {
    return "Hello, " + input + "!";
}
```

### Implementing IMCPToolService

You can implement the `IMCPToolService` interface directly, or extend the abstract `AbstractMCPToolService` class. You need to override the `getMCPToolClass()` method to return a list of classes with `@MCPTools` annotation.

Use the `@Qualifier` annotation to give your implementation a name:

```java
@Service
@Qualifier("myMCPToolService")
public class MyMCPToolService extends AbstractMCPToolService {

    @Override
    protected List<Class<?>> getMCPToolClass() {
        return Arrays.asList(
            MyTools.class,
            AnotherTools.class
        );
    }
}
```

#### Example: PortToolsService

```java
@Service
@Qualifier("portToolsService")
public class PortToolsService extends AbstractMCPToolService {

    @Override
    protected List<Class<?>> getMCPToolClass() {
        return Arrays.asList(
            PortTools.class
        );
    }
}
```

#### Example: IDCAuditToolsService

```java
@Service
@Qualifier("idcAuditToolsService")
public class IDCAuditToolsService extends AbstractMCPToolService {

    @Override
    protected List<Class<?>> getMCPToolClass() {
        return Arrays.asList(
            IDCAuditTools.class
        );
    }
}
```

### Invoking an MCP Tool

To invoke an MCP Tool, use the `IMCPToolService` and specify which implementation to use with the `@Qualifier` annotation:

```java
@Autowired
@Qualifier("sampleMCPToolService") // or "customMCPToolService"
private IMCPToolService mcpToolService;

public void invokeMyTool() {
    Map<String, Object> params = new HashMap<>();
    params.put("input", "World");

    try {
        Object result = mcpToolService.invokeTool("myTool", params);
        System.out.println(result); // Output: Hello, World!
    } catch (MCPToolException e) {
        e.printStackTrace();
    }
}
```

### Getting a List of MCP Tools

To get a list of all MCP Tools, use the `IMCPToolService` with the appropriate qualifier:

```java
@Autowired
@Qualifier("sampleMCPToolService") // or "customMCPToolService"
private IMCPToolService mcpToolService;

public void listTools() {
    JSONArray tools = mcpToolService.getToolsAsJsonArray();
    System.out.println(tools.toJSONString());
}
```

## API Endpoints

The MCP Tool Service provides the following API endpoints:

- `GET /api/mcp/tools`: Get a list of all MCP Tools.
- `GET /api/mcp/providers`: Get a list of all MCP Tools providers.
- `POST /api/mcp/invoke/{toolName}`: Invoke an MCP Tool.

## Demo Endpoints

The MCP Tool Demo Controller provides the following demo endpoints:

- `GET /api/mcp-demo/list-tools`: Get a list of all MCP Tools.
- `GET /api/mcp-demo/echo?input={input}`: Test the echo tool.
- `GET /api/mcp-demo/add?a={a}&b={b}`: Test the add tool.
- `GET /api/mcp-demo/get-items?count={count}`: Test the getItems tool.

## Error Handling

The MCP Tool Service throws `MCPToolException` when an error occurs during MCP Tool operations. The exception contains a message and the cause of the error.

When an error occurs, the API endpoints return a JSON response with the following structure:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Full stack trace"
}
```

## Example

See the `SampleMCPTools` class for an example of how to create an MCP Tools provider and MCP Tools.
