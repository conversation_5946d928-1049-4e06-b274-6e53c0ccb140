package com.telecom.nrm.agent.mcp.annotation;

import java.lang.annotation.*;

/**
 * Annotation to mark a method as an MCP Tool.
 * Methods annotated with @MCPTool will be registered by the MCPToolService.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MCPTool {
    
    /**
     * Name of the MCP Tool.
     * If not specified, the method name will be used.
     * @return the name of the MCP Tool
     */
    String name() default "";
    
    /**
     * Description of the MCP Tool.
     * @return the description of the MCP Tool
     */
    String description() default "";
    
    /**
     * Whether this tool is enabled by default.
     * @return true if the tool is enabled by default, false otherwise
     */
    boolean enabled() default true;
}
