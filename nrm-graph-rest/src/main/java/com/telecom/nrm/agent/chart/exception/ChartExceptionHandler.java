package com.telecom.nrm.agent.chart.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * 图表异常处理类
 * 处理图表生成过程中的异常
 */
@ControllerAdvice
public class ChartExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(ChartExceptionHandler.class);

    /**
     * 处理参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, String>> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.error("参数异常: {}", e.getMessage());
        
        Map<String, String> response = new HashMap<>();
        response.put("error", "参数错误");
        response.put("message", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleException(Exception e) {
        logger.error("服务器异常: {}", e.getMessage(), e);
        
        Map<String, String> response = new HashMap<>();
        response.put("error", "服务器错误");
        response.put("message", "处理请求时发生错误");
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
