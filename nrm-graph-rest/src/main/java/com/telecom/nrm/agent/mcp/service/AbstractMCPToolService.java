package com.telecom.nrm.agent.mcp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.mcp.annotation.MCPTool;
import com.telecom.nrm.agent.mcp.annotation.MCPTools;
import com.telecom.nrm.agent.mcp.exception.MCPToolException;
import com.telecom.nrm.agent.mcp.model.MCPToolInfo;
import com.telecom.nrm.agent.mcp.model.MCPToolsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Abstract service for managing MCP Tools.
 * This service scans for classes annotated with @MCPTools and methods annotated with @MCPTool,
 * and provides functionality to invoke these tools via reflection.
 */
public abstract class AbstractMCPToolService implements IMCPToolService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractMCPToolService.class);

    @Autowired
    private ApplicationContext applicationContext;

    // Map of provider name to provider
    private final Map<String, MCPToolsProvider> providers = new ConcurrentHashMap<>();

    // Map of tool name to tool info
    private final Map<String, MCPToolInfo> tools = new ConcurrentHashMap<>();

    /**
     * Initialize the service by scanning for MCP Tools.
     */
    @PostConstruct
    public void init() {
        logger.info("Initializing AbstractMCPToolService...");
        scanMCPTools();
        logger.info("AbstractMCPToolService initialized with {} providers and {} tools",
                   providers.size(), tools.size());
    }

    /**
     * Process classes with @MCPTools annotation using getMCPToolClass().
     */
    private void scanMCPTools() {
        try {
            // Get the MCP Tool classes from the implementing class
            List<Class<?>> mcpToolClasses = getMCPToolClass();

            if (mcpToolClasses == null || mcpToolClasses.isEmpty()) {
                logger.warn("No MCP Tool classes found. Override getMCPToolClass() to provide classes with @MCPTools annotation.");
                return;
            }

            // Process the classes provided by getMCPToolClass()
            for (Class<?> clazz : mcpToolClasses) {
                logger.debug("Processing MCP Tool class: {}", clazz.getName());
                processToolClass(clazz);
            }
        } catch (Exception e) {
            logger.error("Failed to process MCP Tool classes", e);
        }
    }

    /**
     * Process a class annotated with @MCPTools.
     * @param clazz the class to process
     */
    private void processToolClass(Class<?> clazz) {
        MCPTools mcpToolsAnnotation = clazz.getAnnotation(MCPTools.class);
        if (mcpToolsAnnotation == null) {
            logger.warn("Class {} is not annotated with @MCPTools", clazz.getName());
            return;
        }

        // Get the provider name, use class name if not specified
        String providerName = mcpToolsAnnotation.name();
        if (StringUtils.isEmpty(providerName)) {
            providerName = clazz.getSimpleName();
        }

        // Create a provider
        MCPToolsProvider provider = new MCPToolsProvider(
            providerName,
            mcpToolsAnnotation.description(),
            clazz
        );

        // Scan methods for @MCPTool annotation
        for (Method method : clazz.getDeclaredMethods()) {
            MCPTool mcpToolAnnotation = method.getAnnotation(MCPTool.class);
            if (mcpToolAnnotation != null) {
                // Get the tool name, use method name if not specified
                String toolName = mcpToolAnnotation.name();
                if (StringUtils.isEmpty(toolName)) {
                    toolName = method.getName();
                }

                // Create a tool info
                MCPToolInfo toolInfo = new MCPToolInfo(
                    toolName,
                    mcpToolAnnotation.description(),
                    mcpToolAnnotation.enabled(),
                    clazz,
                    method
                );

                // Add the tool to the provider
                provider.addTool(toolInfo);

                // Add the tool to the tools map
                tools.put(toolName, toolInfo);

                logger.debug("Registered MCP Tool: {}", toolName);
            }
        }

        // Add the provider to the providers map
        providers.put(providerName, provider);
    }

    /**
     * Get all MCP Tools providers.
     * @return a list of all MCP Tools providers
     */
    public List<MCPToolsProvider> getAllProviders() {
        return new ArrayList<>(providers.values());
    }

    /**
     * Get all MCP Tools.
     * @return a list of all MCP Tools
     */
    public List<MCPToolInfo> getAllTools() {
        return new ArrayList<>(tools.values());
    }

    /**
     * Get an MCP Tool by name.
     * @param toolName the name of the tool
     * @return the tool, or null if not found
     */
    public MCPToolInfo getTool(String toolName) {
        return tools.get(toolName);
    }

    /**
     * Invoke an MCP Tool by name with the given parameters.
     * @param toolName the name of the tool
     * @param params the parameters to pass to the tool
     * @return the result of the tool invocation
     * @throws MCPToolException if the tool cannot be invoked
     */
    public Object invokeTool(String toolName, Map<String, Object> params) throws MCPToolException {
        MCPToolInfo toolInfo = tools.get(toolName);
        if (toolInfo == null) {
            throw new MCPToolException("Tool not found: " + toolName);
        }

        if (!toolInfo.isEnabled()) {
            throw new MCPToolException("Tool is disabled: " + toolName);
        }

        try {
            // Get the target class and method
            Class<?> targetClass = toolInfo.getTargetClass();
            Method targetMethod = toolInfo.getTargetMethod();

            // Get an instance of the target class from the application context
            Object targetInstance = applicationContext.getBean(targetClass);

            // Prepare the method parameters
            Parameter[] methodParams = targetMethod.getParameters();
            Object[] args = new Object[methodParams.length];

            for (int i = 0; i < methodParams.length; i++) {
                Parameter param = methodParams[i];
                String paramName = param.getName();
                Class<?> paramType = param.getType();

                // Get the parameter value from the params map
                Object paramValue = params.get(paramName);

                // Convert the parameter value to the expected type
                if (paramValue != null) {
                    if (paramType.isAssignableFrom(paramValue.getClass())) {
                        args[i] = paramValue;
                    } else {
                        // Try to convert the value
                        if (paramType == String.class) {
                            args[i] = String.valueOf(paramValue);
                        } else if (paramType == Integer.class || paramType == int.class) {
                            args[i] = Integer.valueOf(String.valueOf(paramValue));
                        } else if (paramType == Long.class || paramType == long.class) {
                            args[i] = Long.valueOf(String.valueOf(paramValue));
                        } else if (paramType == Double.class || paramType == double.class) {
                            args[i] = Double.valueOf(String.valueOf(paramValue));
                        } else if (paramType == Boolean.class || paramType == boolean.class) {
                            args[i] = Boolean.valueOf(String.valueOf(paramValue));
                        } else if (paramType == JSONObject.class) {
                            if (paramValue instanceof Map) {
                                args[i] = new JSONObject((Map<String, Object>) paramValue);
                            } else {
                                args[i] = JSONObject.parseObject(String.valueOf(paramValue));
                            }
                        } else if (paramType == JSONArray.class) {
                            if (paramValue instanceof List) {
                                args[i] = new JSONArray((List<Object>) paramValue);
                            } else {
                                args[i] = JSONArray.parseArray(String.valueOf(paramValue));
                            }
                        } else {
                            throw new MCPToolException("Cannot convert parameter " + paramName +
                                                     " to type " + paramType.getName());
                        }
                    }
                } else {
                    // Parameter is null
                    args[i] = null;
                }
            }

            // Invoke the method
            return targetMethod.invoke(targetInstance, args);

        } catch (InvocationTargetException e) {
            // Unwrap the target exception
            Throwable targetException = e.getTargetException();
            logger.error("Error invoking tool: {}", toolName, targetException);
            throw new MCPToolException("Error invoking tool: " + toolName +
                                     " - " + targetException.getMessage(), targetException);
        } catch (Exception e) {
            logger.error("Error invoking tool: {}", toolName, e);
            throw new MCPToolException("Error invoking tool: " + toolName +
                                     " - " + e.getMessage(), e);
        }
    }

    /**
     * Get a JSON array describing all MCP Tools.
     * @return a JSON array describing all MCP Tools
     */
    public JSONArray getToolsAsJsonArray() {
        JSONArray jsonArray = new JSONArray();

        for (MCPToolInfo toolInfo : tools.values()) {
            if (toolInfo.isEnabled()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", toolInfo.getName());
                jsonObject.put("description", toolInfo.getDescription());

                // Add parameter information
                JSONObject paramsObject = new JSONObject();
                for (Map.Entry<String, Class<?>> entry : toolInfo.getParameters().entrySet()) {
                    paramsObject.put(entry.getKey(), entry.getValue().getSimpleName());
                }
                jsonObject.put("parameters", paramsObject);

                // Add return type information
                jsonObject.put("returnType", toolInfo.getReturnType().getSimpleName());

                jsonArray.add(jsonObject);
            }
        }

        return jsonArray;
    }

    /**
     * Get a JSON array describing all MCP Tools providers.
     * @return a JSON array describing all MCP Tools providers
     */
    public JSONArray getProvidersAsJsonArray() {
        JSONArray jsonArray = new JSONArray();

        for (MCPToolsProvider provider : providers.values()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", provider.getName());
            jsonObject.put("description", provider.getDescription());

            // Add tools information
            JSONArray toolsArray = new JSONArray();
            for (MCPToolInfo toolInfo : provider.getTools()) {
                if (toolInfo.isEnabled()) {
                    JSONObject toolObject = new JSONObject();
                    toolObject.put("name", toolInfo.getName());
                    toolObject.put("description", toolInfo.getDescription());

                    // Add parameter information
                    JSONObject paramsObject = new JSONObject();
                    for (Map.Entry<String, Class<?>> entry : toolInfo.getParameters().entrySet()) {
                        paramsObject.put(entry.getKey(), entry.getValue().getSimpleName());
                    }
                    toolObject.put("parameters", paramsObject);

                    // Add return type information
                    toolObject.put("returnType", toolInfo.getReturnType().getSimpleName());

                    toolsArray.add(toolObject);
                }
            }
            jsonObject.put("tools", toolsArray);

            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

    /**
     * Get the MCP Tool classes.
     * This method must be implemented by subclasses to provide classes with @MCPTools annotation.
     * @return a list of classes annotated with @MCPTools
     */
    protected abstract List<Class<?>> getMCPToolClass();

}
