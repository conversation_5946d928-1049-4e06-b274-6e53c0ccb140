package com.telecom.nrm.agent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;

@Slf4j
public class Utils {

    public static final String r1_url = "http://openapi.telecomjs.com:80/eop/AIGLKFPT/DeepSeekR1_w8a8_AIPlatform/completions";
    public static final String r1_app_id = "a18f86ca710840688ff0fe023ea431f2";
    public static final String r1_app_key = "a6c5eebd88fc4330a1ea18e5276faa8a";

    private static final ObjectMapper mapper = new ObjectMapper();

    public static JSONObject RequestR1React(JSONArray messages,WebSocketSession session,boolean stream) {
        StringBuffer res = new StringBuffer();
        JSONObject react = new JSONObject();
        JSONObject param = new JSONObject();
        param.put("messages",messages);
        param.put("model","DeepSeek-R1-Q-w8a8");
        param.put("temperature", 0);
//        param.put("tool_choice", "auto");
        param.put("stream",stream);
        JSONObject parameters = new JSONObject();
        parameters.put("max_input_length", 1024);
        param.put("parameters", parameters);
        try (CloseableHttpClient client = HttpClients.createDefault()) {

            HttpPost request = new HttpPost(r1_url);
            request.addHeader("X-APP-ID", r1_app_id);
            request.addHeader("X-APP-KEY", r1_app_key);
            request.setEntity(new StringEntity(param.toJSONString(), "UTF-8"));

            // 执行请求
            HttpResponse response = client.execute(request);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream inputStream = null;
                BufferedReader reader = null;
                try   {
                    inputStream = entity.getContent();
                    reader = new BufferedReader(new InputStreamReader(inputStream));
                    String line;
                    // 逐行读取流式响应
                    while ((line = reader.readLine()) != null) {
                        if (StringUtils.isEmpty(line)) {
                            continue;
                        }
                        // 如果拿到了action结束，否需的就都不需要了,AI会给错误的答案
                        if (res.indexOf("</action>")>=0) {
                            break;
                        }
                        if (res.indexOf("<answer>")>=0 && res.indexOf("</think>") <0) {
                            // 添加下</think> 解决AI返回出现think有开始没有结束的问题
                            JSONObject content = new JSONObject();
                            content.put("content","</think>");
                            res = new StringBuffer( res.toString().replace("<answer>", "</think><answer>"));
                            try {
                                session.sendMessage(new TextMessage(content.toJSONString()));
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }

                        if (!stream) {
                            JSONObject result = JSONObject.parseObject(line);
                            if (result.containsKey("choices") ) {
                                JSONArray choices = result.getJSONArray("choices");
                                if (choices.size()>0) {
                                    res.append(choices.getJSONObject(0).getJSONObject("message").getString("content"));
                                }
                            }
                        } else {
                            if (!processDataChunk(line.substring(5).trim(),res,session)) {
                                break; // 可能内容终止或者是页面发起取消
                            }
                        }
                    }


                } finally {
                    InputStream finalInputStream = inputStream;
                    BufferedReader finalReader = reader;
                    new Thread(() -> {
                        try {
                            finalInputStream.close();
                            finalReader.close();
                        } catch (IOException e) {
                            log.warn(e.getMessage(),e);
                        }
                    }).start();
                }
            }
        } catch (IOException e) {
            log.warn(e.getMessage(),e);
        }
        try {
            if (res.indexOf("<action>") >= 0) {
                react.put("action", res.substring(res.indexOf("<action>")+8,res.indexOf("</action>")));
            }

            react.put("message", res.toString());
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
        }

        return react;
    }

    private static boolean processDataChunk(String jsonData, StringBuffer sb,WebSocketSession session) {
        try {
            if("[DONE]".equalsIgnoreCase(jsonData)) {
                return false; //结束 V3接口会返回这个标识
            }
            JsonNode node = mapper.readTree(jsonData);
            JsonNode choices = node.get("choices");
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).get("delta");
                if (delta.has("content")) {
                    System.out.print(delta.get("content").asText());
                    sb.append(delta.get("content").asText());
                    if (session != null) {
                        JSONObject content = new JSONObject();
                        content.put("content",delta.get("content").asText());
                        session.sendMessage(new TextMessage(content.toJSONString()));
                    }

                }
            }
            return true;
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
            return false;

        }
    }


}
