package com.telecom.nrm.agent.weather;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.agent.Utils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WeatherReactService {

    @Autowired
    private WeatherToolsService toolsService;

    public String  execute(String userMessage){
        JSONArray message = new JSONArray();
        String pt = prompt().toString();
        if (StringUtils.isNotEmpty(pt)) {
            message.add(generateMessage("system", prompt().toString()));
        }
        message.add(generateMessage("user", userMessage));
        JSONObject react = new JSONObject();
        for (int i = 0; i < 10; i++) {
            react = Utils.RequestR1React(message, null, false);
            message.add(generateMessage("assistant", react.getString("message")));
            if (react.containsKey("action") && StringUtils.isNotEmpty(react.getString("action"))) {
                JSONArray actions = react.getJSONArray("action");
                for (int i1 = 0; i1 < actions.size(); i1++) {
                    String call = this.toolsService.call_tool(actions.getJSONObject(i1));
                    message.add(generateMessage("user", "[观察]"+actions.getJSONObject(i1).getString("name")+"调用结果:"+call));
                }
            } else {
                break;
            }
        }



        return react.toJSONString();

    }


    private JSONObject generateMessage(String role, String msg) {
        JSONObject message = new JSONObject();
        message.put("role", role);
        message.put("content", msg);
        return message;

    }

    private StringBuffer prompt() {
        StringBuffer content = new StringBuffer();
        content.append("你是个天气预报专家，可以使用给定的工具进行天气情况分析。");
        content.append("可以使用的工具信息:").append(toolsService.generateToolsConfig()).append("。");
        content.append("注意：1、如果需要调用工具时，返回[推理]和[行动],不要给出[答案]，等待用户输入观察结果。当不需要调用工具时,返回[推理]和[答案]，请不要修改输入参数，如果无法解析出工具入参请以[答案]如实返回提示信息。");

        content.append("请严格按REACT模式执行：\n" +
                "            [推理] 分析问题过程,内容使用<reason>和</reason>包裹。\n" +
                "            [行动] 需要调用的工具（JSON数组格式）。工具名称key是name，工具参数key是input。内容使用<action>和</action>包裹。\n" +
                "            [观察] 用户反馈调用工具结果。\n" +
                "            [答案] 最终结论,内容使用<answer>和</answer>包裹。");
        return content;
    }

}
